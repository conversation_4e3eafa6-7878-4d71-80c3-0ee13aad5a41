###### build stage ###########
FROM maven:3-amazoncorretto-17 AS build
WORKDIR /app
COPY pom.xml .
RUN mkdir -p /root/.m2
COPY ./settings.xml /root/.m2/
RUN mvn -s /root/.m2/settings.xml dependency:resolve
COPY src ./src
RUN mvn -s /root/.m2/settings.xml package -DskipTests

##### run stage #############
FROM amazoncorretto:17.0.14-alpine3.21
WORKDIR /app
COPY --from=build /app/target/by-mas-*.jar by-mas.jar
EXPOSE 8080
CMD ["java", "-jar", "by-mas.jar"]