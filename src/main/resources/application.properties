spring.profiles.active=${ENVIRONMENT:dev}
server.port=${SERVER_PORT:8080}
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/docs
endpoints.info.enabled=true
info.app.version=@project.version@
management.endpoints.web.exposure.include=health,info,beans
management.info.env.enabled=true
spring.jackson.deserialization.fail-on-unknown-properties=true
# Hibernate ddl auto (create, create-drop, validate, update)
spring.jpa.hibernate.ddl-auto=validate
server.error.include-message=always
#Discord
discord.webhook=https://discord.com/api/webhooks/
discord.id=someid
discord.token=sometoken
kinesis.enabled=${KINESIS_ENABLED:true}
movix.enabled=${MOVIX_ENABLED:true}
fresco.host=${FRESCO_HOST:https://fresco.svc.internal.allaria.dev}
grotesco.host=${GROTESCO_HOST:https://grotesco.svc.internal.allaria.dev}
allaria-market-data.host=${MARKET_DATA_HOST:https://market-data.svc.internal.allaria.dev}
pitbull.host=${PITBULL_HOST:https://pitbull-int.allariamas.dev}
user-account-mgmt.host=${USER_ACCOUNT_MGMT_HOST:https://user-account-mgmt-int.allariamas.dev}
admin.token=dommyvalue
authn.host=${AUTHN_HOST:https://authn.svc.internal.allaria.dev}
env=${ENVIRONMENT:dev}
key-cloak-url=${KEY_CLOAK_HOST:https://keycloak.svc.internal.allaria.dev/realms/main/protocol/openid-connect/certs}
by-mas.maximum-pool-size=${BYMAS_MAX_POOL_SIZE:10}
allaria-int.maximum-pool-size=${ALLARIA_INT_MAX_POOL_SIZE:10}
by-mas.conn-time-out=${BYMAS_CONN_TIME_OUT:30000}
allaria-int.conn-time-out=${ALLARIA_INT_CONN_TIME_OUT:30000}
