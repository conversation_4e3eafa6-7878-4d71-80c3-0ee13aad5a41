import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Token de autorización
TOKEN = "ADMIN_TOKEN"  # <-- reemplaz<PERSON> con tu token real

# Lista de IDs (ejemplo con 2500; reemplazá por los reales)
account_ids = []  # <-- tu lista completa acá

# Endpoint y headers
URL = "https://api.allaria.cloud/by-mas/positions/sync-from-allaria-integrations"
HEADERS = {
    "X-Client-Origin": "ALLARIA_MAS",
    "X-Client-Target": "ALLARIA",
    "Authorization": f"Bearer {TOKEN}"
}
FROM_DATE = "2025-07-07"


def post_request(account_id):
    params = {
        "account-id": account_id,
        "from-date": FROM_DATE
    }
    try:
        response = requests.post(URL, headers=HEADERS, params=params)
        return (account_id, response.status_code, response.text if response.status_code != 200 else "")
    except Exception as e:
        return (account_id, "ERROR", str(e))


# Ejecutar en bloques de 10
for i in range(0, len(account_ids), 10):
    batch = account_ids[i:i + 10]
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(post_request, aid) for aid in batch]
        for future in as_completed(futures):
            account_id, status, msg = future.result()
            print(f"[{account_id}] Status: {status}")
            if status != 204:
                print(f"  Error: {msg}")
    time.sleep(20)  # esperar 5 segundos entre cada bloque
