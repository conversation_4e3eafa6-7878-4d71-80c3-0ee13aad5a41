package ar.com.daruma.bymas.utils.discord;

import ar.com.daruma.bymas.configuration.env.EnvConfig;
import ar.com.daruma.citadel.model.DiscordMessage;
import ar.com.daruma.citadel.model.DiscordNotification;
import ar.com.daruma.citadel.service.DiscordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class DiscordSender {

  @Autowired private DiscordService discordService;
  @Autowired private DiscordNotification discordNotification;
  @Autowired private EnvConfig envConfig;

  public void notifyDiscord(String message) {
    try {
      String formattedMessage = "```" + appendEnv() + message + "```";
      discordNotification.setMessage(new DiscordMessage(formattedMessage));
      discordService.notify(discordNotification);
    } catch (Exception e) {
      log.error("Error sending discord notification: {}", e.getMessage());
    }
  }

  private String appendEnv() {
    return envConfig.isProd() ? "[PROD] " : "[DEV] ";
  }
}
