package ar.com.daruma.bymas.utils.time.application;

import ar.com.daruma.bymas.client.domain.Day;
import ar.com.daruma.bymas.client.domain.service.PitbullService;
import ar.com.daruma.bymas.utils.time.domain.NonWorkingDay;
import java.time.LocalDate;
import java.util.List;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WorkingDayService {

  private final PitbullService pitbullClient;

  public boolean isWorkingDay(LocalDate date) {
    List<LocalDate> holidays = pitbullClient.findHolidays();
    return new Day(date).isWorkingDay(holidays);
  }

  public void validateIsWorkingDay(LocalDate date) {
    if (!isWorkingDay(date)) {
      throw new NonWorkingDay(String.format("Date: [%s] is not a working day", date));
    }
  }

  /** Allows obtaining the working day that is (pastDays) days before the initial date (date). */
  public LocalDate lastAvailableDay(LocalDate date, int pastDays) {
    return calculateDay(date.minusDays(1), pastDays, d -> d.minusDays(1));
  }

  public LocalDate nextAvailableDay(LocalDate date, int nextDays) {
    return calculateDay(date.plusDays(1), nextDays, d -> d.plusDays(1));
  }

  private LocalDate calculateDay(LocalDate date, int days, UnaryOperator<LocalDate> adjuster) {
    List<LocalDate> holidays = pitbullClient.findHolidays();
    return Stream.iterate(date, adjuster)
        .filter(d -> new Day(d).isWorkingDay(holidays))
        .skip(days - 1)
        .findFirst()
        .orElseThrow(() -> new RuntimeException("No available day found"));
  }
}
