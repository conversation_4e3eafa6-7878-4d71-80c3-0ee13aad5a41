package ar.com.daruma.bymas.utils.objectMapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import javax.annotation.PostConstruct;
import org.springframework.stereotype.Component;

@Component
public class ObjectMapperUtils {

  private final ObjectMapper injectedObjectMapper;
  public static ObjectMapper INSTANCE;

  public ObjectMapperUtils(ObjectMapper injectedObjectMapper) {
    this.injectedObjectMapper = injectedObjectMapper;
  }

  @PostConstruct
  public void init() {
    INSTANCE = injectedObjectMapper;
  }
}
