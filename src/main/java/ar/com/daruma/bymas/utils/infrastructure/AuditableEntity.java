package ar.com.daruma.bymas.utils.infrastructure;

import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@MappedSuperclass
@Getter
@Setter
public abstract class AuditableEntity {

  @Column(name = "created_at", nullable = false, updatable = false)
  private LocalDateTime createdAt;

  @Column(name = "updated_at", nullable = false)
  private LocalDateTime updatedAt;

  @PrePersist
  protected void onCreate() {
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
    this.updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }
}
