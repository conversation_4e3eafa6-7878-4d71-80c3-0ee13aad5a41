package ar.com.daruma.bymas.utils.time.application;

import ar.com.daruma.bymas.utils.time.domain.NotMarketTimeError;
import java.time.LocalDateTime;
import java.time.LocalTime;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MarketTimeService {

  private static final LocalTime START_OF_MARKET_TIME = LocalTime.of(9, 0);
  private static final LocalTime END_OF_MARKET_TIME = LocalTime.of(17, 30);
  private static final Logger logger = LogManager.getLogger(MarketTimeService.class);
  @Autowired private WorkingDayService workingDayService;

  public void execute() {
    logger.info("Validating market time");
    workingDayService.validateIsWorkingDay(BuenosAiresTime.nowAsLocalDate());
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    boolean isValidTime =
        now.isBefore(now.toLocalDate().atTime(END_OF_MARKET_TIME))
            && now.isAfter(now.toLocalDate().atTime(START_OF_MARKET_TIME));
    if (!isValidTime)
      throw new NotMarketTimeError(
          String.format("Time: [%s] is not a market time", now.toLocalTime()));
  }
}
