package ar.com.daruma.bymas.utils.time.application;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

public final class BuenosAiresTime {

  public static final ZoneId BUENOS_AIRES_ZONE = ZoneId.of("America/Argentina/Buenos_Aires");

  public static ZonedDateTime nowAsZonedDateTime() {
    return ZonedDateTime.now(BUENOS_AIRES_ZONE);
  }

  public static LocalDate nowAsLocalDate() {
    return nowAsZonedDateTime().toLocalDate();
  }

  public static LocalDateTime nowAsLocalDateTime() {
    return nowAsZonedDateTime().toLocalDateTime();
  }
}
