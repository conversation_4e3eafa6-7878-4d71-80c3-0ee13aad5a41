package ar.com.daruma.bymas.portfolioAccount.application.sync;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaMasCompany;

import ar.com.daruma.bymas.allariaMas.accounts.application.find.FindAllariaMasAccountsInBatches;
import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.entities.AccountEntity;
import ar.com.daruma.bymas.allariaMas.settlement.application.find.FindMoneyMarketSettlementForPersonType;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities.Settlement;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePesoPortfolioPositionByAllariaMasAccount;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SyncPortfolioAccountsFromAllariaMas extends DiscordSender {

  private static final int BATCH_SIZE = 5000;
  private final FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;
  private final UpdatePesoPortfolioPositionByAllariaMasAccount
      updatePesoPortfolioPositionByAllariaMasAccount;
  private final FindAllariaMasAccountsInBatches findAllariaMasAccountsInBatches;
  private final FindMoneyMarketSettlementForPersonType findMoneyMarketSettlementForPersonType;
  private final PortfolioAccountService portfolioAccountService;

  @Transactional(transactionManager = "allariaMasTransactionManager", readOnly = true)
  public void sync(List<Integer> accountIds) {
    Settlement physicalSettlement =
        findMoneyMarketSettlementForPersonType.find(PersonType.PHYSICAL);
    Settlement juridicalSettlement =
        findMoneyMarketSettlementForPersonType.find(PersonType.JURIDICAL);
    log.info("Starting to sync portfolio accounts from Allaria Mas to ByMas");
    Pageable pageable = PageRequest.of(0, BATCH_SIZE);
    Page<AccountEntity> accountPage;
    do {
      accountPage = findAllariaMasAccountsInBatches.find(accountIds, pageable);
      processAllariaMasAccounts(accountPage, physicalSettlement, juridicalSettlement);
      pageable = accountPage.nextPageable();
    } while (accountPage.hasNext());
  }

  @Transactional(
      transactionManager = "byMasTransactionManager",
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = Exception.class)
  public void processAllariaMasAccounts(
      Page<AccountEntity> accounts, Settlement physicalSettlement, Settlement juridicalSettlement) {
    List<Integer> alreadyCreatedAccounts =
        portfolioAccountService.findByAccountNumberInAndCompany(
            accounts.map(AccountEntity::getId).stream().toList(), allariaMasCompany);
    List<AccountEntity> accountsToCreate =
        accounts.stream()
            .filter(account -> !alreadyCreatedAccounts.contains(account.getId()))
            .toList();
    for (AccountEntity account : accountsToCreate) {
      log.info("Processing allaria mas account: {}", account.getId());
      PortfolioAccount portfolioAccount =
          findsertPortfolioAccountByAccountNumber.findsert(
              account.getId(), account.getName(), account.isPhysical(), allariaMasCompany);

      updatePesoPortfolioPositionByAllariaMasAccount.update(
          account,
          portfolioAccount,
          getSettlementByType(account, physicalSettlement, juridicalSettlement));
      log.info(
          "Portfolio account with id {} successfully updated for allaria mas account: {}",
          portfolioAccount.getId(),
          account.getId());
    }
  }

  private Optional<Settlement> getSettlementByType(
      AccountEntity account, Settlement physicalSettlement, Settlement juridicalSettlement) {
    if (account.isPhysical()) {
      return Optional.of(physicalSettlement);
    } else {
      return Optional.of(juridicalSettlement);
    }
  }
}
