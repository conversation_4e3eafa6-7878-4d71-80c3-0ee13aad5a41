package ar.com.daruma.bymas.portfolioAccount.presentation.controller;

import ar.com.daruma.bymas.portfolioAccount.application.sync.SyncPortfolioAccountsFromAllariaMas;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/positions")
@Validated
@RequiredArgsConstructor
public class AccountsController {

  private final SyncPortfolioAccountsFromAllariaMas syncPortfolioAccountsFromAllariaMas;

  @BymasAuthorization()
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PostMapping("/sync-from-allaria-mas")
  public void syncAccounts(@RequestBody(required = false) List<Integer> accountIds) {
    if (accountIds == null) {
      accountIds = List.of();
    }
    syncPortfolioAccountsFromAllariaMas.sync(accountIds);
  }
}
