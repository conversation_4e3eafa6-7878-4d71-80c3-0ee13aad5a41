package ar.com.daruma.bymas.portfolioAccount.application.findsert;

import ar.com.daruma.bymas.client.domain.AllariaAccount;
import ar.com.daruma.bymas.client.domain.service.FrescoService;
import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.domain.entities.errors.PortfolioCompanyNotFoundException;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.domain.PortfolioCompanyService;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindOrInsertPortfolioAccountByAccountId {
  private final FrescoService frescoService;
  private final PortfolioCompanyService portfolioCompanyService;
  private final FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;
  private final PortfolioAccountService portfolioAccountService;
  private static final String ALLARIA_COMPANY_NAME = "Allaria";

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioAccount findOrInsert(Integer accountId) {
    log.info("Finding or creating portfolio account by account id {}", accountId);
    PortfolioCompany company =
        portfolioCompanyService
            .findFirstByName(ALLARIA_COMPANY_NAME)
            .orElseThrow(() -> new PortfolioCompanyNotFoundException(ALLARIA_COMPANY_NAME));
    Optional<PortfolioAccount> maybeAccount =
        portfolioAccountService.findByAccountNumberAndCompany(accountId, company);
    PortfolioAccount savedAccount =
        maybeAccount.orElseGet(
            () -> {
              AllariaAccount allariaAccount =
                  frescoService.getAllariaAccountById(accountId.longValue());
              return findsertPortfolioAccountByAccountNumber.findsert(
                  accountId,
                  allariaAccount.getDescription(),
                  allariaAccount.getIsPhysical(),
                  company);
            });
    log.info("New portfolio account created with id: {}", savedAccount.getId());
    return savedAccount;
  }
}
