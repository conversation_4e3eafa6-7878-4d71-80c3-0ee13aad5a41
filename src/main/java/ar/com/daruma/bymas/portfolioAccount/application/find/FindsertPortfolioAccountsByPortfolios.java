package ar.com.daruma.bymas.portfolioAccount.application.find;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class FindsertPortfolioAccountsByPortfolios {

  private final PortfolioAccountService portfolioAccountService;

  public List<PortfolioAccount> findsert(List<Portfolio> portfolios) {
    log.info("Finding or creating {} portfolio accounts by portfolios", portfolios.size());
    List<PortfolioAccount> accounts = portfolioAccountService.findsertByPortfolios(portfolios);
    log.info("Found or created {} portfolio accounts by portfolios", accounts.size());
    return accounts;
  }
}
