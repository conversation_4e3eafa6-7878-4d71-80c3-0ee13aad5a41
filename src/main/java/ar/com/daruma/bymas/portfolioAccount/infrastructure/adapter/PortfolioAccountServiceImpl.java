package ar.com.daruma.bymas.portfolioAccount.infrastructure.adapter;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;

import ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.entities.AllariaAccount;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.client.application.UserAccountMgmtClient;
import ar.com.daruma.bymas.client.domain.AllariaMasAccountResponse;
import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.repository.PortfolioAccountRepository;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class PortfolioAccountServiceImpl implements PortfolioAccountService {

  private final PortfolioAccountRepository repository;
  private final UserAccountMgmtClient userAccountMgmtClient;

  public static final PortfolioCompany allariaMasCompany =
      new PortfolioCompany(UUID.fromString(System.getenv("ALLARIA_MAS_COMPANY_UUID")), "Allaria +");

  @Override
  public PortfolioAccount save(PortfolioAccount account) {
    return repository.save(account);
  }

  @Override
  public Optional<PortfolioAccount> findById(UUID id) {
    return repository.findById(id);
  }

  @Override
  public List<PortfolioAccount> findAll() {
    return repository.findAll();
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioAccount findsertByAccountNumberAndCompany(
      Integer accountNumber, String name, Boolean isPhysical, PortfolioCompany company) {
    Optional<PortfolioAccount> maybeAccount =
        repository.findByAccountIdAndCompany(accountNumber, company);
    maybeAccount.ifPresent(
        account -> {
          account.setName(name);
          account.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
        });
    return save(
        maybeAccount.orElseGet(
            () -> new PortfolioAccount(accountNumber, name, isPhysical, company)));
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioAccount findsertAllariaMasAccountByAccountNumber(Integer accountNumber) {
    Optional<PortfolioAccount> maybeAccount =
        repository.findByAccountIdAndCompany(accountNumber, allariaMasCompany);
    maybeAccount.ifPresent(account -> account.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime()));
    return save(maybeAccount.orElseGet(() -> createNewAccount(accountNumber)));
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public Optional<PortfolioAccount> findByAccountNumberAndCompany(
      Integer accountNumber, PortfolioCompany company) {
    return repository.findByAccountIdAndCompany(accountNumber, company);
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public List<Integer> findByAccountNumberInAndCompany(
      List<Integer> list, PortfolioCompany allariaMasCompany) {
    return repository.findByAccountIdInAndCompany(list, allariaMasCompany).stream()
        .map(PortfolioAccount::getAccountId)
        .toList();
  }

  @Override
  public List<PortfolioAccount> findsertByPortfolios(List<Portfolio> portfolios) {
    Set<AllariaAccount> allariaAccounts =
        portfolios.stream().map(Portfolio::getAllariaAccount).collect(Collectors.toSet());

    Map<Integer, AllariaAccount> allariaAccountsMap =
        allariaAccounts.stream()
            .collect(Collectors.toMap(AllariaAccount::getAccountNumber, account -> account));

    List<Integer> accountNumbers =
        allariaAccounts.stream().map(AllariaAccount::getAccountNumber).toList();

    List<PortfolioAccount> existingAccounts =
        repository.findByAccountIdInAndCompany(accountNumbers, allariaCompany);

    existingAccounts.forEach(account -> updateExistingAccount(account, allariaAccountsMap));

    Set<AllariaAccount> nonExistentAccounts =
        allariaAccounts.stream()
            .filter(account -> isAccountNonExistent(account, existingAccounts))
            .collect(Collectors.toSet());

    List<PortfolioAccount> newAccounts =
        nonExistentAccounts.stream()
            .map(
                account ->
                    account.createNewPortfolioAccount(
                        true)) // TODO check if we can get this from fresco??
            .toList();

    List<PortfolioAccount> allAccounts = new ArrayList<>(existingAccounts);
    allAccounts.addAll(newAccounts);

    return repository.saveAll(allAccounts);
  }

  private void updateExistingAccount(
      PortfolioAccount account, Map<Integer, AllariaAccount> allariaAccountsMap) {
    AllariaAccount allariaAccount = allariaAccountsMap.get(account.getAccountId());
    account.setName(allariaAccount.getDenomination());
    account.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  private boolean isAccountNonExistent(
      AllariaAccount account, List<PortfolioAccount> existingAccounts) {
    return existingAccounts.stream()
        .noneMatch(
            existingAccount -> existingAccount.getAccountId().equals(account.getAccountNumber()));
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  private PortfolioAccount createNewAccount(Integer accountNumber) {
    AllariaMasAccountResponse externalAccount = userAccountMgmtClient.getAccount(accountNumber);
    return new PortfolioAccount(
        accountNumber, externalAccount.getName(), externalAccount.isPhysical(), allariaMasCompany);
  }
}
