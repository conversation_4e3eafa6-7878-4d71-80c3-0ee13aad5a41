package ar.com.daruma.bymas.portfolioAccount.application.find;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.application.find.FindPortfolioAccountRelationshipsByAccountId;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class FindRelatedAccounts {

  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;

  private final FindPortfolioAccountRelationshipsByAccountId
      findPortfolioAccountRelationshipsByAccountId;

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<PortfolioAccount> findAndReturnWithOwnAccount(
      Integer accountId, PortfolioCompany accountCompany) {
    PortfolioAccount account =
        findPortfolioAccountByAccountId.findAccountByIdAndCompany(accountId, accountCompany);
    List<PortfolioAccount> relationshipAccounts =
        findPortfolioAccountRelationshipsByAccountId.find(accountId).stream()
            .map(PortfolioAccountRelationships::getPortfolioAccount)
            .toList();
    List<PortfolioAccount> allAccounts = new ArrayList<>(relationshipAccounts);
    allAccounts.add(account);
    return allAccounts;
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<PortfolioAccountRelationships> findRelationships(Integer accountId) {
    return findPortfolioAccountRelationshipsByAccountId.find(accountId);
  }
}
