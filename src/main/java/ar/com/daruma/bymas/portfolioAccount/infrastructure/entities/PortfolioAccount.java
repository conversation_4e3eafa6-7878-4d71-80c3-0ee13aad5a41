package ar.com.daruma.bymas.portfolioAccount.infrastructure.entities;

import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.utils.infrastructure.AuditableEntity;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_accounts")
public class PortfolioAccount extends AuditableEntity {
  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "company_id", nullable = false)
  private PortfolioCompany company;

  @Column(name = "account_id", nullable = false)
  private Integer accountId;

  @Column(nullable = false)
  private String name;

  @Enumerated(EnumType.STRING)
  @Column(name = "company_type", nullable = false)
  private PersonType personType;

  public PortfolioAccount() {}

  public PortfolioAccount(
      Integer accountNumber, String name, Boolean isPhysical, PortfolioCompany company) {
    this.accountId = accountNumber;
    this.name = name;
    this.company = company;
    this.personType = isPhysical ? PersonType.PHYSICAL : PersonType.JURIDICAL;
  }

  public boolean isAllariaMasAccount() {
    return this.company.isAllariaMasCompany();
  }

  public boolean isAllariaAccount() {
    return this.company.isAllariaCompany();
  }
}
