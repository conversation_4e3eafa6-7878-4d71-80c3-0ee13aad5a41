package ar.com.daruma.bymas.portfolioAccount.application.findsert;

import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class FindsertPortfolioAccountByAccountNumber {

  private final PortfolioAccountService portfolioAccountService;

  public PortfolioAccount findsert(
      Integer accountNumber, String name, Boolean isPhysical, PortfolioCompany company) {
    log.info(
        "Finding or inserting portfolio account with accountNumber: {} and  name: {}",
        accountNumber,
        name);
    PortfolioAccount account =
        portfolioAccountService.findsertByAccountNumberAndCompany(
            accountNumber, name, isPhysical, company);
    log.info("Portfolio account with id {} found or inserted", account.getAccountId());
    return account;
  }

  public PortfolioAccount findsert(Integer accountNumber) {
    log.info("Finding or inserting portfolio account with accountNumber: {}", accountNumber);
    PortfolioAccount account =
        portfolioAccountService.findsertAllariaMasAccountByAccountNumber(accountNumber);
    log.info("Portfolio account with id {} found or inserted", account.getAccountId());
    return account;
  }
}
