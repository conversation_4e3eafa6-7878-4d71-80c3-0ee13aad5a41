package ar.com.daruma.bymas.portfolioAccount.infrastructure.repository;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioAccountRepository extends JpaRepository<PortfolioAccount, UUID> {

  @EntityGraph(attributePaths = {"company"})
  Optional<PortfolioAccount> findByAccountIdAndCompany(
      Integer accountNumber, PortfolioCompany company);

  List<PortfolioAccount> findByAccountIdInAndCompany(
      List<Integer> list, PortfolioCompany allariaMasCompany);
}
