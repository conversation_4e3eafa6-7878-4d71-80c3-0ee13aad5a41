package ar.com.daruma.bymas.portfolioAccount.application.find;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;
import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaMasCompany;

import ar.com.daruma.bymas.client.application.FrescoClient;
import ar.com.daruma.bymas.client.domain.AllariaAccount;
import ar.com.daruma.bymas.portfolioAccount.domain.PortfolioAccountService;
import ar.com.daruma.bymas.portfolioAccount.domain.entities.errors.PortfolioAccountNotFoundException;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioAccountByAccountId {

  private final PortfolioAccountService portfolioAccountService;
  private final FrescoClient frescoClient;

  public PortfolioAccount findAllariaMasAccount(Integer accountId) {
    return findAccountByIdAndCompany(accountId, allariaMasCompany);
  }

  public PortfolioAccount findAllariaAccount(Integer accountId) {
    return findAccountByIdAndCompany(accountId, allariaCompany);
  }

  public PortfolioAccount findAccountByIdAndCompany(Integer accountId, PortfolioCompany company) {
    log.info(
        "Finding portfolio account by account id {} and company {}", accountId, company.getName());
    PortfolioAccount account =
        portfolioAccountService
            .findByAccountNumberAndCompany(accountId, company)
            .orElseGet(
                () -> {
                  if (company.isAllariaCompany()) {
                    return findsertAccountFromFresco(accountId, company);
                  }
                  throw new PortfolioAccountNotFoundException(accountId);
                });
    log.info("Portfolio account found with id: {}", account.getId());
    return account;
  }

  private PortfolioAccount findsertAccountFromFresco(Integer accountId, PortfolioCompany company) {
    try {
      AllariaAccount allariaAccount = frescoClient.getAllariaAccountById(accountId.longValue());
      log.info("Account found in Fresco: {}", allariaAccount);
      PortfolioAccount portfolioAccount =
          new PortfolioAccount(
              (int) allariaAccount.getAccountNumber(),
              allariaAccount.getDescription(),
              allariaAccount.getIsPhysical(),
              company);
      portfolioAccountService.save(portfolioAccount);
      log.info("Portfolio account created or updated from Fresco: {}", portfolioAccount);
      return portfolioAccount;
    } catch (Exception e) {
      log.error("Error while fetching account from Fresco for accountId: {}", accountId, e);
      throw new PortfolioAccountNotFoundException(accountId);
    }
  }
}
