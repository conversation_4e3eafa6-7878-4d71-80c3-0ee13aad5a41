package ar.com.daruma.bymas.portfolioAccount.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PortfolioAccountService {
  PortfolioAccount save(PortfolioAccount account);

  Optional<PortfolioAccount> findById(UUID id);

  List<PortfolioAccount> findAll();

  PortfolioAccount findsertByAccountNumberAndCompany(
      Integer accountNumber, String name, Boolean isPhysical, PortfolioCompany company);

  PortfolioAccount findsertAllariaMasAccountByAccountNumber(Integer accountNumber);

  Optional<PortfolioAccount> findByAccountNumberAndCompany(
      Integer accountNumber, PortfolioCompany company);

  List<Integer> findByAccountNumberInAndCompany(
      List<Integer> list, PortfolioCompany allariaMasCompany);

  List<PortfolioAccount> findsertByPortfolios(List<Portfolio> portfolios);
}
