package ar.com.daruma.bymas.portfolioQuotation.infrastructure.repository;

import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioQuotationRepository extends JpaRepository<PortfolioQuotation, UUID> {

  @Query(
      value =
          "Select * FROM portfolio_quotations WHERE portfolio_asset_id = :portfolioAssetId AND"
              + " quotation_type = CAST(:quotationType as quotation_type) AND"
              + " kind = CAST(:kind as portfolio_quotation_kind) ORDER BY processed_at DESC LIMIT 1",
      nativeQuery = true)
  Optional<PortfolioQuotation>
      findFirstByPortfolioAssetIdAndQuotationTypeAndKindOrderByProcessedAtDesc(
          @Param("portfolioAssetId") UUID portfolioAssetId,
          @Param("quotationType") String quotationType,
          @Param("kind") String kind);

  @Query(
      value =
          "Select * FROM portfolio_quotations WHERE portfolio_asset_id = :portfolioAssetId AND"
              + " quotation_type = CAST(:quotationType AS quotation_type) AND"
              + " kind = CAST(:kind as portfolio_quotation_kind) AND"
              + " processed_at = :processedAt",
      nativeQuery = true)
  Optional<PortfolioQuotation> findByPortfolioAssetIdAndQuotationTypeAndProcessedAtAndKind(
      @Param("portfolioAssetId") UUID portfolioAssetId,
      @Param("quotationType") String quotationType,
      @Param("processedAt") LocalDateTime processedAt,
      @Param("kind") String kind);

  @Query(
      value =
          "SELECT * FROM portfolio_quotations WHERE portfolio_asset_id IN :portfolioAssetIds AND"
              + " processed_at IN :processedAt AND"
              + " kind = CAST(:kind AS portfolio_quotation_kind)",
      nativeQuery = true)
  Set<PortfolioQuotation> findByPortfolioAssetIdInAndProcessedAtInAndKind(
      @Param("portfolioAssetIds") Set<UUID> portfolioAssetIds,
      @Param("processedAt") Set<LocalDateTime> processedAt,
      @Param("kind") String kind);

  @Query(
      value =
          "SELECT * FROM portfolio_quotations WHERE portfolio_asset_id IN :portfolioAssetIds AND"
              + " quotation_type = CAST(:quotationType AS quotation_type)",
      nativeQuery = true)
  List<PortfolioQuotation> findByPortfolioAssetIdsAndQuotationType(
      @Param("portfolioAssetIds") List<UUID> portfolioAssetIds,
      @Param("quotationType") QuotationType quotationType);
}
