package ar.com.daruma.bymas.portfolioQuotation.application.create;

import static ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetUtils.categories.monedas;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;

import ar.com.daruma.bymas.client.allariaMarketData.AllariaMarketDataClient;
import ar.com.daruma.bymas.client.allariaMarketData.entities.DollarRate;
import ar.com.daruma.bymas.client.allariaMarketData.entities.DollarResponse;
import ar.com.daruma.bymas.portfolioAsset.application.findsert.FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CreateDollarPortfolioQuotations {

  @Autowired private PortfolioQuotationService portfolioQuotationService;
  @Autowired private AllariaMarketDataClient allariaMarketDataClient;

  @Autowired
  private FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
      findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;

  private static final Logger logger = LogManager.getLogger(CreateDollarPortfolioQuotations.class);

  @Transactional(
      transactionManager = "byMasTransactionManager",
      propagation = Propagation.REQUIRES_NEW,
      rollbackFor = Exception.class)
  public void create() {
    logger.info("Creating current dollar portfolio quotations");
    DollarResponse dollarRates = allariaMarketDataClient.getDollarRates();
    DollarRate usdMepT0 = dollarRates.getUsdMepT0();
    DollarRate usdOfficialBna = dollarRates.getUsdOfficialBna();
    PortfolioAsset usdMepAsset =
        findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
            .findsertAsCurrency(
                PortfolioQuotationCurrencyUtils.dolarMepName,
                monedas,
                PortfolioQuotationCurrencyUtils.dolarMepSubCategory,
                PortfolioQuotationCurrencyUtils.dolarMepCode,
                PortfolioQuotationCurrencyUtils.dolarMepAbbreviation,
                PortfolioQuotationCurrencyUtils.dolarMepCurrency)
            .getAsset();
    PortfolioAsset usdOfficialBnaAsset =
        findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
            .findsertAsCurrency(
                PortfolioQuotationCurrencyUtils.dolarOfficialBnaName,
                monedas,
                PortfolioQuotationCurrencyUtils.dolarOfficialBnaSubCategory,
                PortfolioQuotationCurrencyUtils.dolarOfficialBnaCode,
                PortfolioQuotationCurrencyUtils.dolarOfficialBnaAbbreviation,
                PortfolioQuotationCurrencyUtils.dolarOfficialBnaCurrency)
            .getAsset();
    logger.info("Saving dollar rates");
    saveDollarRate(usdMepT0, usdMepAsset);
    saveDollarRate(usdOfficialBna, usdOfficialBnaAsset);
    logger.info("Current dollar portfolio quotations have been successfully created");
  }

  private void saveDollarRate(DollarRate dollarRate, PortfolioAsset portfolioAsset) {
    logger.info("Saving dollar rate for asset: {}", portfolioAsset.getName());
    portfolioQuotationService.save(
        portfolioAsset,
        QuotationType.BUY,
        dollarRate.getUpdateAt(),
        dollarRate.getPurchasePrice(),
        MARKET_DATA);
    portfolioQuotationService.save(
        portfolioAsset,
        QuotationType.SELL,
        dollarRate.getUpdateAt(),
        dollarRate.getSellingPrice(),
        MARKET_DATA);
    logger.info("Dollar rate for asset: {} has been successfully saved", portfolioAsset.getName());
  }
}
