package ar.com.daruma.bymas.portfolioQuotation.domain.entities.errors;

import ar.com.daruma.citadel.exceptions.NotFoundException;

public class NoPortfolioQuotationFoundForAsset extends NotFoundException {
  public NoPortfolioQuotationFoundForAsset(
      String assetName, String assetAbbreviation, String currency) {
    super(
        String.format(
            "No portfolio quotation found for asset with name: [%s], abbreviation: [%s] and currency: [%s]",
            assetName, assetAbbreviation, currency));
  }
}
