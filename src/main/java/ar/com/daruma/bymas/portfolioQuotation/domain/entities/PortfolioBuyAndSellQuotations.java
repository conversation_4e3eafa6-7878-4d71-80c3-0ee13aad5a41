package ar.com.daruma.bymas.portfolioQuotation.domain.entities;

import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.util.List;

public record PortfolioBuyAndSellQuotations(
    PortfolioQuotation buyQuotation, PortfolioQuotation sellQuotation) {

  public List<PortfolioQuotation> getQuotations() {
    return List.of(buyQuotation, sellQuotation);
  }
}
