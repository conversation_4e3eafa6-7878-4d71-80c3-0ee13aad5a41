package ar.com.daruma.bymas.portfolioQuotation.domain;

public class PortfolioQuotationCurrencyUtils {

  // TODO : cambiar estos dos nombres en bymas por Dolar MEP
  public static final String dolarMepName = "Dólar MEP";
  public static final String dolarMepSubCategory = "Monedas";

  public static final String dolarMepCode = "USD-MEP";

  public static final String dolarMepAbbreviation = "USD-M";

  public static final String dolarMepCurrency = "USD-M";
  public static final String pesoName = "Peso";
  public static final String dolarCableName = "Dólar Cable";

  public static String dolarOfficialBnaName = "Dólar";
  public static String dolarOfficialBnaSubCategory = "Monedas";
  public static String dolarOfficialBnaCode = "USD-I";
  public static String dolarOfficialBnaAbbreviation = "USD-I";
  public static String dolarOfficialBnaCurrency = "USD-I";

  public static String pesosBnaName = "Pesos BNA";
}
