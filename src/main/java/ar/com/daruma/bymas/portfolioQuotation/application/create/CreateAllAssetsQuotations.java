package ar.com.daruma.bymas.portfolioQuotation.application.create;

import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreateAllAssetsQuotations {
  private static final Logger logger = LogManager.getLogger(CreateAllAssetsQuotations.class);

  private final CreateAssetsQuotationsWorker createAssetsQuotationsWorker;

  @Async("asyncExecutor")
  public void create(String abbreviation) {
    Optional.ofNullable(abbreviation)
        .ifPresentOrElse(
            createAssetsQuotationsWorker::createForUniqueAsset,
            createAssetsQuotationsWorker::createForAllAssets);
  }
}
