package ar.com.daruma.bymas.portfolioQuotation.application.create;

import ar.com.daruma.bymas.client.allariaMarketData.AllariaMarketDataClient;
import ar.com.daruma.bymas.client.allariaMarketData.entities.MarketDataGeneralQuotation;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import ar.com.daruma.bymas.portfolioQuotation.domain.utils.AssetQuotationUtils;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateAssetQuotation {

  private final AllariaMarketDataClient allariaMarketDataClient;
  private final AssetQuotationUtils assetQuotationUtils;
  private final CreateAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving
      createAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving;
  private final PortfolioQuotationService portfolioQuotationService;

  @Transactional(transactionManager = "byMasTransactionManager")
  public void create(PortfolioAsset portfolioAsset) {
    MarketDataGeneralQuotation assetData;
    if (portfolioAsset.isFund()) {
      String marketDataAssetName = assetQuotationUtils.mapFundName(portfolioAsset.getName());
      assetData = allariaMarketDataClient.getInvestmentFundByName(marketDataAssetName);
    } else {
      assetData = allariaMarketDataClient.getAssetByAbbreviation(portfolioAsset.getAbbreviation());
    }
    BigDecimal buyPrice = assetData.getBuyPrice();
    BigDecimal salePrice = assetData.getSalePrice();
    LocalDateTime retrievedAtTimestamp = assetData.getRetrievedAtTimestamp();
    List<PortfolioQuotation> quotations =
        createAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving.create(
            retrievedAtTimestamp, buyPrice, salePrice, portfolioAsset);
    portfolioQuotationService.saveAll(quotations);
  }
}
