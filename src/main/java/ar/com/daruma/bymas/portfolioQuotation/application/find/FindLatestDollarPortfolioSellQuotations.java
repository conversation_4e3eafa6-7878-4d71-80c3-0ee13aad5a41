package ar.com.daruma.bymas.portfolioQuotation.application.find;

import static ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetUtils.categories.monedas;

import ar.com.daruma.bymas.portfolioAsset.application.findsert.FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;
import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioCurrencyAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindLatestDollarPortfolioSellQuotations {

  @Autowired
  private FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
      findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;

  @Autowired private FindLatestPortfolioQuotation findLatestPortfolioQuotation;

  private static final Logger logger =
      LogManager.getLogger(FindLatestDollarPortfolioSellQuotations.class);

  public List<PortfolioCurrencyQuotation> find() {
    logger.info("Finding latest dollar portfolio quotations");
    PortfolioCurrencyAsset usdMepCurrencyAsset =
        findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency.findsertAsCurrency(
            PortfolioQuotationCurrencyUtils.dolarMepName,
            monedas,
            PortfolioQuotationCurrencyUtils.dolarMepSubCategory,
            PortfolioQuotationCurrencyUtils.dolarMepCode,
            PortfolioQuotationCurrencyUtils.dolarMepAbbreviation,
            PortfolioQuotationCurrencyUtils.dolarMepCurrency);
    Optional<PortfolioCurrencyQuotation> mepCurrencyQuotation =
        findLatestPortfolioQuotation
            .findByAssetAndQuotationType(usdMepCurrencyAsset.getAsset(), QuotationType.SELL)
            .map(
                quotation ->
                    new PortfolioCurrencyQuotation(quotation, usdMepCurrencyAsset.getValuation()));
    List<PortfolioCurrencyQuotation> quotations =
        Stream.of(mepCurrencyQuotation).flatMap(Optional::stream).toList();
    logger.info("Found {} latest dollar portfolio quotations", quotations.size());
    return quotations;
  }
}
