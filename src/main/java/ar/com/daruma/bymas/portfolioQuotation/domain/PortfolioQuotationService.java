package ar.com.daruma.bymas.portfolioQuotation.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import org.springframework.stereotype.Service;

@Service
public interface PortfolioQuotationService {

  PortfolioQuotation save(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime processedAt,
      BigDecimal price,
      QuotationKind kind);

  Optional<PortfolioQuotation> findLatestByPortfolioAssetIdAndQuotationTypeAndKind(
      UUID portfolioAssetId, QuotationType quotationType, QuotationKind kind);

  PortfolioQuotation findsert(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime date,
      BigDecimal price,
      QuotationKind quotationKind);

  PortfolioQuotation findsertWithoutSaving(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime date,
      BigDecimal price,
      QuotationKind quotationKind);

  void saveAll(List<PortfolioQuotation> portfolioQuotations);

  Set<PortfolioQuotation> getExistingQuotationsByKeys(
      Set<PortfolioQuotation.PortfolioQuotationKey> portfolioQuotationKeys, QuotationKind kind);

  void findsertByAssetAndPortfolios(Map<PortfolioAsset, List<Portfolio>> assetToPortfoliosMap);

  List<PortfolioQuotation> findLatestByPortfolioAssetsAndQuotationType(
      List<PortfolioAsset> portfolioAssets, QuotationType quotationType);
}
