package ar.com.daruma.bymas.portfolioQuotation.presentation.controller;

import ar.com.daruma.bymas.portfolioQuotation.application.create.CreateAllAssetsQuotations;
import ar.com.daruma.bymas.portfolioQuotation.application.create.CreateDollarPortfolioQuotations;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/quotations")
@Validated
public class PortfolioQuotationsController {

  @Autowired CreateDollarPortfolioQuotations createDollarPortfolioQuotations;
  @Autowired CreateAllAssetsQuotations createAllAssetsQuotations;

  @BymasAuthorization
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PostMapping("/dollar")
  public void syncDollarQuotations() {
    createDollarPortfolioQuotations.create();
  }

  @BymasAuthorization
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PostMapping("/assets")
  public void syncAssetsQuotations(
      @RequestParam(name = "abbreviation", required = false) String abbreviation) {
    createAllAssetsQuotations.create(abbreviation);
  }
}
