package ar.com.daruma.bymas.portfolioQuotation.application.findsert;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindsertMultipleAssetQuotationsFromAllariaIntegrations {

  private final PortfolioQuotationService portfolioQuotationService;

  public void findsert(Map<PortfolioAsset, List<Portfolio>> assetToPortfoliosMap) {
    Map<PortfolioAsset, List<Portfolio>> filteredMap =
        assetToPortfoliosMap.entrySet().stream()
            .filter(
                entry ->
                    !entry.getKey().isDollarMepAsset()
                        && entry.getValue().stream()
                            .anyMatch(
                                portfolio -> portfolio.maybeInvestmentQuotation().isPresent()))
            .collect(java.util.stream.Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    log.info("Findserting quotations for {} assets", filteredMap.size());
    portfolioQuotationService.findsertByAssetAndPortfolios(filteredMap);
    log.info("Findserted quotations for {} assets", filteredMap.size());
  }
}
