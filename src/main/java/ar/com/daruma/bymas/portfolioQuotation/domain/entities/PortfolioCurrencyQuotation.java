package ar.com.daruma.bymas.portfolioQuotation.domain.entities;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarMepName;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PortfolioCurrencyQuotation {

  private PortfolioQuotation quotation;

  private PortfolioCurrencyValuation valuation;

  public PortfolioCurrencyQuotation(
      PortfolioQuotation quotation, PortfolioCurrencyValuation valuation) {
    this.quotation = quotation;
    this.valuation = valuation;
  }

  public Boolean isDolarMep() {
    return this.valuation.getName().equals(dolarMepName);
  }

  public boolean isSell() {
    return this.quotation.getQuotationType().equals(QuotationType.SELL);
  }
}
