package ar.com.daruma.bymas.portfolioQuotation.application.create;

import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType.BUY;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType.SELL;

import ar.com.daruma.bymas.client.allariaMarketData.AllariaMarketDataClient;
import ar.com.daruma.bymas.client.allariaMarketData.entities.MarketDataGeneralQuotation;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetsByAbbreviations;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetsByNames;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CreateAssetsQuotationsWorker extends DiscordSender {

  private final AllariaMarketDataClient allariaMarketDataClient;

  private final FindPortfolioAssetsByNames findPortfolioAssetsByNames;

  private final FindPortfolioAssetsByAbbreviations findPortfolioAssetsByAbbreviations;

  private final CreateAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving
      createAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving;

  private final PortfolioQuotationService portfolioQuotationService;

  @Transactional(transactionManager = "byMasTransactionManager")
  public void createForUniqueAsset(String abbreviation) {
    log.info("Creating quotation for asset with abbreviation: {}", abbreviation);
    MarketDataGeneralQuotation assetData =
        allariaMarketDataClient.getAssetByAbbreviation(abbreviation);
    Map<String, List<PortfolioAsset>> assetsByAbbreviationOrName;
    List<PortfolioQuotation> quotationsToCreate = new ArrayList<>();
    if (assetData.isFoundByAbbreviation()) {
      assetsByAbbreviationOrName = findAssetsByAbbreviationOrName(List.of(assetData), List.of());
    } else {
      assetsByAbbreviationOrName = findAssetsByAbbreviationOrName(List.of(), List.of(assetData));
    }
    processGeneralQuotationData(
        assetsByAbbreviationOrName, assetData, Set.of(), quotationsToCreate);
    portfolioQuotationService.saveAll(quotationsToCreate);
    notifyDiscord(
        String.format(
            "Current asset portfolio quotation for abbreviation [%s] has been successfully created",
            assetData.getAssetAbbreviation()));
  }

  private Map<String, List<PortfolioAsset>> findAssetsByAbbreviationOrName(
      List<MarketDataGeneralQuotation> quotationsFoundByAbbreviation,
      List<MarketDataGeneralQuotation> quotationsFoundByName) {
    List<PortfolioAsset> assetsByName =
        findPortfolioAssetsByNames.find(
            quotationsFoundByName.stream()
                .map(MarketDataGeneralQuotation::getAssetName)
                .distinct()
                .toList());

    List<PortfolioAsset> assetsByAbbreviation =
        findPortfolioAssetsByAbbreviations.find(
            quotationsFoundByAbbreviation.stream()
                .map(MarketDataGeneralQuotation::getAssetAbbreviation)
                .distinct()
                .toList());
    Map<String, List<PortfolioAsset>> assetsByAbbreviationOrName =
        assetsByAbbreviation.stream()
            .collect(Collectors.groupingBy(PortfolioAsset::getAbbreviation, Collectors.toList()));
    assetsByName.forEach(
        asset -> {
          String name = asset.getName();
          if (!assetsByAbbreviationOrName.containsKey(name)) {
            assetsByAbbreviationOrName.put(name, new ArrayList<>());
          }
          assetsByAbbreviationOrName.get(name).add(asset);
        });
    return assetsByAbbreviationOrName;
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void createForAllAssets() {
    log.info("Creating current assets portfolio quotations");
    List<MarketDataGeneralQuotation> allAssetsData = allariaMarketDataClient.getAllAssetsData();
    log.info("Retrieved {} assets from Allaria Market Data service", allAssetsData.size());
    List<MarketDataGeneralQuotation> quotationsFoundByAbbreviation =
        allAssetsData.stream().filter(MarketDataGeneralQuotation::isFoundByAbbreviation).toList();
    List<MarketDataGeneralQuotation> quotationsFoundByName =
        allAssetsData.stream().filter(asset -> !asset.isFoundByAbbreviation()).toList();
    Map<String, List<PortfolioAsset>> assetsByAbbreviationOrName =
        findAssetsByAbbreviationOrName(quotationsFoundByAbbreviation, quotationsFoundByName);
    Set<PortfolioQuotation.PortfolioQuotationKey> quotationsToBeFoundKeys = new HashSet<>();
    allAssetsData.forEach(
        quotation -> {
          if (assetsByAbbreviationOrName.containsKey(quotation.searchTerm())) {
            List<PortfolioAsset> assets = assetsByAbbreviationOrName.get(quotation.searchTerm());
            assets.forEach(
                asset -> {
                  PortfolioQuotation.PortfolioQuotationKey buyKey =
                      new PortfolioQuotation.PortfolioQuotationKey(
                          asset.getId(), BUY, quotation.getRetrievedAtTimestamp(), MARKET_DATA);
                  PortfolioQuotation.PortfolioQuotationKey saleKey =
                      new PortfolioQuotation.PortfolioQuotationKey(
                          asset.getId(), SELL, quotation.getRetrievedAtTimestamp(), MARKET_DATA);
                  quotationsToBeFoundKeys.add(buyKey);
                  quotationsToBeFoundKeys.add(saleKey);
                });
          }
        });
    Set<PortfolioQuotation> existingQuotations =
        portfolioQuotationService.getExistingQuotationsByKeys(quotationsToBeFoundKeys, MARKET_DATA);
    Set<PortfolioQuotation.PortfolioQuotationKey> existingQuotationsByKey =
        existingQuotations.stream()
            .map(PortfolioQuotation::getPortfolioQuotationKey)
            .collect(Collectors.toSet());
    allAssetsData =
        allAssetsData.stream()
            .filter(data -> assetsByAbbreviationOrName.containsKey(data.searchTerm()))
            .toList();
    List<PortfolioQuotation> quotationsToCreate = new ArrayList<>();
    allAssetsData.forEach(
        assetData -> {
          try {
            processGeneralQuotationData(
                assetsByAbbreviationOrName, assetData, existingQuotationsByKey, quotationsToCreate);
          } catch (Exception e) {
            log.error(
                "Error processing general quotation data for asset {}: {}",
                assetData.getAssetAbbreviation(),
                e.getMessage());
          }
        });
    portfolioQuotationService.saveAll(quotationsToCreate);
    String message =
        String.format(
            "Current assets portfolio quotations have been successfully created for [%d] assets",
            allAssetsData.size());
    notifyDiscord(message);
  }

  private void processGeneralQuotationData(
      Map<String, List<PortfolioAsset>> assetsByAbbreviationOrName,
      MarketDataGeneralQuotation generalQuotationData,
      Set<PortfolioQuotation.PortfolioQuotationKey> existingQuotationsByKey,
      List<PortfolioQuotation> quotationsToCreate) {
    List<PortfolioAsset> portfolioAssets =
        assetsByAbbreviationOrName.get(generalQuotationData.searchTerm());
    BigDecimal buyPrice = generalQuotationData.getBuyPrice();
    BigDecimal salePrice = generalQuotationData.getSalePrice();
    LocalDateTime retrievedAtTimestamp = generalQuotationData.getRetrievedAtTimestamp();
    portfolioAssets.forEach(
        portfolioAsset -> {
          if (existingQuotationsByKey.contains(
              new PortfolioQuotation.PortfolioQuotationKey(
                  portfolioAsset.getId(),
                  BUY,
                  generalQuotationData.getRetrievedAtTimestamp(),
                  MARKET_DATA))) {
            return;
          }
          quotationsToCreate.addAll(
              createAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving.create(
                  retrievedAtTimestamp, buyPrice, salePrice, portfolioAsset));
        });
  }
}
