package ar.com.daruma.bymas.portfolioQuotation.application.find;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindLatestPortfolioQuotation {

  private final PortfolioQuotationService portfolioQuotationService;

  public Optional<PortfolioQuotation> findByAssetAndQuotationType(
      PortfolioAsset portfolioAsset, QuotationType quotationType) {
    log.info(
        "Finding latest portfolio quotation by portfolioAssetId {} and quotationType {}",
        portfolioAsset.getId(),
        quotationType);
    Optional<PortfolioQuotation> portfolioQuotation =
        portfolioQuotationService.findLatestByPortfolioAssetIdAndQuotationTypeAndKind(
            portfolioAsset.getId(), quotationType, portfolioAsset.getQuotationKind());
    if (portfolioQuotation.isEmpty()) {
      String message =
          String.format(
              "No portfolio quotation found for portfolioAsset [%s], quotationType [%s] and kind [%s], searching for quotations of opposite-kind [%s]",
              portfolioAsset.getName(),
              quotationType,
              portfolioAsset.getQuotationKind(),
              portfolioAsset.getQuotationKind().getOppositeKind());
      log.warn(message);
      portfolioQuotation =
          portfolioQuotationService.findLatestByPortfolioAssetIdAndQuotationTypeAndKind(
              portfolioAsset.getId(),
              quotationType,
              portfolioAsset.getQuotationKind().getOppositeKind());
    }
    log.info(finalLogMessage(portfolioQuotation, portfolioAsset.getId(), quotationType));
    return portfolioQuotation;
  }

  public List<PortfolioQuotation> findByAssetsAndQuotationType(
      List<PortfolioAsset> portfolioAssets, QuotationType quotationType) {
    log.info(
        "Finding latest portfolio quotation by {} portfolioAssets and quotationType {}",
        portfolioAssets.size(),
        quotationType);
    List<PortfolioQuotation> quotations =
        portfolioQuotationService.findLatestByPortfolioAssetsAndQuotationType(
            portfolioAssets, quotationType);
    log.info("Found {} latest portfolio quotations", quotations.size());
    return quotations;
  }

  private String finalLogMessage(
      Optional<PortfolioQuotation> portfolioQuotation,
      UUID portfolioAssetId,
      QuotationType quotationType) {
    return portfolioQuotation
        .map(
            value ->
                "Latest portfolio quotation found with id %s and price %s"
                    .formatted(value.getId().toString(), value.getPrice().toString()))
        .orElse(
            "No portfolio quotation found with portfolioAssetId %s and quotationType %s"
                .formatted(portfolioAssetId.toString(), quotationType.toString()));
  }
}
