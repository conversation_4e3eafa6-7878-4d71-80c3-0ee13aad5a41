package ar.com.daruma.bymas.portfolioQuotation.infrastructure.adapter;

import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.ALLARIA_DATA;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationService;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.repository.PortfolioQuotationRepository;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortfolioQuotationServiceImpl implements PortfolioQuotationService {

  private final PortfolioQuotationRepository repository;

  @Override
  public PortfolioQuotation save(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime processedAt,
      BigDecimal price,
      QuotationKind kind) {
    return repository.save(
        new PortfolioQuotation(portfolioAsset, price, kind, quotationType, processedAt));
  }

  @Override
  public Optional<PortfolioQuotation> findLatestByPortfolioAssetIdAndQuotationTypeAndKind(
      UUID portfolioAssetId, QuotationType quotationType, QuotationKind kind) {
    return repository.findFirstByPortfolioAssetIdAndQuotationTypeAndKindOrderByProcessedAtDesc(
        portfolioAssetId, quotationType.toString(), kind.toString());
  }

  @Override
  public PortfolioQuotation findsert(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime date,
      BigDecimal price,
      QuotationKind quotationKind) {
    return repository
        .findByPortfolioAssetIdAndQuotationTypeAndProcessedAtAndKind(
            portfolioAsset.getId(), quotationType.toString(), date, quotationKind.toString())
        .orElseGet(() -> save(portfolioAsset, quotationType, date, price, quotationKind));
  }

  @Override
  public PortfolioQuotation findsertWithoutSaving(
      PortfolioAsset portfolioAsset,
      QuotationType quotationType,
      LocalDateTime date,
      BigDecimal price,
      QuotationKind quotationKind) {
    return repository
        .findByPortfolioAssetIdAndQuotationTypeAndProcessedAtAndKind(
            portfolioAsset.getId(), quotationType.toString(), date, quotationKind.toString())
        .orElseGet(
            () ->
                new PortfolioQuotation(portfolioAsset, price, quotationKind, quotationType, date));
  }

  @Override
  public void saveAll(List<PortfolioQuotation> portfolioQuotations) {
    repository.saveAll(portfolioQuotations);
  }

  @Override
  public Set<PortfolioQuotation> getExistingQuotationsByKeys(
      Set<PortfolioQuotation.PortfolioQuotationKey> portfolioQuotationKeys, QuotationKind kind) {
    Set<UUID> portfolioAssetIds =
        portfolioQuotationKeys.stream()
            .map(PortfolioQuotation.PortfolioQuotationKey::portfolioAssetId)
            .collect(Collectors.toSet());
    Set<LocalDateTime> processedAtSet =
        portfolioQuotationKeys.stream()
            .map(PortfolioQuotation.PortfolioQuotationKey::processedAt)
            .collect(Collectors.toSet());
    Set<PortfolioQuotation> existingQuotations =
        repository.findByPortfolioAssetIdInAndProcessedAtInAndKind(
            portfolioAssetIds, processedAtSet, kind.toString());
    existingQuotations =
        existingQuotations.stream()
            .filter(
                quotation -> portfolioQuotationKeys.contains(quotation.getPortfolioQuotationKey()))
            .collect(Collectors.toSet());
    return existingQuotations;
  }

  @Override
  public void findsertByAssetAndPortfolios(
      Map<PortfolioAsset, List<Portfolio>> assetToPortfoliosMap) {
    List<PortfolioQuotation> quotationsToSave = new ArrayList<>();
    Set<PortfolioQuotation.PortfolioQuotationKey> portfolioAssetKeys = new HashSet<>();
    assetToPortfoliosMap.forEach(
        (key, value) -> {
          portfolioAssetKeys.add(
              new PortfolioQuotation.PortfolioQuotationKey(
                  key.getId(), QuotationType.BUY, value.get(0).getDate(), ALLARIA_DATA));
          portfolioAssetKeys.add(
              new PortfolioQuotation.PortfolioQuotationKey(
                  key.getId(), QuotationType.SELL, value.get(0).getDate(), ALLARIA_DATA));
        });
    Set<PortfolioQuotation> existingQuotations =
        getExistingQuotationsByKeys(portfolioAssetKeys, ALLARIA_DATA);
    Set<PortfolioQuotation.PortfolioQuotationKey> existingQuotationsByKey =
        existingQuotations.stream()
            .map(PortfolioQuotation::getPortfolioQuotationKey)
            .collect(Collectors.toSet());
    for (Map.Entry<PortfolioAsset, List<Portfolio>> entry : assetToPortfoliosMap.entrySet()) {
      PortfolioAsset portfolioAsset = entry.getKey();
      List<Portfolio> portfolios = entry.getValue();
      if (portfolios.isEmpty()) {
        continue;
      }
      Portfolio portfolio = portfolios.get(0);
      BigDecimal price = portfolio.getInvestmentQuotation();
      boolean buyExists =
          existingQuotationsByKey.contains(
              new PortfolioQuotation.PortfolioQuotationKey(
                  portfolioAsset.getId(), QuotationType.BUY, portfolio.getDate(), ALLARIA_DATA));
      if (!buyExists) {
        quotationsToSave.add(
            new PortfolioQuotation(
                portfolioAsset, price, ALLARIA_DATA, QuotationType.BUY, portfolio.getDate()));
        existingQuotationsByKey.add(
            new PortfolioQuotation.PortfolioQuotationKey(
                portfolioAsset.getId(), QuotationType.BUY, portfolio.getDate(), ALLARIA_DATA));
      }
      boolean sellExists =
          existingQuotationsByKey.contains(
              new PortfolioQuotation.PortfolioQuotationKey(
                  portfolioAsset.getId(), QuotationType.SELL, portfolio.getDate(), ALLARIA_DATA));
      if (!sellExists) {
        quotationsToSave.add(
            new PortfolioQuotation(
                portfolioAsset, price, ALLARIA_DATA, QuotationType.SELL, portfolio.getDate()));
        existingQuotationsByKey.add(
            new PortfolioQuotation.PortfolioQuotationKey(
                portfolioAsset.getId(), QuotationType.SELL, portfolio.getDate(), ALLARIA_DATA));
      }
    }
    if (!quotationsToSave.isEmpty()) {
      repository.saveAll(quotationsToSave);
    }
  }

  @Override
  public List<PortfolioQuotation> findLatestByPortfolioAssetsAndQuotationType(
      List<PortfolioAsset> portfolioAssets, QuotationType quotationType) {
    return repository.findByPortfolioAssetIdsAndQuotationType(
        portfolioAssets.stream().map(PortfolioAsset::getId).toList(), quotationType);
  }
}
