package ar.com.daruma.bymas.portfolioQuotation.domain.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public final class AssetQuotationUtils {

  private static final Map<String, String> FUND_TYPE_MAPPINGS = new HashMap<>();
  private static final Map<String, String> CLASS_MAPPINGS = new HashMap<>();

  static {
    // Initialize fund type mappings
    FUND_TYPE_MAPPINGS.put("Allaria Renta Dolar Ley 27260", "ALLARIA RENTA DOLAR LEY 27260 FCI");
    FUND_TYPE_MAPPINGS.put("FCI ALLARIA RTA MIXTA", "ALLARIA RENTA MIXTA DÓLARES FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Latam", "ALLARIA DÓLAR LATAM FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dólar Global", "ALLARIA DÓLAR GLOBAL FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dolar Global FCI", "ALLARIA DÓLAR GLOBAL FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dolar Crecimiento", "ALLARIA DÓLAR CRECIMIENTO FCI");
    FUND_TYPE_MAPPINGS.put("BALANZ CAP.RTA.FIJA", "BALANZ CAP.RTA.FIJA FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dólar Dinámico", "ALLARIA DÓLAR DINÁMICO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Unicred Dólares FCI", "ALLARIA UNICRED DÓLARES FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dólar Ahorro", "ALLARIA DOLAR AHORRO FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA DOLAR AHORRO", "ALLARIA DOLAR AHORRO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dólar Retorno Total", "ALLARIA DOLAR RETORNO TOTAL FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA DOLAR RETORNO TOTAL", "ALLARIA DOLAR RETORNO TOTAL FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA DOLAR CRECIMIENTO", "ALLARIA DÓLAR CRECIMIENTO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dólar Ahorro Plus", "ALLARIA DÓLAR AHORRO PLUS FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA DOLAR GESTION", "ALLARIA DÓLAR GESTION FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Ahorro", "ALLARIA AHORRO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Acciones", "ALLARIA ACCIONES FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Renta Mixta II", "ALLARIA RENTA MIXTA II F.C.I.");
    FUND_TYPE_MAPPINGS.put(
        "Allaria Abierto Pymes", "ALLARIA FONDO COMUN DE INVERSION ABIERTO PYMEs FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Ahorro Plus", "ALLARIA AHORRO PLUS FCI");
    FUND_TYPE_MAPPINGS.put(
        "Allaria Desarrollo",
        "ALLARIA DESARROLLO FCIA P/ EL FINANCIAMIENTO DE LA INFRAESTR. Y LA ECONOMIA REAL");
    FUND_TYPE_MAPPINGS.put("Allaria Renta Fija", "ALLARIA RENTA FIJA FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Renta Balanceada I", "ALLARIA RENTA BALANCEADA I FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Renta Balanceada II", "ALLARIA RENTA BALANCEADA II FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA GESTION FCI", "ALLARIA GESTION FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Diversificado", "ALLARIA DIVERSIFICADO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Estratégico", "ALLARIA ESTRATEGICO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Estructurado", "ALLARIA ESTRUCTURADO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Renta Mixta FCI", "ALLARIA RENTA MIXTA FCI");
    FUND_TYPE_MAPPINGS.put(
        "Allaria Unicred Fondo Común de Inversión Abierto Pymes FCI",
        "ALLARIA UNICRED FONDO COMÚN DE INVERSIÓN ABIERTO PYMES FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Unicred Empresas FCI", "ALLARIA UNICRED EMPRESAS FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Unicred Cobertura FCI", "ALLARIA UNICRED COBERTURA FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Principal FCI", "ALLARIA PRINCIPAL FCI");
    FUND_TYPE_MAPPINGS.put(
        "Alamerica FCI Abierto Pymes FCI", "ALAMERICA FONDO COMÚN DE INVERSIÓN ABIERTO PYMES FCI");
    FUND_TYPE_MAPPINGS.put("INTERVALORES AHORRO", "INTERVALORES AHORRO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Cobertura Dinamica", "ALLARIA COBERTURA DINÁMICA FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA PATRIMONIO II", "ALLARIA PATRIMONIO II FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA CAPITAL FCI", "ALLARIA CAPITAL FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA PATRIMONIO IV", "ALLARIA PATRIMONIO IV FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA PATRIMONIO V", "ALLARIA PATRIMONIO V FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dinámico II", "ALLARIA DINÁMICO II FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA DINAMICO IV", "ALLARIA DINAMICO IV FCI");
    FUND_TYPE_MAPPINGS.put("FCI ALLARIA DINAMICO", "ALLARIA DINAMICO FCI");
    FUND_TYPE_MAPPINGS.put("FCI Allaria Gestión II", "ALLARIA GESTIÓN II FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Sustentable", "ALLARIA SUSTENTABLE ASG FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Agro", "ALLARIA AGRO FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Dinámico III", "ALLARIA DINÁMICO III FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Gestion V", "ALLARIA GESTION V FCI");
    FUND_TYPE_MAPPINGS.put("Allaria Equity Selection", "ALLARIA EQUITY SELECTION FCI");
    FUND_TYPE_MAPPINGS.put("ALLARIA AHORRO DINAMICO", "ALLARIA AHORRO DINÁMICO");
    FUND_TYPE_MAPPINGS.put("ALLARIA GESTION IV", "ALLARIA GESTIÓN IV FCI");
    FUND_TYPE_MAPPINGS.put("Alamerica Renta Fija Latam", "ALAMERICA RENTA FIJA DOLAR LATAM FCI");

    // Initialize class mappings
    CLASS_MAPPINGS.put("Clase A", "A");
    CLASS_MAPPINGS.put("Clase B", "B");
    CLASS_MAPPINGS.put("Clase C", "C");
    CLASS_MAPPINGS.put("Clase D", "D");
    CLASS_MAPPINGS.put("CL. A", "A");
    CLASS_MAPPINGS.put("CL. B", "B");
    CLASS_MAPPINGS.put("CL. C", "C");
    CLASS_MAPPINGS.put("clase A", "A");
    CLASS_MAPPINGS.put("clase B", "B");
    CLASS_MAPPINGS.put("clase C", "C");
    CLASS_MAPPINGS.put("Clase Blanqueo", "Ley Nº 27.743");
    CLASS_MAPPINGS.put("Clase Ley 27.743", "Ley Nº 27.743");
    CLASS_MAPPINGS.put("Clase  Ley 27.743", "Ley Nº 27.743");
    CLASS_MAPPINGS.put("Ley 27743", "Ley Nº 27.743");
    CLASS_MAPPINGS.put("LEY 27743", "Ley Nº 27.743");
  }

  public String mapFundName(String sourceName) {
    if (sourceName == null || sourceName.trim().isEmpty()) {
      return null;
    }

    String cleanSource = sourceName.trim();

    // Extract fund base name and class
    String fundBaseName = extractFundBaseName(cleanSource);
    String classInfo = extractClassInfo(cleanSource);

    // Map to target format
    String targetBaseName = FUND_TYPE_MAPPINGS.get(fundBaseName);
    if (targetBaseName == null) {
      // Try partial matching for complex names
      targetBaseName = findBestMatch(fundBaseName);
    }

    if (targetBaseName == null) {
      return null; // No mapping found
    }

    // Construct final name
    if (classInfo != null && !classInfo.isEmpty()) {
      return targetBaseName + " - " + classInfo;
    } else {
      return targetBaseName;
    }
  }

  private String extractFundBaseName(String name) {
    // Remove common suffixes and class information
    String baseName = name;

    // Remove class information and descriptive parts
    baseName = baseName.replaceAll("\\s*-\\s*(Clase|CL\\.|clase)\\s+[A-Z]\\s*-.*$", "");
    baseName = baseName.replaceAll("\\s*-\\s*(Clase|CL\\.|clase)\\s+[A-Z]$", "");
    baseName = baseName.replaceAll("\\s*-\\s*[A-Z]\\s*-.*$", "");
    baseName = baseName.replaceAll("\\s*-\\s*[A-Z]$", "");
    baseName = baseName.replaceAll("\\s*[A-Z]$", "");
    baseName = baseName.replaceAll("\\s*-\\s*Ley\\s+\\d+.*$", "");
    baseName = baseName.replaceAll("\\s*-\\s*Blanqueo.*$", "");
    baseName = baseName.replaceAll("\\s*Persona Humana Jurídica.*$", "");
    baseName = baseName.replaceAll("\\s*Mep\\s*Dolar.*$", "");
    baseName = baseName.replaceAll("\\s*Dolar.*$", "");
    baseName = baseName.replaceAll("\\s*MEP.*$", "");
    baseName = baseName.replaceAll("\\s*CABLE.*$", "");
    baseName = baseName.replaceAll("\\s*U\\$S.*$", "");
    baseName = baseName.replaceAll("\\s*u\\$u.*$", "");
    baseName = baseName.replaceAll("\\s*Mep.*$", "");

    return baseName.trim();
  }

  private String extractClassInfo(String name) {
    // Extract class information
    Pattern classPattern =
        Pattern.compile(".*?\\s*-\\s*(Clase|CL\\.|clase)\\s+([A-Z])(?:\\s*-.*)?$");
    java.util.regex.Matcher matcher = classPattern.matcher(name);
    if (matcher.matches()) {
      return matcher.group(2);
    }

    // Check for single letter class at end
    Pattern singleClassPattern = Pattern.compile(".*\\s+([A-Z])$");
    matcher = singleClassPattern.matcher(name);
    if (matcher.matches()) {
      return matcher.group(1);
    }

    // Check for Ley 27743/Blanqueo
    if (name.contains("Ley 27.743")
        || name.contains("LEY 27743")
        || name.contains("Ley 27743")
        || name.contains("Blanqueo")) {
      return "Ley Nº 27.743";
    }

    return null;
  }

  private String findBestMatch(String fundBaseName) {
    // Try to find partial matches
    for (Map.Entry<String, String> entry : FUND_TYPE_MAPPINGS.entrySet()) {
      String key = entry.getKey();
      if (fundBaseName.contains(key) || key.contains(fundBaseName)) {
        return entry.getValue();
      }
    }

    // Try fuzzy matching for similar names
    String normalizedInput = fundBaseName.toUpperCase().replaceAll("\\s+", " ");
    for (Map.Entry<String, String> entry : FUND_TYPE_MAPPINGS.entrySet()) {
      String normalizedKey = entry.getKey().toUpperCase().replaceAll("\\s+", " ");
      if (calculateSimilarity(normalizedInput, normalizedKey) > 0.8) {
        return entry.getValue();
      }
    }

    return null;
  }

  private double calculateSimilarity(String s1, String s2) {
    int maxLength = Math.max(s1.length(), s2.length());
    if (maxLength == 0) return 1.0;

    int editDistance = calculateEditDistance(s1, s2);
    return (maxLength - editDistance) / (double) maxLength;
  }

  private int calculateEditDistance(String s1, String s2) {
    int[][] dp = new int[s1.length() + 1][s2.length() + 1];

    for (int i = 0; i <= s1.length(); i++) {
      dp[i][0] = i;
    }
    for (int j = 0; j <= s2.length(); j++) {
      dp[0][j] = j;
    }

    for (int i = 1; i <= s1.length(); i++) {
      for (int j = 1; j <= s2.length(); j++) {
        if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
          dp[i][j] = dp[i - 1][j - 1];
        } else {
          dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
        }
      }
    }

    return dp[s1.length()][s2.length()];
  }

  // Utility method to map a list of fund names
  public List<String> mapFundNames(List<String> sourceNames) {
    List<String> mappedNames = new ArrayList<>();
    for (String sourceName : sourceNames) {
      String mapped = mapFundName(sourceName);
      mappedNames.add(mapped != null ? mapped : sourceName); // Keep original if no mapping found
    }
    return mappedNames;
  }
}
