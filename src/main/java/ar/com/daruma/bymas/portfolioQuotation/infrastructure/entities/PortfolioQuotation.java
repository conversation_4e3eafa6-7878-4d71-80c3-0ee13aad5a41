package ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_quotations")
public class PortfolioQuotation {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_asset_id", nullable = false)
  private PortfolioAsset portfolioAsset;

  @Column(nullable = false)
  private BigDecimal price;

  @Enumerated(EnumType.STRING)
  @Column(name = "kind", nullable = false)
  private QuotationKind kind;

  @Enumerated(EnumType.STRING)
  @Column(name = "quotation_type", nullable = false)
  private QuotationType quotationType;

  @Column(name = "processed_at", nullable = false)
  private LocalDateTime processedAt;

  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  public PortfolioQuotation() {}

  public PortfolioQuotation(
      PortfolioAsset portfolioAsset,
      BigDecimal price,
      QuotationKind kind,
      QuotationType quotationType,
      LocalDateTime processedAt) {
    this.portfolioAsset = portfolioAsset;
    this.price = price;
    this.kind = kind;
    this.quotationType = quotationType;
    this.processedAt = processedAt;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public PortfolioQuotationKey getPortfolioQuotationKey() {
    return new PortfolioQuotationKey(
        getPortfolioAsset().getId(), getQuotationType(), getProcessedAt(), getKind());
  }

  public UUID getPortfolioAssetId() {
    return getPortfolioAsset().getId();
  }

  public record PortfolioQuotationKey(
      UUID portfolioAssetId,
      QuotationType quotationType,
      LocalDateTime processedAt,
      QuotationKind kind) {}
}
