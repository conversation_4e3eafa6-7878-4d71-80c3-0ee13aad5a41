package ar.com.daruma.bymas.portfolioQuotation.application.create;

import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CreateAssetQuotationByBuySalePriceAndAssetRetrievedAtTimestampWithoutSaving {

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<PortfolioQuotation> create(
      LocalDateTime timestamp,
      BigDecimal buyPrice,
      BigDecimal salePrice,
      PortfolioAsset portfolioAsset) {
    PortfolioQuotation buy =
        new PortfolioQuotation(portfolioAsset, buyPrice, MARKET_DATA, QuotationType.BUY, timestamp);
    PortfolioQuotation sale =
        new PortfolioQuotation(
            portfolioAsset, salePrice, MARKET_DATA, QuotationType.SELL, timestamp);
    return List.of(buy, sale);
  }
}
