package ar.com.daruma.bymas.portfolioPositionBalances.domain;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface PortfolioPositionBalanceService {
  PortfolioPositionBalance save(PortfolioPositionBalance balance);

  List<PortfolioPositionBalance> saveAll(List<PortfolioPositionBalance> balances);

  List<PortfolioPositionBalance> findLatestByDateAndMaybeAccount(
      LocalDate date, Optional<PortfolioAccount> maybeAccount);
}
