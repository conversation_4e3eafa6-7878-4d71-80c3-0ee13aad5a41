package ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.adapter;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.repository.PortfolioPositionBalanceRepository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioPositionBalanceServiceImpl implements PortfolioPositionBalanceService {

  private final PortfolioPositionBalanceRepository repository;

  @Autowired
  public PortfolioPositionBalanceServiceImpl(PortfolioPositionBalanceRepository repository) {
    this.repository = repository;
  }

  @Override
  public PortfolioPositionBalance save(PortfolioPositionBalance balance) {
    return repository.save(balance);
  }

  @Override
  public List<PortfolioPositionBalance> saveAll(List<PortfolioPositionBalance> balances) {
    return repository.saveAll(balances);
  }

  @Override
  public List<PortfolioPositionBalance> findLatestByDateAndMaybeAccount(
      LocalDate date, Optional<PortfolioAccount> maybeAccount) {
    LocalDateTime startOfDay = date.atStartOfDay();
    LocalDateTime endOfDay = date.plusDays(1).atStartOfDay().minusNanos(1);
    return maybeAccount
        .map(
            account ->
                repository.findLatestBalancesByCreatedAtBetweenAndPortfolioAccountId(
                    startOfDay, endOfDay, account.getId()))
        .orElseGet(() -> repository.findLatestBalancesByCreatedAtBetween(startOfDay, endOfDay));
  }
}
