package ar.com.daruma.bymas.portfolioPositionBalances.application.find;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FindLatestPortfolioPositionBalancesByDateAndMaybePortfolioAccount {
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;

  public List<PortfolioPositionBalance> find(
      LocalDate date, Optional<PortfolioAccount> maybeAccount) {
    log.info(
        "Finding latest balances for date: {} and account: {}",
        date,
        maybeAccount.map(account -> account.getId().toString()).orElse("all accounts"));
    List<PortfolioPositionBalance> result =
        portfolioPositionBalanceService.findLatestByDateAndMaybeAccount(date, maybeAccount);
    log.info(
        "{} balances found for date: {} and account: {}",
        result.size(),
        date,
        maybeAccount.map(account -> account.getId().toString()).orElse("all accounts"));
    return result;
  }
}
