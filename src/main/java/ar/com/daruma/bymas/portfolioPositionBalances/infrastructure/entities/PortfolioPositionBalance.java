package ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities;

import ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization.JsonNodeUserType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementProcessState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.*;
import org.hibernate.annotations.Type;
import org.springframework.lang.Nullable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "portfolio_position_balances")
public class PortfolioPositionBalance {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "movement_id", referencedColumnName = "id")
  private @Nullable PortfolioMovement movement;

  @Column(name = "settlement_quantity", nullable = false)
  private BigDecimal settlementQuantity;

  @Column private String description;
  @Column private String tags;

  @Type(JsonNodeUserType.class)
  @Column(name = "metadata", columnDefinition = "jsonb")
  private JsonNode metadata;

  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "agreement_quantity", nullable = false)
  private BigDecimal agreementQuantity;

  @Column(name = "locked_quantity", nullable = false)
  private BigDecimal lockedQuantity;

  @Column(name = "available_quantity", nullable = false)
  private BigDecimal availableQuantity;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_position_id", nullable = false, referencedColumnName = "id")
  private PortfolioPosition portfolioPosition;

  @Enumerated(EnumType.STRING)
  @Column(name = "movement_process_state")
  private @Nullable MovementProcessState movementProcessState;

  @Column(name = "is_cancel_movement", nullable = false)
  private Boolean isCancelMovement;

  @Column(name = "is_sync", nullable = false)
  private Boolean isSync;

  public PortfolioPositionBalance(
      PortfolioPosition portfolioPosition,
      @Nullable PortfolioMovement movement,
      Boolean isCancelMovement) {
    this.movement = movement;
    this.portfolioPosition = portfolioPosition;
    this.settlementQuantity = portfolioPosition.getSettlementQuantity();
    this.description = portfolioPosition.getDescription();
    this.tags = portfolioPosition.getTags();
    this.metadata = portfolioPosition.getMetadata();
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
    this.agreementQuantity = portfolioPosition.getAgreementQuantity();
    this.lockedQuantity = portfolioPosition.getLockedQuantity();
    this.availableQuantity = portfolioPosition.getAvailableQuantity();
    this.movementProcessState = movement != null ? movement.getMovementProcessState() : null;
    this.isCancelMovement = isCancelMovement;
    this.isSync = movement == null;
  }

  public BasePortfolioPosition.AssetAndAccountIdPair getAssetAndAccountIdPair() {
    return this.portfolioPosition.getAssetAndAccountIdPair();
  }

  public boolean hasDifferentQuantities(PortfolioPositionHistory history) {
    return !this.settlementQuantity.equals(history.getSettlementQuantity())
        || !this.agreementQuantity.equals(history.getAgreementQuantity());
  }
}
