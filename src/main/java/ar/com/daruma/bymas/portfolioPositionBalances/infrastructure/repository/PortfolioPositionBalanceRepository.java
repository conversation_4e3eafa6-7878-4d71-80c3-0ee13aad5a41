package ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.repository;

import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioPositionBalanceRepository
    extends JpaRepository<PortfolioPositionBalance, UUID> {

  @Query(
      value =
          "SELECT pb.* FROM portfolio_position_balances pb "
              + "INNER JOIN ( "
              + "  SELECT portfolio_position_id, MAX(created_at) AS max_created_at "
              + "  FROM portfolio_position_balances "
              + "  WHERE created_at BETWEEN :start AND :end "
              + "  GROUP BY portfolio_position_id "
              + ") latest "
              + "ON pb.portfolio_position_id = latest.portfolio_position_id "
              + "AND pb.created_at = latest.max_created_at",
      nativeQuery = true)
  List<PortfolioPositionBalance> findLatestBalancesByCreatedAtBetween(
      @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

  @Query(
      value =
          "SELECT pb.* FROM portfolio_position_balances pb "
              + "INNER JOIN portfolio_positions pp ON pb.portfolio_position_id = pp.id "
              + "INNER JOIN ( "
              + "  SELECT portfolio_position_id, MAX(created_at) AS max_created_at "
              + "  FROM portfolio_position_balances "
              + "  WHERE created_at BETWEEN :start AND :end "
              + "  GROUP BY portfolio_position_id "
              + ") latest ON pb.portfolio_position_id = latest.portfolio_position_id "
              + "AND pb.created_at = latest.max_created_at "
              + "WHERE pp.portfolio_account_id = :accountId",
      nativeQuery = true)
  List<PortfolioPositionBalance> findLatestBalancesByCreatedAtBetweenAndPortfolioAccountId(
      @Param("start") LocalDateTime start,
      @Param("end") LocalDateTime end,
      @Param("accountId") UUID accountId);
}
