package ar.com.daruma.bymas.portfolioUserAccountRelationships.application.upsert;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.PortfolioUserAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationship;
import ar.com.daruma.citadel.utils.CustomLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink {

  private final PortfolioUserAccountRelationshipsService service;
  private static final CustomLogger logger =
      CustomLogger.getLogger(
          UpsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink.class);

  public PortfolioUserAccountRelationship execute(
      PortfolioAccountRelationships relationship, AllariaLinks link) {
    Integer userId = link.getUserId();
    logger.info(
        "Starting upsert of portfolio user account relationship for relationship: {}, userId: {}",
        relationship.getId(),
        userId);
    PortfolioUserAccountRelationship portfolioUserRelationship =
        service.upsertByRelationshipAndUser(relationship, userId, link);
    logger.info(
        "Successfully upserted portfolio user account relationship: {}, userId: {}",
        portfolioUserRelationship.getId(),
        userId);
    return portfolioUserRelationship;
  }
}
