package ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.adapter;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.PortfolioUserAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationship;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.repository.PortfolioUserAccountRelationshipsRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortfolioUserAccountRelationshipsServiceImpl
    implements PortfolioUserAccountRelationshipsService {

  private final PortfolioUserAccountRelationshipsRepository repository;

  @Override
  public List<PortfolioUserAccountRelationship> findByRelationshipIds(List<UUID> relationshipIds) {
    return repository.findByRelationshipIdIn(relationshipIds);
  }

  @Override
  public List<PortfolioUserAccountRelationship> findByUserId(Integer userId) {
    return repository.findById_UserId(userId);
  }

  @Override
  public PortfolioUserAccountRelationship upsertByRelationshipAndUser(
      PortfolioAccountRelationships relationship, Integer userId, AllariaLinks link) {
    Optional<PortfolioUserAccountRelationship> maybeRelationship =
        repository.findById_RelationshipIdAndId_UserId(relationship.getId(), userId);
    maybeRelationship.ifPresent(
        portfolioUserAccountRelationship ->
            portfolioUserAccountRelationship.setState(link.getActiveState()));
    return repository.save(
        maybeRelationship.orElseGet(
            () -> new PortfolioUserAccountRelationship(relationship, userId, link)));
  }
}
