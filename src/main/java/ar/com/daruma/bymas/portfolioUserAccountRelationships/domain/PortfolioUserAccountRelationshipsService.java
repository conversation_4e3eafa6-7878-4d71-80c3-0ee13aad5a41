package ar.com.daruma.bymas.portfolioUserAccountRelationships.domain;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationship;
import java.util.List;
import java.util.UUID;

public interface PortfolioUserAccountRelationshipsService {

  List<PortfolioUserAccountRelationship> findByRelationshipIds(List<UUID> relationshipIds);

  List<PortfolioUserAccountRelationship> findByUserId(Integer userId);

  PortfolioUserAccountRelationship upsertByRelationshipAndUser(
      PortfolioAccountRelationships relationship, Integer userId, AllariaLinks link);
}
