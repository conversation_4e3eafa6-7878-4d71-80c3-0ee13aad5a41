package ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.Hibernate;

@Getter
@Setter
@Embeddable
public class PortfolioUserAccountRelationshipId implements java.io.Serializable {
  private static final long serialVersionUID = 3909580209441773349L;

  @NotNull
  @Column(name = "relationship_id", nullable = false)
  private UUID relationshipId;

  @NotNull
  @Column(name = "user_id", nullable = false)
  private Integer userId;

  public PortfolioUserAccountRelationshipId() {}

  public PortfolioUserAccountRelationshipId(UUID id, Integer userId) {
    this.relationshipId = id;
    this.userId = userId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
    PortfolioUserAccountRelationshipId entity = (PortfolioUserAccountRelationshipId) o;
    return Objects.equals(this.relationshipId, entity.relationshipId)
        && Objects.equals(this.userId, entity.userId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(relationshipId, userId);
  }
}
