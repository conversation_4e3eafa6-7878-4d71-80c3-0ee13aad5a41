package ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.repository;

import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationship;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationshipId;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioUserAccountRelationshipsRepository
    extends JpaRepository<PortfolioUserAccountRelationship, PortfolioUserAccountRelationshipId> {

  List<PortfolioUserAccountRelationship> findByRelationshipIdIn(List<UUID> relationshipIds);

  List<PortfolioUserAccountRelationship> findById_UserId(Integer userId);

  Optional<PortfolioUserAccountRelationship> findById_RelationshipIdAndId_UserId(
      UUID id, Integer userId);
}
