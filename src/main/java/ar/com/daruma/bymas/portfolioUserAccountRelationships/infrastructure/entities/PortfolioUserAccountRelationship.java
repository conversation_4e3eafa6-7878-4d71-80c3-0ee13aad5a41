package ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.citadel.model.global.ActiveState;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "portfolio_user_account_relationships")
public class PortfolioUserAccountRelationship {
  @EmbeddedId private PortfolioUserAccountRelationshipId id;

  @MapsId("relationshipId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "relationship_id", nullable = false)
  private PortfolioAccountRelationships relationship;

  @Enumerated(EnumType.STRING)
  @Column(name = "state", nullable = false)
  private ActiveState state;

  public PortfolioUserAccountRelationship() {}

  public PortfolioUserAccountRelationship(
      PortfolioAccountRelationships relationship, Integer userId, AllariaLinks link) {
    this.id = new PortfolioUserAccountRelationshipId(relationship.getId(), userId);
    this.relationship = relationship;
    this.state = link.getActiveState();
  }
}
