package ar.com.daruma.bymas.version.presentation.controller;

import ar.com.daruma.bymas.version.domain.InfoService;
import ar.com.daruma.bymas.version.domain.entities.ByMasApp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@ResponseBody
@RestController
@RequestMapping("/by-mas")
public class VersionController {

  @Autowired InfoService infoService;

  @GetMapping("/version")
  public ByMasApp health() {
    return infoService.version();
  }
}
