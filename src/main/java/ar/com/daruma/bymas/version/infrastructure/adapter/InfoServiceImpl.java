package ar.com.daruma.bymas.version.infrastructure.adapter;

import ar.com.daruma.bymas.version.domain.InfoService;
import ar.com.daruma.bymas.version.domain.entities.ByMasApp;
import ar.com.daruma.bymas.version.domain.entities.ByMasInfo;
import com.google.gson.Gson;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.info.InfoEndpoint;
import org.springframework.stereotype.Service;

@Service
public class InfoServiceImpl implements InfoService {

  @Autowired private InfoEndpoint delegate;

  @Override
  public ByMasApp version() {

    final Map<String, Object> info = delegate.info();
    final ByMasInfo bymasInfo = new Gson().fromJson(new Gson().toJson(info), ByMasInfo.class);

    return bymasInfo.getApp();
  }
}
