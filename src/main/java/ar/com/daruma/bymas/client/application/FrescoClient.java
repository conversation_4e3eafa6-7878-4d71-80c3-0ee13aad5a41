package ar.com.daruma.bymas.client.application;

import ar.com.daruma.bymas.client.domain.AllariaAccount;
import ar.com.daruma.bymas.client.domain.service.FrescoService;
import ar.com.daruma.bymas.configuration.fresco.FrescoConfiguration;
import ar.com.daruma.bymas.configuration.secrets.AdminTokenConfiguration;
import ar.com.daruma.citadel.dto.GetRequestDTO;
import ar.com.daruma.citadel.utils.CustomLogger;
import ar.com.daruma.citadel.utils.HttpClient;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FrescoClient implements FrescoService {

  @Autowired private FrescoConfiguration frescoConfiguration;
  @Autowired private AdminTokenConfiguration adminTokenConfiguration;
  private static final CustomLogger logger = CustomLogger.getLogger(FrescoClient.class);

  /**
   * Retrieves an Allaria account from the Fresco service based on the provided account-id.
   *
   * @param accountId The unique identifier (code) of the Allaria account to retrieve. Must not be
   *     null.
   * @return The {@link AllariaAccount} object representing the account retrieved from the Fresco
   *     service.
   */
  public AllariaAccount getAllariaAccountById(Long accountId) {
    logger.info(String.format("Getting allaria-account with id [%s] from Fresco", accountId));
    Map<String, String> headers = headersWithAdminToken();
    String url = frescoConfiguration.getHost() + "/account-by-number/" + accountId;
    GetRequestDTO<AllariaAccount> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setCustomHeaders(headers);
    request.setResponseClass(AllariaAccount.class);
    AllariaAccount response = HttpClient.get(request);
    logger.info(
        String.format(
            "Allaria-account with id [%s] has been successfully retrieved from Fresco", accountId));
    return response;
  }

  /**
   * Retrieves an Allaria account from the Fresco service based on the provided account-code.
   *
   * @param accountCode The unique identifier (code) of the Allaria account to retrieve. Must not be
   *     null.
   * @return The {@link AllariaAccount} object representing the account retrieved from the Fresco
   *     service.
   */
  public AllariaAccount getAllariaAccountByCode(Long accountCode) {
    logger.info(String.format("Getting allaria-account with code [%s] from Fresco", accountCode));
    Map<String, String> headers = headersWithAdminToken();
    String url = frescoConfiguration.getHost() + "/accounts/" + accountCode;
    GetRequestDTO<AllariaAccount> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setCustomHeaders(headers);
    request.setResponseClass(AllariaAccount.class);
    AllariaAccount response = HttpClient.get(request);
    logger.info(
        String.format(
            "Allaria-account with code [%s] has been successfully retrieved from Fresco",
            accountCode));
    return response;
  }

  private Map<String, String> headersWithAdminToken() {
    return Map.of(
        "Content-Type", "application/json", "Authorization", adminTokenConfiguration.getToken());
  }
}
