package ar.com.daruma.bymas.client.domain;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;

public class Day {
  private LocalDate now;

  public Day(LocalDate now) {
    this.now = now;
  }

  public boolean isWorkingDay(List<LocalDate> holidays) {
    return !(isWeekend() || isHoliday(holidays));
  }

  private boolean isWeekend() {
    return now.getDayOfWeek() == DayOfWeek.SATURDAY || now.getDayOfWeek() == DayOfWeek.SUNDAY;
  }

  private boolean isHoliday(List<LocalDate> holidays) {
    return holidays.stream().anyMatch(holiday -> holiday.isEqual(now));
  }
}
