package ar.com.daruma.bymas.client.allariaMarketData.entities;

import ar.com.daruma.bymas.client.allariaMarketData.exceptions.AssetPeriodDataNotFoundException;
import ar.com.daruma.bymas.client.allariaMarketData.exceptions.AssetPeriodDataValidationException;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AssetResponse {
  private Map<String, AssetData> assets = new HashMap<>();

  @JsonAnySetter
  public void setCedearData(String cedear, AssetData data) {
    assets.put(cedear, data);
  }

  public List<AssetSimplifiedData> getSimplifiedAssetData() {
    List<AssetSimplifiedData> result = new ArrayList<>();
    for (Map.Entry<String, AssetData> entry : getAssets().entrySet()) {
      String assetName = entry.getKey();
      AssetData assetData = entry.getValue();
      AssetPeriodData fastestPeriodData = assetData.getFastestPeriodData();
      if (fastestPeriodData == null) {
        throw new AssetPeriodDataNotFoundException(
            "No period data available for asset: " + assetName);
      }
      Set<ConstraintViolation<AssetPeriodData>> violations =
          Validation.buildDefaultValidatorFactory().getValidator().validate(fastestPeriodData);
      if (!violations.isEmpty()) {
        StringBuilder errorMsg =
            new StringBuilder("Validation failed for asset: " + assetName + " - ");
        violations.forEach(
            violation ->
                errorMsg
                    .append(violation.getPropertyPath())
                    .append(": ")
                    .append(violation.getMessage())
                    .append("; "));
        throw new AssetPeriodDataValidationException(errorMsg.toString());
      }
      result.add(new AssetSimplifiedData(assetName, fastestPeriodData));
    }
    return result;
  }
}
