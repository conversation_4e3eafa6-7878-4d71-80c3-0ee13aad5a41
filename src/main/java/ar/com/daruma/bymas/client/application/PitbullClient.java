package ar.com.daruma.bymas.client.application;

import ar.com.daruma.bymas.client.domain.service.PitbullService;
import ar.com.daruma.bymas.configuration.secrets.AdminTokenConfiguration;
import ar.com.daruma.citadel.dto.GetRequestDTO;
import ar.com.daruma.citadel.utils.CustomLogger;
import ar.com.daruma.citadel.utils.HttpClient;
import ar.com.daruma.citadelsdk.configuration.PitbullConfiguration;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PitbullClient implements PitbullService {

  private final PitbullConfiguration pitbullConfiguration;
  private final AdminTokenConfiguration adminTokenConfiguration;
  private static final CustomLogger logger = CustomLogger.getLogger(FrescoClient.class);

  public List<LocalDate> findHolidays() {
    Map<String, String> headers = headersWithAdminToken();
    String url = pitbullConfiguration.getHost() + "/pitbull/holidays";
    GetRequestDTO<List> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setCustomHeaders(headers);
    request.setResponseClass(List.class);
    List<?> response = HttpClient.get(request);
    return response.stream()
        .map(element -> LocalDate.parse(element.toString()))
        .collect(Collectors.toList());
  }

  private Map<String, String> headersWithAdminToken() {
    return Map.of(
        "Content-Type", "application/json", "Authorization", adminTokenConfiguration.getToken());
  }
}
