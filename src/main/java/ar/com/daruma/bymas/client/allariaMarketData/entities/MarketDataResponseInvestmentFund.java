package ar.com.daruma.bymas.client.allariaMarketData.entities;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public record MarketDataResponseInvestmentFund(
    String name,
    String abbreviation,
    BigDecimal variation,
    @JsonAlias("net_worth") BigDecimal netWorth,
    @JsonAlias("price_per_share") BigDecimal pricePerShare,
    @JsonAlias("shares_in_circulation") BigDecimal sharesInCirculation,
    BigDecimal tir,
    BigDecimal tna,
    String currency,
    @JsonAlias("retrieved_at") LocalDateTime retrievedAt)
    implements MarketDataGeneralQuotation {
  @Override
  public BigDecimal getBuyPrice() {
    return pricePerShare;
  }

  @Override
  public BigDecimal getSalePrice() {
    return pricePerShare;
  }

  @Override
  public LocalDateTime getRetrievedAtTimestamp() {
    return retrievedAt;
  }

  @Override
  public String getAssetAbbreviation() {
    return abbreviation;
  }

  @Override
  public String getAssetName() {
    return name;
  }

  @Override
  public Boolean isFoundByAbbreviation() {
    return false;
  }

  @Override
  public String searchTerm() {
    return name;
  }
}
