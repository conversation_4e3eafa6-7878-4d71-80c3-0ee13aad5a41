package ar.com.daruma.bymas.client.allariaMarketData.entities;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DollarRate {

  @JsonAlias("purchase_price")
  private BigDecimal purchasePrice;

  @JsonAlias("selling_price")
  private BigDecimal sellingPrice;

  @JsonAlias("update_at")
  private LocalDateTime updateAt;
}
