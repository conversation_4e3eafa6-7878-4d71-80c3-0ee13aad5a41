package ar.com.daruma.bymas.client.allariaMarketData.entities;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalPrecision;
import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.oneHundred;

import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AssetPeriodData {
  private BigDecimal trade;
  private Long volume;
  private String currency;

  @JsonAlias("ask_price")
  @NotNull
  private BigDecimal askPrice;

  @NotNull
  @JsonAlias("bid_price")
  private BigDecimal bidPrice;

  @JsonAlias("security_id")
  private String securityId;

  private Long trades;

  @JsonAlias("trade_amount")
  private BigDecimal tradeAmount;

  @JsonAlias("weighted_average_price")
  private BigDecimal weightedAveragePrice;

  private BigDecimal imbalance;

  @JsonAlias("previous_close")
  private BigDecimal previousClose;

  @JsonAlias("closing_price")
  private BigDecimal closingPrice;

  @JsonAlias("broadcast_time")
  @NotNull
  private String broadcastTime;

  @JsonAlias("retrieved_at")
  private String retrievedAt;

  public LocalDateTime getTimestamp() {
    return LocalDateTime.parse(this.retrievedAt);
  }

  public void dividePricesBy100() {
    setAskPrice(getAskPrice().divide(oneHundred, bigDecimalPrecision, RoundingMode.HALF_DOWN));
    setBidPrice(getBidPrice().divide(oneHundred, bigDecimalPrecision, RoundingMode.HALF_DOWN));
  }
}
