package ar.com.daruma.bymas.client.allariaMarketData.entities;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AssetData {
  private Map<String, AssetPeriodData> periods = new HashMap<>();

  @JsonAnySetter
  public void setPeriodData(String periodId, AssetPeriodData data) {
    periods.put(periodId, data);
  }

  public AssetPeriodData getFastestPeriodData() {
    if (periods.isEmpty()) {
      return null;
    }
    return periods.get(Collections.min(periods.keySet()));
  }
}
