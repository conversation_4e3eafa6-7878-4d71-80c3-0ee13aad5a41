package ar.com.daruma.bymas.client.allariaMarketData.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AssetSimplifiedData implements MarketDataGeneralQuotation {
  private String abbreviation;
  private AssetPeriodData data;

  public AssetSimplifiedData(String abbreviation, AssetPeriodData data) {
    this.abbreviation = abbreviation;
    this.data = data;
  }

  @Override
  public BigDecimal getBuyPrice() {
    return data.getBidPrice();
  }

  @Override
  public BigDecimal getSalePrice() {
    return data.getAskPrice();
  }

  @Override
  public LocalDateTime getRetrievedAtTimestamp() {
    return data.getTimestamp();
  }

  @Override
  public String getAssetAbbreviation() {
    return abbreviation;
  }

  @Override
  public String getAssetName() {
    return abbreviation;
  }

  @Override
  public Boolean isFoundByAbbreviation() {
    return true;
  }

  @Override
  public String searchTerm() {
    return abbreviation;
  }
}
