package ar.com.daruma.bymas.client.allariaMarketData;

import ar.com.daruma.bymas.client.allariaMarketData.entities.*;
import ar.com.daruma.bymas.client.allariaMarketData.exceptions.AssetDataNotFoundException;
import ar.com.daruma.bymas.configuration.allariaMarketData.AllariaMarketDataConfiguration;
import ar.com.daruma.citadel.dto.GetRequestDTO;
import ar.com.daruma.citadel.utils.HttpClient;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AllariaMarketDataClient {

  private final AllariaMarketDataConfiguration allariaMarketDataConfiguration;

  public DollarResponse getDollarRates() {
    log.info("Getting dollar rates from Allaria Market Data service");
    String url = allariaMarketDataConfiguration.getHost() + "/dollars";
    GetRequestDTO<DollarResponse> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(DollarResponse.class);

    DollarResponse response = HttpClient.get(request);
    log.info("Dollar rates have been successfully retrieved from Allaria Market Data service");
    return response;
  }

  // TODO : cambiar el conseguir assets por un solo endpoint que devuelva todos los assets de cada
  // tipo -> OPEX lo esta haciendo

  public MarketDataGeneralQuotation getAssetByAbbreviation(String abbreviation) {
    log.info("Getting asset by abbreviation {} from Allaria Market Data service", abbreviation);
    String url =
        allariaMarketDataConfiguration.getHost()
            + "/securities/symbols/"
            + abbreviation.toUpperCase();

    GetRequestDTO<AssetResponse> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(AssetResponse.class);

    AssetResponse response = HttpClient.get(request);
    AssetSimplifiedData assetData =
        response.getSimplifiedAssetData().stream()
            .filter(asset -> asset.getAbbreviation().equalsIgnoreCase(abbreviation))
            .findFirst()
            .orElseThrow(
                () ->
                    new AssetDataNotFoundException(
                        "Asset simplified quotation data not found for abbreviation: "
                            + abbreviation));
    log.info("Asset data has been successfully retrieved from Allaria Market Data service");
    return assetData;
  }

  public MarketDataGeneralQuotation getInvestmentFundByName(String name) {
    log.info("Getting Investment Fund by name {} from Allaria Market Data service", name);
    String url = allariaMarketDataConfiguration.getHost() + "/investment-funds/" + name;

    GetRequestDTO<MarketDataResponseInvestmentFund> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(MarketDataResponseInvestmentFund.class);

    MarketDataResponseInvestmentFund response = HttpClient.get(request);

    if (response == null) {
      log.warn("Investment Fund data not found for name: {}", name);
      throw new AssetDataNotFoundException("Investment Fund data not found for name: " + name);
    }
    log.info(
        "Investment Fund data has been successfully retrieved from Allaria Market Data service");
    return response;
  }

  public List<MarketDataGeneralQuotation> getAllAssetsData() {
    log.info("Getting all assets data from Allaria Market Data service");
    List<MarketDataGeneralQuotation> allAssets = new ArrayList<>();
    allAssets.addAll(getCedearData());
    allAssets.addAll(getOptionsData());
    allAssets.addAll(getStocksData());
    allAssets.addAll(getFixedIncomeData());
    allAssets.addAll(getInvestmentFundsData());
    log.info("All assets data has been successfully retrieved from Allaria Market Data service");
    return allAssets;
  }

  public List<AssetSimplifiedData> getFixedIncomeData() {
    List<AssetSimplifiedData> allAssets = new ArrayList<>();
    allAssets.addAll(getBondsData());
    allAssets.addAll(getCertParticipation());
    allAssets.addAll(getLetterData());
    allAssets.addAll(getTreasuryLetterData());
    allAssets.addAll(getNegotiableObligations());
    allAssets.addAll(getNegotiableObligationsPymes());
    allAssets.addAll(getDebtSecurities());
    allAssets.addAll(getPublicSecurities());
    return allAssets.stream().peek(assetData -> assetData.getData().dividePricesBy100()).toList();
  }

  private List<MarketDataResponseInvestmentFund> getInvestmentFundsData() {
    log.info("Getting Investment Funds data from Allaria Market Data service");
    String url = allariaMarketDataConfiguration.getHost() + "/investment-funds";

    GetRequestDTO<MarketDataResponseInvestmentFund[]> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(MarketDataResponseInvestmentFund[].class);

    MarketDataResponseInvestmentFund[] response = HttpClient.get(request);

    log.info(
        "Investment Funds data has been successfully retrieved from Allaria Market Data service");
    return Arrays.asList(response);
  }

  private List<AssetSimplifiedData> getCedearData() {
    log.info("Getting CEDEAR data from Allaria Market Data service");
    String url = allariaMarketDataConfiguration.getHost() + "/equity/cedears";

    GetRequestDTO<AssetResponse> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(AssetResponse.class);

    AssetResponse response = HttpClient.get(request);

    log.info("CEDEAR data has been successfully retrieved from Allaria Market Data service");
    return response.getSimplifiedAssetData();
  }

  private List<AssetSimplifiedData> getOptionsData() {
    log.info("Getting Options data from Allaria Market Data service");
    String url = allariaMarketDataConfiguration.getHost() + "/equity/options";

    GetRequestDTO<AssetResponse> request = new GetRequestDTO<>();
    request.setUrl(url);
    request.setResponseClass(AssetResponse.class);

    AssetResponse response = HttpClient.get(request);

    log.info("Options data has been successfully retrieved from Allaria Market Data service");
    return response.getSimplifiedAssetData();
  }

  private List<AssetSimplifiedData> getStocksData() {
    String generalUrl = allariaMarketDataConfiguration.getHost() + "/equity/stocks/general";
    String liderUrl = allariaMarketDataConfiguration.getHost() + "/equity/stocks/lider";
    return getAssetDataFromMultipleUrls(List.of(generalUrl, liderUrl), "Stocks data");
  }

  private List<AssetSimplifiedData> getBondsData() {
    String pptUrl =
        allariaMarketDataConfiguration.getHost() + "/fixed_income/bonosconsolidacion/ppt";
    return getAssetDataFromMultipleUrls(List.of(pptUrl), "Bonds data");
  }

  private List<AssetSimplifiedData> getCertParticipation() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/certparticipacion/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Cert Participation data");
  }

  private List<AssetSimplifiedData> getLetterData() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/letras/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Letter data");
  }

  private List<AssetSimplifiedData> getTreasuryLetterData() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/letrastesoro/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Treasury Letter data");
  }

  private List<AssetSimplifiedData> getNegotiableObligations() {
    String url =
        allariaMarketDataConfiguration.getHost() + "/fixed_income/obligacionesnegociables/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Negotiable Obligations data");
  }

  private List<AssetSimplifiedData> getNegotiableObligationsPymes() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/onpymes/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "PYMES Negotiable Obligations data");
  }

  private List<AssetSimplifiedData> getDebtSecurities() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/titulosdeuda/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Debt Securities data");
  }

  private List<AssetSimplifiedData> getPublicSecurities() {
    String url = allariaMarketDataConfiguration.getHost() + "/fixed_income/titulospublicos/ppt";
    return getAssetDataFromMultipleUrls(List.of(url), "Government Securities data");
  }

  private List<AssetSimplifiedData> getAssetDataFromMultipleUrls(
      List<String> urls, String logMessage) {
    log.info("Getting " + logMessage + " from Allaria Market Data service");
    List<AssetSimplifiedData> combinedData =
        urls.stream()
            .map(
                url -> {
                  GetRequestDTO<AssetResponse> request = new GetRequestDTO<>();
                  request.setUrl(url);
                  request.setResponseClass(AssetResponse.class);
                  return HttpClient.get(request);
                })
            .flatMap(response -> response.getSimplifiedAssetData().stream())
            .collect(Collectors.toList());

    log.info(logMessage + " has been successfully retrieved from Allaria Market Data service");
    return combinedData;
  }
}
