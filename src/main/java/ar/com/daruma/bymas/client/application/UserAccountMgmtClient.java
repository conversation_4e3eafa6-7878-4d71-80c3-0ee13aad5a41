package ar.com.daruma.bymas.client.application;

import ar.com.daruma.bymas.client.domain.AllariaMasAccountResponse;
import ar.com.daruma.bymas.client.domain.service.UserAccountMgmtService;
import ar.com.daruma.bymas.configuration.secrets.AdminTokenConfiguration;
import ar.com.daruma.bymas.configuration.userAccountMgmt.UserAccountMgmtConfiguration;
import ar.com.daruma.citadel.dto.GetRequestDTO;
import ar.com.daruma.citadel.exceptions.HttpException;
import ar.com.daruma.citadel.utils.CustomLogger;
import ar.com.daruma.citadel.utils.HttpClient;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserAccountMgmtClient implements UserAccountMgmtService {

  @Autowired UserAccountMgmtConfiguration userAccountMgmtConfiguration;
  @Autowired private AdminTokenConfiguration adminTokenConfiguration;
  private static final CustomLogger logger = CustomLogger.getLogger(UserAccountMgmtClient.class);

  public AllariaMasAccountResponse getAccount(Integer accountId) {
    logger.info(
        String.format("Getting allaria-account with code [%s] from user account mgmt", accountId));

    Map<String, String> headers = headersWithAdminToken();

    String url = userAccountMgmtConfiguration.getHost() + "/accounts/" + accountId;

    GetRequestDTO<AllariaMasAccountResponse> request = new GetRequestDTO<>();

    request.setUrl(url);
    request.setCustomHeaders(headers);
    request.setResponseClass(AllariaMasAccountResponse.class);

    try {
      AllariaMasAccountResponse response = HttpClient.get(request);
      logger.info(
          String.format(
              "Allaria-account with code [%s] has been successfully retrieved from user account mgmt",
              accountId));
      return response;
    } catch (HttpException e) {
      logger.error("Error getting allaria account from user account mgmt: {}", e.getMessage());
      throw e;
    }
  }

  private Map<String, String> headersWithAdminToken() {
    return Map.of(
        "Content-Type",
        "application/json",
        "Authorization",
        "Bearer " + adminTokenConfiguration.getToken());
  }
}
