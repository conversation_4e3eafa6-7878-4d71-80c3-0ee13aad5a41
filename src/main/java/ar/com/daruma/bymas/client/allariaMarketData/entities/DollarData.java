package ar.com.daruma.bymas.client.allariaMarketData.entities;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DollarData {

  @JsonAlias("usd_ccl_t1")
  private DollarRate usdCclT1;

  @JsonAlias("usd_blue")
  private DollarRate usdBlue;

  @JsonAlias("usd_ccl_t0")
  private DollarRate usdCclT0;

  @JsonAlias("usd_official_bna")
  private DollarRate usdOfficialBna;

  @JsonAlias("usd_crypto")
  private DollarRate usdCrypto;

  @JsonAlias("usd_mep_t1")
  private DollarRate usdMepT1;

  @JsonAlias("usd_mep_t0")
  private DollarRate usdMepT0;
}
