package ar.com.daruma.bymas.runner;

import java.util.Properties;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.stereotype.Component;

@Component
public class ByMasInitializer
    implements ApplicationContextInitializer<ConfigurableApplicationContext> {
  private static final Logger logger = LogManager.getLogger(ByMasInitializer.class);
  private static final String SPRING_DATASOURCE_ALLARIAMAS = "spring.datasource.allaria-mas";
  private static final String SPRING_DATASOURCE_ALLARIAINTEGRATIONS =
      "spring.datasource.allaria-integrations";
  private static final String SPRING_DATASOURCE_BYMAS = "spring.datasource.by-mas";
  private static final String DISCORD_ID = "discord.id";
  private static final String DISCORD_TOKEN = "discord.token";
  private static final String ADMIN_TOKEN = "admin.token";
  private static final String FIREBASE_KEY = "firebase.key";
  private static final String FIREBASE_ADMIN_TOKEN = "firebase.admin-token";

  @Override
  public void initialize(ConfigurableApplicationContext applicationContext) {

    logger.info("Setting secrets properties.");

    Properties props = new Properties();

    logger.warn("Cannot load the secrets for bymas");
    logger.info("Trying to get properties from environment variables.");
    // DATABASE PROPERTIES.
    initializeDatabasePropertiesFromEnv(
        SPRING_DATASOURCE_ALLARIAMAS,
        "ALLARIAMAS_RDS_HOSTNAME",
        "ALLARIAMAS_RDS_DB_NAME",
        "ALLARIAMAS_RDS_USERNAME",
        "ALLARIAMAS_RDS_PASSWORD",
        props);
    initializeDatabasePropertiesFromEnv(
        SPRING_DATASOURCE_ALLARIAINTEGRATIONS,
        "ALLARIAINTEGRATIONS_RDS_HOSTNAME",
        "ALLARIAINTEGRATIONS_RDS_DB_NAME",
        "ALLARIAINTEGRATIONS_RDS_USERNAME",
        "ALLARIAINTEGRATIONS_RDS_PASSWORD",
        props);
    initializeDatabasePropertiesFromEnv(
        SPRING_DATASOURCE_BYMAS,
        "BYMAS_RDS_HOSTNAME",
        "BYMAS_RDS_DB_NAME",
        "BYMAS_RDS_USERNAME",
        "BYMAS_RDS_PASSWORD",
        props);
    props.put(DISCORD_ID, System.getenv("DISCORD_ID"));
    props.put(DISCORD_TOKEN, System.getenv("DISCORD_TOKEN"));
    props.put(ADMIN_TOKEN, System.getenv("ADMIN_TOKEN"));
    props.put(
        FIREBASE_KEY, (System.getenv("FIREBASE_KEY") != null) ? System.getenv("FIREBASE_KEY") : "");
    props.put(FIREBASE_ADMIN_TOKEN, System.getenv("ADMIN_TOKEN"));
    applicationContext
        .getEnvironment()
        .getPropertySources()
        .addFirst(new PropertiesPropertySource("LoadedSecret", props));
  }

  private void initializeDatabasePropertiesFromEnv(
      String prefix,
      String hostName,
      String dbName,
      String userName,
      String password,
      Properties props) {
    props.put(
        prefix + ".url",
        String.format("jdbc:postgresql://%s/%s", System.getenv(hostName), System.getenv(dbName)));
    props.put(prefix + ".username", System.getenv(userName));
    props.put(prefix + ".password", System.getenv(password));
  }
}
