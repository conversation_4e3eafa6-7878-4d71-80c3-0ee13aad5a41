package ar.com.daruma.bymas.portfolioPosition.application.find;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.*;

import ar.com.daruma.bymas.portfolioAccount.application.find.FindPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindRelatedAccounts;
import ar.com.daruma.bymas.portfolioAccount.domain.entities.errors.PortfolioAccountNotFoundException;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.application.find.FindMovementsThatAreSettledForDateAndPositions;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByFiltersAccountAndMaybeAsset;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers.PortfolioPositionResponseByGroupAndDescriptionMapper;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers.PortfolioPositionResponseByGroupMapper;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers.PortfolioPositionResponseMapper;
import ar.com.daruma.bymas.portfolioPositionValuation.application.find.FindBasePortfolioPositionValuationsForBasePositions;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.json.JsonWriteFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindCurrentPortfolioPositionsForAccountByFilters {

  private final FindsertPortfolioPositionByFiltersAccountAndMaybeAsset
      findsertPortfolioPositionByFiltersAccountAndMaybeAsset;

  private final FindPortfolioAssetByName findPortfolioAssetByName;
  private final PortfolioPositionResponseByGroupMapper portfolioPositionResponseByGroupMapper;

  private final PortfolioPositionResponseByGroupAndDescriptionMapper
      portfolioPositionResponseByGroupAndDescriptionMapper;

  private final PortfolioPositionResponseMapper portfolioPositionResponseMapper;
  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;

  private final FindBasePortfolioPositionValuationsForBasePositions
      findBasePortfolioPositionValuationsForBasePositions;
  private final FindMovementsThatAreSettledForDateAndPositions
      findMovementsThatAreSettledForDateAndPositions;
  private final FindRelatedAccounts findRelatedAccounts;

  @PersistenceContext private EntityManager entityManager;

  private static final List<String> balanceAssetNames =
      List.of(dolarMepName, pesoName, dolarCableName);

  @Autowired
  private void configureObjectMapper(ObjectMapper objectMapper) {
    objectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
    objectMapper.configure(JsonWriteFeature.WRITE_NUMBERS_AS_STRINGS.mappedFeature(), true);
  }

  private final ObjectMapper objectMapper;

  @Transactional(transactionManager = "byMasTransactionManager")
  public JsonNode findPosition(FindPositionByFiltersInput input) {
    log.info(
        "Finding portfolio positions for account: {} and filters: {}",
        input.getAccountId(),
        input.toLogStream());

    Optional<PortfolioAsset> maybeAsset = processAssetFilter(input);
    List<PortfolioAccountRelationships> relationships =
        findRelatedAccounts.findRelationships(input.getAccountId());
    List<PortfolioAccount> accounts = getFilteredAccounts(input, relationships);
    if (accounts.isEmpty()) {
      return createEmptyResponse(input);
    }
    List<BasePortfolioPosition> positions = getPortfolioPositions(input, accounts, maybeAsset);

    List<BasePortfolioPosition> filteredPositions =
        filterPositions(positions, input, relationships);

    Page<BasePortfolioPosition> pagedPositions = createPagedPositions(filteredPositions, input);

    Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper =
        getPositionValuations(pagedPositions, input);

    log.info(
        "Found {} portfolio positions for account: {} and filters: {}",
        pagedPositions.getTotalElements(),
        input.getAccountId(),
        input.toLogStream());

    return buildResponse(positionToValuationMapper, input, pagedPositions);
  }

  private Optional<PortfolioAsset> processAssetFilter(FindPositionByFiltersInput input) {
    return input
        .getAsset()
        .filter(
            asset -> {
              if (asset.equals("BALANCES")) {
                input.setAsset(Optional.empty());
                input.setRetrieveBalances(true);
                return false;
              }
              return true;
            })
        .map(findPortfolioAssetByName::find);
  }

  private List<PortfolioAccount> getFilteredAccounts(
      FindPositionByFiltersInput input, List<PortfolioAccountRelationships> relationships) {
    try {
      PortfolioAccount ownAccount =
          findPortfolioAccountByAccountId.findAccountByIdAndCompany(
              input.getAccountId(), input.getAccountCompany());
      List<PortfolioAccount> accounts =
          new ArrayList<>(
              relationships.stream()
                  .map(PortfolioAccountRelationships::getPortfolioAccount)
                  .toList());
      accounts.add(ownAccount);
      List<UUID> mirroredAccountIds = getMirroredAccountIds(relationships);
      return applyAccountFilters(accounts, input, mirroredAccountIds);

    } catch (PortfolioAccountNotFoundException ex) {
      return Collections.emptyList();
    }
  }

  private List<PortfolioAccount> applyAccountFilters(
      List<PortfolioAccount> accounts,
      FindPositionByFiltersInput input,
      List<UUID> mirroredAccountIds) {
    if (input.isOnlyAllariaMasCompany()) {
      return accounts.stream().filter(PortfolioAccount::isAllariaMasAccount).toList();
    }
    if (input.isOnlyAllariaCompany()) {
      return accounts.stream()
          .filter(
              account ->
                  account.isAllariaAccount()
                      || (input.hasToRetrieveBalances()
                          && mirroredAccountIds.contains(account.getId())))
          .toList();
    }
    return accounts;
  }

  private JsonNode createEmptyResponse(FindPositionByFiltersInput input) {
    return objectMapper.valueToTree(
        portfolioPositionResponseMapper.mapToResponse(Map.of(), input, 1, 0L));
  }

  private List<BasePortfolioPosition> getPortfolioPositions(
      FindPositionByFiltersInput input,
      List<PortfolioAccount> accounts,
      Optional<PortfolioAsset> maybeAsset) {

    List<BasePortfolioPosition> positions =
        findsertPortfolioPositionByFiltersAccountAndMaybeAsset.findsert(
            input, accounts, maybeAsset);
    if (input.isForFuture()) {
      List<PortfolioMovement> movements =
          findMovementsThatAreSettledForDateAndPositions
              .find((List<PortfolioPosition>) (List<?>) positions, input.getDate())
              .stream()
              .filter(movement -> !movement.isCancelledOrFailed() && movement.affectsPosition())
              .toList();
      applyFutureMovementsToPositions(positions, movements);
    }
    return positions;
  }

  private void applyFutureMovementsToPositions(
      List<BasePortfolioPosition> positions, List<PortfolioMovement> movements) {
    positions.forEach(entityManager::detach);
    movements.forEach(entityManager::detach);
    Map<BasePortfolioPosition.AssetAndAccountIdPair, List<PortfolioMovement>>
        movementsByAssetAndAccount =
            movements.stream()
                .collect(Collectors.groupingBy(PortfolioMovement::getAssetAndAccountIdPair));
    positions.forEach(
        basePortfolioPosition -> {
          List<PortfolioMovement> movementsForPosition =
              movementsByAssetAndAccount.getOrDefault(
                  basePortfolioPosition.getAssetAndAccountIdPair(), List.of());
          movementsForPosition.forEach(
              movement -> {
                // COMMENT NECESARIO: NO GUARDAR BALANCES EN PROXIMO CÓDIGO, SOLO ES PARA SIMULACIÓN
                // DE BALANCES FUTUROS
                PortfolioPosition position = (PortfolioPosition) basePortfolioPosition;
                position.cancelMovement(objectMapper, movement);
                movement.setMovementProcessState(movement.getMovementProcessState().nextState());
                position.applyMovement(objectMapper, movement);
              });
        });
  }

  private List<BasePortfolioPosition> filterPositions(
      List<BasePortfolioPosition> positions,
      FindPositionByFiltersInput input,
      List<PortfolioAccountRelationships> relationships) {
    // TODO : change logic to get balance from allariaMas if mirrored
    List<UUID> mirroredAccountIds = getMirroredAccountIds(relationships);
    List<BasePortfolioPosition> filteredPositions =
        positions.stream()
            .filter(
                position ->
                    shouldIncludePosition(position, mirroredAccountIds, input)
                        && isOnlyAllariaFilterForPosition(position, input))
            .toList();

    if (input.hasToRetrieveBalances()) {
      return filteredPositions;
    } else {
      return filteredPositions.stream().filter(position -> !position.hasZeroQuantities()).toList();
    }
  }

  private Boolean isOnlyAllariaFilterForPosition(
      BasePortfolioPosition position, FindPositionByFiltersInput input) {
    if (!input.isOnlyAllariaCompany()) {
      return true;
    }
    return position.getPortfolioAccount().isAllariaAccount()
        || position.getPortfolioAsset().isPesoAsset();
  }

  private List<UUID> getMirroredAccountIds(List<PortfolioAccountRelationships> relationships) {

    return relationships.stream()
        .filter(PortfolioAccountRelationships::isMirrored)
        .map(PortfolioAccountRelationships::getPortfolioAccountId)
        .toList();
  }

  private boolean shouldIncludePosition(
      BasePortfolioPosition position,
      List<UUID> mirroredAccountIds,
      FindPositionByFiltersInput input) {
    String assetName = position.getPortfolioAsset().getName();
    UUID accountId = position.getPortfolioAccount().getId();
    boolean accountIsMirrored = !mirroredAccountIds.isEmpty();
    if (input.hasToRetrieveBalances()) {
      boolean isPesoFromAllariaMasAndMirroredAccount =
          accountIsMirrored
              && position.getPortfolioAsset().isPesoAsset()
              && position.getPortfolioAccount().isAllariaMasAccount();
      boolean isNotPesoPosition = !position.getPortfolioAsset().isPesoAsset();
      return !accountIsMirrored || isPesoFromAllariaMasAndMirroredAccount || isNotPesoPosition;
    }
    return !balanceAssetNames.contains(assetName) || !mirroredAccountIds.contains(accountId);
  }

  private Page<BasePortfolioPosition> createPagedPositions(
      List<BasePortfolioPosition> positions, FindPositionByFiltersInput input) {
    Pageable pageable = PageRequest.of(input.getPage(), input.getCount());
    return paginateResults(positions, pageable);
  }

  private Page<BasePortfolioPosition> paginateResults(
      List<BasePortfolioPosition> basePortfolioPositions, Pageable pageable) {

    List<BasePortfolioPosition> sortedPositions =
        basePortfolioPositions.stream()
            .sorted(Comparator.comparing(BasePortfolioPosition::getId))
            .toList();
    int start = (int) pageable.getOffset();
    int end = Math.min((start + pageable.getPageSize()), sortedPositions.size());

    if (start > sortedPositions.size()) {
      return new PageImpl<>(Collections.emptyList(), pageable, sortedPositions.size());
    }

    List<BasePortfolioPosition> pageContent = sortedPositions.subList(start, end);
    return new PageImpl<>(pageContent, pageable, sortedPositions.size());
  }

  private Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> getPositionValuations(
      Page<BasePortfolioPosition> pagedPositions, FindPositionByFiltersInput input) {
    List<BasePortfolioPosition> positions = pagedPositions.getContent();

    if (input.hasToRetrieveBalances()) {
      return positions.stream()
          .collect(Collectors.toMap(position -> position, position -> new ArrayList<>()));
    }

    return findBasePortfolioPositionValuationsForBasePositions.find(positions).stream()
        .collect(
            Collectors.groupingBy(
                BasePortfolioPositionValuation::getBasePortfolioPosition, Collectors.toList()));
  }

  private JsonNode buildResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput input,
      Page<BasePortfolioPosition> pagedPositions) {

    Integer totalPages = pagedPositions.getTotalPages();
    Long totalElements = pagedPositions.getTotalElements();

    return input
        .getGroup()
        .map(
            group ->
                buildGroupedResponse(
                    positionToValuationMapper, input, group, totalPages, totalElements))
        .orElseGet(
            () ->
                buildStandardResponse(positionToValuationMapper, input, totalPages, totalElements));
  }

  private JsonNode buildGroupedResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput input,
      String group,
      Integer totalPages,
      Long totalElements) {

    Function<BasePortfolioPosition, String> groupBy = getGroupingFunction(group);

    if (input.getGroupBySubCategoryDescription()) {
      return objectMapper.valueToTree(
          portfolioPositionResponseByGroupAndDescriptionMapper.mapToResponse(
              positionToValuationMapper, input, groupBy, totalPages, totalElements));
    }

    return objectMapper.valueToTree(
        portfolioPositionResponseByGroupMapper.mapToResponse(
            positionToValuationMapper, input, groupBy, totalPages, totalElements));
  }

  private Function<BasePortfolioPosition, String> getGroupingFunction(String group) {
    return group.equals("INSTRUMENT")
        ? BasePortfolioPosition::getAssetSubCategory
        : BasePortfolioPosition::getAssetCurrency;
  }

  private JsonNode buildStandardResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput input,
      Integer totalPages,
      Long totalElements) {

    return objectMapper.valueToTree(
        portfolioPositionResponseMapper.mapToResponse(
            positionToValuationMapper, input, totalPages, totalElements));
  }
}
