package ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Position {

  @JsonProperty("account_id")
  private Integer accountId;

  private AssetInfo asset;

  @JsonProperty("agreement_quantity")
  private BigDecimal agreementQuantity;

  @JsonProperty("settlement_quantity")
  private BigDecimal settlementQuantity;

  @JsonProperty("available_quantity")
  private BigDecimal availableQuantity;

  @JsonProperty("locked_quantity")
  private BigDecimal lockedQuantity;

  private List<Valuation> valuations;

  @JsonProperty("last_updated_at")
  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
  private LocalDateTime lastUpdatedAt;

  @JsonProperty("agreement_percentage")
  private BigDecimal agreementPercentage;

  @JsonProperty("settlement_percentage")
  private BigDecimal settlementPercentage;

  private JsonNode metadata;
}
