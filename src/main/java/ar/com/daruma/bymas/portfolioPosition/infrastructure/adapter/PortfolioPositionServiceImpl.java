package ar.com.daruma.bymas.portfolioPosition.infrastructure.adapter;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.*;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.repository.PortfolioAssetRepository;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.domain.utils.SyncPortfolioPositionFromMovementsUtils;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.*;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.repository.PortfolioPositionHistoryRepository;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.repository.PortfolioPositionRepository;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.utils.PortfolioPositionUtils;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import ar.com.daruma.citadel.utils.CustomLogger;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortfolioPositionServiceImpl implements PortfolioPositionService {

  private final PortfolioPositionRepository portfolioPositionRepository;
  private final PortfolioPositionHistoryRepository historyRepository;
  private final PortfolioAssetRepository portfolioAssetRepository;
  private final WorkingDayService workingDayService;
  private final ObjectMapper objectMapper;
  private final SyncPortfolioPositionFromMovementsUtils syncPortfolioPositionFromMovementsUtils;
  private final PortfolioPositionUtils portfolioPositionUtils;
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;
  @PersistenceContext private EntityManager entityManager;

  private static final CustomLogger logger =
      CustomLogger.getLogger(PortfolioPositionServiceImpl.class);

  @Override
  public PortfolioPosition save(PortfolioPosition position) {
    return portfolioPositionRepository.save(position);
  }

  @Override
  public PortfolioPositionHistory saveHistory(PortfolioPositionHistory positionHistory) {
    return historyRepository.save(positionHistory);
  }

  @Override
  public Optional<PortfolioPosition> findById(UUID id) {
    return portfolioPositionRepository.findById(id);
  }

  @Override
  public List<PortfolioPosition> findAll() {
    return portfolioPositionRepository.findAll();
  }

  private PortfolioPosition createPositionByHistory(
      PortfolioAsset asset,
      PortfolioAccount account,
      Optional<PortfolioPositionHistory> positionHistory) {
    PortfolioPosition newPosition =
        positionHistory
            .map(PortfolioPosition::new)
            .orElseGet(() -> new PortfolioPosition(asset, account));
    return save(newPosition);
  }

  private PortfolioPosition createPositionByHistory(
      UUID assetId, UUID accountId, Optional<PortfolioPositionHistory> positionHistory) {
    PortfolioAsset asset = entityManager.getReference(PortfolioAsset.class, assetId);
    PortfolioAccount account = entityManager.getReference(PortfolioAccount.class, accountId);
    PortfolioPosition newPosition =
        positionHistory
            .map(PortfolioPosition::new)
            .orElseGet(() -> new PortfolioPosition(asset, account));
    return save(newPosition);
  }

  public Boolean hasHistoryPositionByDate(LocalDate date, UUID assetId, UUID accountId) {
    return historyRepository
        .findByDateAndPortfolioAssetIdAndPortfolioAccountId(date, assetId, accountId)
        .isPresent();
  }

  @Override
  public List<PortfolioPositionHistory> findsertHistoriesByPortfolios(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {
    Set<PortfolioPositionHistory> existingHistories =
        findExistingHistories(
            groupedPortfolios, portfolioAssetsByInvestmentId, portfolioAccountsByAccountNumber);
    Map<PortfolioPositionHistory.HistoryKey, PortfolioPositionHistory> existingHistoriesByKey =
        mapHistoriesByKey(existingHistories);
    Map<PortfolioPositionHistory.HistoryKey, List<Portfolio>> portfoliosByKey =
        mapPortfoliosByKey(
            groupedPortfolios, portfolioAssetsByInvestmentId, portfolioAccountsByAccountNumber);
    updateExistingHistories(existingHistories, portfoliosByKey);
    List<PortfolioPositionHistory> historiesToCreate =
        createMissingHistories(
            groupedPortfolios,
            existingHistoriesByKey,
            portfolioAssetsByInvestmentId,
            portfolioAccountsByAccountNumber);
    List<PortfolioPositionHistory> allHistories = new ArrayList<>(existingHistories);
    allHistories.addAll(historiesToCreate);
    return historyRepository.saveAll(allHistories);
  }

  @Override
  public void saveAll(List<PortfolioPosition> positionsToUpdate) {
    portfolioPositionRepository.saveAll(positionsToUpdate);
  }

  @Override
  public List<PortfolioPosition> findByPortfolioAccount(PortfolioAccount account) {
    return portfolioPositionRepository.findByPortfolioAccountId(account.getId());
  }

  private Set<PortfolioPositionHistory> findExistingHistories(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    List<UUID> accountIds =
        groupedPortfolios.values().stream()
            .map(portfolios -> portfolios.get(0).accountNumber())
            .distinct()
            .map(portfolioAccountsByAccountNumber::get)
            .map(PortfolioAccount::getId)
            .toList();
    List<UUID> assetIds =
        groupedPortfolios.values().stream()
            .map(portfolios -> portfolios.get(0).investmentId())
            .distinct()
            .map(portfolioAssetsByInvestmentId::get)
            .map(PortfolioAsset::getId)
            .toList();
    List<LocalDate> dates =
        groupedPortfolios.values().stream()
            .map(portfolios -> portfolios.get(0).getDate().toLocalDate())
            .distinct()
            .toList();
    Set<PortfolioPositionHistory.HistoryKey> historyKeys =
        groupedPortfolios.values().stream()
            .map(
                portfolios ->
                    buildHistoryKey(
                        portfolios.get(0),
                        portfolioAssetsByInvestmentId,
                        portfolioAccountsByAccountNumber))
            .collect(Collectors.toSet());
    Set<PortfolioPositionHistory> histories =
        historyRepository.findDistinctByPortfolioAccountIdInAndPortfolioAssetIdInAndDateIn(
            accountIds, assetIds, dates);
    histories =
        histories.stream()
            .filter(history -> historyKeys.contains(history.getHistoryKey()))
            .collect(Collectors.toSet());
    return histories;
  }

  private PortfolioPositionHistory.HistoryKey buildHistoryKey(
      Portfolio portfolio,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {
    return new PortfolioPositionHistory.HistoryKey(
        portfolioAssetsByInvestmentId.get(portfolio.investmentId()).getId(),
        portfolioAccountsByAccountNumber.get(portfolio.accountNumber()).getId(),
        portfolio.getDate().toLocalDate());
  }

  private Map<PortfolioPositionHistory.HistoryKey, PortfolioPositionHistory> mapHistoriesByKey(
      Set<PortfolioPositionHistory> histories) {

    return histories.stream()
        .collect(Collectors.toMap(PortfolioPositionHistory::getHistoryKey, history -> history));
  }

  private Map<PortfolioPositionHistory.HistoryKey, List<Portfolio>> mapPortfoliosByKey(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    return groupedPortfolios.values().stream()
        .flatMap(List::stream)
        .collect(
            Collectors.groupingBy(
                portfolio ->
                    createHistoryKeyFromPortfolio(
                        portfolio,
                        portfolioAssetsByInvestmentId,
                        portfolioAccountsByAccountNumber)));
  }

  private PortfolioPositionHistory.HistoryKey createHistoryKeyFromPortfolio(
      Portfolio portfolio,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    return new PortfolioPositionHistory.HistoryKey(
        portfolioAssetsByInvestmentId.get(portfolio.investmentId()).getId(),
        portfolioAccountsByAccountNumber.get(portfolio.accountNumber()).getId(),
        portfolio.getDate().toLocalDate());
  }

  private void updateExistingHistories(
      Set<PortfolioPositionHistory> existingHistories,
      Map<PortfolioPositionHistory.HistoryKey, List<Portfolio>> portfoliosByKey) {

    existingHistories.forEach(history -> updateHistoryQuantities(history, portfoliosByKey));
  }

  private void updateHistoryQuantities(
      PortfolioPositionHistory history,
      Map<PortfolioPositionHistory.HistoryKey, List<Portfolio>> portfoliosByKey) {

    PortfolioPositionHistory.HistoryKey key = history.getHistoryKey();
    List<Portfolio> portfolios = portfoliosByKey.get(key);

    Optional<Portfolio> liquidationPortfolio =
        portfolios.stream().filter(Portfolio::isLiquidation).findFirst();
    Optional<Portfolio> concertationPortfolio =
        portfolios.stream().filter(Portfolio::isConcertation).findFirst();
    liquidationPortfolio.ifPresent(portfolio -> history.setQuantities(portfolio.getQuantities()));
    concertationPortfolio.ifPresent(portfolio -> history.setQuantities(portfolio.getQuantities()));
  }

  private PortfolioQuantities extractQuantityByType(
      List<Portfolio> portfolios, Predicate<Portfolio> filter, PortfolioQuantitiesType type) {
    return portfolios.stream()
        .filter(filter)
        .findFirst()
        .map(Portfolio::getQuantities)
        .orElse(new PortfolioQuantities(type));
  }

  private List<PortfolioPositionHistory> createMissingHistories(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<PortfolioPositionHistory.HistoryKey, PortfolioPositionHistory> existingHistoriesByKey,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    List<List<Portfolio>> missingPortfolios =
        findMissingPortfolioGroups(
            groupedPortfolios,
            existingHistoriesByKey,
            portfolioAssetsByInvestmentId,
            portfolioAccountsByAccountNumber);

    return missingPortfolios.stream()
        .map(
            group ->
                createHistoryFromPortfolioGroup(
                    group, portfolioAssetsByInvestmentId, portfolioAccountsByAccountNumber))
        .toList();
  }

  private List<List<Portfolio>> findMissingPortfolioGroups(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<PortfolioPositionHistory.HistoryKey, PortfolioPositionHistory> existingHistoriesByKey,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    return groupedPortfolios.values().stream()
        .flatMap(Collection::stream)
        .filter(
            portfolio ->
                !existingHistoriesByKey.containsKey(
                    createHistoryKeyFromPortfolio(
                        portfolio,
                        portfolioAssetsByInvestmentId,
                        portfolioAccountsByAccountNumber)))
        .collect(
            Collectors.groupingBy(
                portfolio ->
                    portfolio.getPortfolioHistoryKey(
                        portfolioAssetsByInvestmentId, portfolioAccountsByAccountNumber)))
        .values()
        .stream()
        .toList();
  }

  private PortfolioPositionHistory createHistoryFromPortfolioGroup(
      List<Portfolio> group,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {

    Portfolio firstPortfolio = group.get(0);
    PortfolioAsset asset = portfolioAssetsByInvestmentId.get(firstPortfolio.investmentId());
    PortfolioAccount account = portfolioAccountsByAccountNumber.get(firstPortfolio.accountNumber());

    PortfolioQuantities settlementQuantities =
        extractQuantityByType(group, Portfolio::isLiquidation, PortfolioQuantitiesType.SETTLEMENT);
    PortfolioQuantities agreementQuantities =
        extractQuantityByType(group, Portfolio::isConcertation, PortfolioQuantitiesType.AGREEMENT);

    return new PortfolioPositionHistory(
        firstPortfolio.getDate().toLocalDate(),
        asset,
        account,
        agreementQuantities,
        settlementQuantities);
  }

  @Override
  public PortfolioPosition findsertByDateAndAssetAndAccountId(
      LocalDate date, PortfolioAsset asset, PortfolioAccount account) {
    return portfolioPositionRepository
        .findByPortfolioAssetIdAndPortfolioAccountId(asset.getId(), account.getId())
        .map(portfolio -> updatePositionIfOutdated(portfolio, date))
        .orElseGet(
            () -> {
              Optional<PortfolioPositionHistory> yesterdayPosition =
                  findMaybeHistoryByDateAndAssetAndAccountId(
                      workingDayService.lastAvailableDay(date, 1), asset.getId(), account.getId());
              return createPositionByHistory(asset, account, yesterdayPosition);
            });
  }

  @Override
  public PortfolioPosition findsertByDateAndAssetAndAccountId(
      LocalDate date, UUID assetId, UUID accountId) {
    return portfolioPositionRepository
        .findByPortfolioAssetIdAndPortfolioAccountId(assetId, accountId)
        .map(portfolio -> updatePositionIfOutdated(portfolio, date))
        .orElseGet(
            () -> {
              Optional<PortfolioPositionHistory> yesterdayPosition =
                  findMaybeHistoryByDateAndAssetAndAccountId(
                      workingDayService.lastAvailableDay(date, 1), assetId, accountId);
              return createPositionByHistory(assetId, accountId, yesterdayPosition);
            });
  }

  private List<PortfolioPosition> findsertByDateAndAssetAndAccountIds(
      LocalDate date, PortfolioAsset asset, Set<UUID> accountIds) {
    List<PortfolioPosition> positions =
        portfolioPositionRepository.findByPortfolioAssetIdAndPortfolioAccountIdIn(
            asset.getId(), accountIds);
    List<PortfolioPositionBalance> portfolioPositionBalances =
        updatePositionsByHistoryIfOutdatedWithoutSaving(positions, date);
    portfolioPositionBalanceService.saveAll(portfolioPositionBalances);
    saveAll(positions);
    return positions;
  }

  public List<PortfolioPosition> findsertBalancePositionsForAccount(
      LocalDate date, PortfolioAccount account) {
    List<PortfolioAsset> currencyAssets = findCurrencyAssets();
    List<PortfolioPosition> positions = findOrCreatePositions(account, date, currencyAssets);

    Set<PortfolioAsset> existingAssets =
        positions.stream()
            .map(BasePortfolioPosition::getPortfolioAsset)
            .collect(Collectors.toSet());

    List<PortfolioPosition> filledPositions = fillEmptyMetadataBalances(positions, existingAssets);
    List<PortfolioPosition> missingCurrencyPositions =
        createMissingCurrencyPositions(currencyAssets, existingAssets, account);

    return Stream.concat(filledPositions.stream(), missingCurrencyPositions.stream())
        .collect(Collectors.toList());
  }

  private List<PortfolioAsset> findCurrencyAssets() {
    return portfolioAssetRepository.findByNameIn(List.of(dolarMepName, pesoName, dolarCableName));
  }

  private List<PortfolioPosition> findOrCreatePositions(
      PortfolioAccount account, LocalDate date, List<PortfolioAsset> assets) {
    List<UUID> assetIds = assets.stream().map(PortfolioAsset::getId).toList();
    logger.info("Finding positions for account {} and assets {}", account.getId(), assetIds);
    List<PortfolioPosition> positions =
        portfolioPositionRepository
            .findByPortfolioAssetIdInAndPortfolioAccountId(assetIds, account.getId())
            .stream()
            .map(position -> updatePositionIfOutdated(position, date))
            .collect(Collectors.toCollection(ArrayList::new));

    if (positions.isEmpty()) {
      logger.info("No positions found for account {} and assets {}", account.getId(), assetIds);
      LocalDate fallbackDate = workingDayService.lastAvailableDay(date, 1);
      List<PortfolioPositionHistory> histories =
          historyRepository.findByDateAndPortfolioAssetIdInAndPortfolioAccountId(
              fallbackDate, assetIds, account.getId());
      logger.info("Found {} positions for {} assets on history", histories.size(), assetIds);
      positions.addAll(
          histories.stream()
              .map(
                  history ->
                      createPositionByHistory(
                          history.getPortfolioAsset(), account, Optional.of(history)))
              .toList());
    }

    return positions;
  }

  private List<PortfolioPosition> fillEmptyMetadataBalances(
      List<PortfolioPosition> positions, Set<PortfolioAsset> existingAssets) {
    return positions.stream()
        .peek(
            position -> {
              if (existingAssets.contains(position.getPortfolioAsset())
                  && position.hasNoBalances()) {
                position.generateOwnBalances(objectMapper);
              }
            })
        .collect(Collectors.toList());
  }

  private List<PortfolioPosition> createMissingCurrencyPositions(
      List<PortfolioAsset> allAssets,
      Set<PortfolioAsset> existingAssets,
      PortfolioAccount account) {
    List<UUID> existingAssetIds = existingAssets.stream().map(PortfolioAsset::getId).toList();
    logger.info("Existing asset IDs: {}", existingAssetIds);
    return allAssets.stream()
        .filter(asset -> !existingAssetIds.contains(asset.getId()))
        .map(
            asset -> {
              logger.info("Creating missing currency positions for {} asset", asset.getName());
              return new PortfolioPosition(asset, account, objectMapper);
            })
        .toList();
  }

  private List<PortfolioPositionHistory> findsertBalanceHistoryPositionsForAccount(
      LocalDate localDate, PortfolioAccount account) {
    List<PortfolioAsset> assets =
        portfolioAssetRepository.findByNameIn(List.of(dolarMepName, pesoName, dolarCableName));
    return historyRepository.findByDateAndPortfolioAssetIdInAndPortfolioAccountId(
        localDate, assets.stream().map(PortfolioAsset::getId).toList(), account.getId());
  }

  @Override
  public Optional<PortfolioPositionHistory> findMaybeHistoryByDateAndAssetAndAccountId(
      LocalDate date, UUID assetId, UUID accountId) {
    return historyRepository.findByDateAndPortfolioAssetIdAndPortfolioAccountId(
        date, assetId, accountId);
  }

  private Optional<PortfolioPositionHistory> findHistoryByDateAndAssetAndAccountId(
      LocalDate date, PortfolioAsset asset, PortfolioAccount account) {
    return historyRepository.findByDateAndPortfolioAssetIdAndPortfolioAccountId(
        date, asset.getId(), account.getId());
  }

  private Set<PortfolioPositionHistory> findHistoriesByDateAndAssetAccountsPair(
      LocalDate date, Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIds) {
    Set<UUID> assetIds =
        assetAndAccountIds.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAssetId)
            .collect(Collectors.toSet());
    Set<UUID> accountsIds =
        assetAndAccountIds.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAccountId)
            .collect(Collectors.toSet());
    Set<PortfolioPositionHistory> histories =
        historyRepository.findDistinctByDateAndPortfolioAssetIdInAndPortfolioAccountIdIn(
            date, assetIds, accountsIds);
    return histories.stream()
        .filter(history -> assetAndAccountIds.contains(history.getAssetAndAccountIdPair()))
        .collect(Collectors.toSet());
  }

  @Override
  public void update(PortfolioPosition porfolioPosition) {
    portfolioPositionRepository.save(porfolioPosition);
  }

  @Override
  public List<BasePortfolioPosition> findByFiltersAccountsAndMaybeAsset(
      FindPositionByFiltersInput filters,
      List<PortfolioAccount> accounts,
      Optional<PortfolioAsset> maybeAsset) {
    List<BasePortfolioPosition> basePortfolioPositions = new ArrayList<>();
    if (filters.isForTodayOrFuture()) {
      handleTodayFiltersWithAccounts(filters, accounts, maybeAsset, basePortfolioPositions);

    } else {
      // TODO check if needs optimizing
      accounts.forEach(
          account -> handleHistoricalFilters(filters, account, maybeAsset, basePortfolioPositions));
    }
    return basePortfolioPositions;
  }

  private void handleTodayFiltersWithAccounts(
      FindPositionByFiltersInput filters,
      List<PortfolioAccount> accounts,
      Optional<PortfolioAsset> maybeAsset,
      List<BasePortfolioPosition> basePortfolioPositions) {
    Stream<UUID> accountIds = accounts.stream().map(PortfolioAccount::getId);
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    if (maybeAsset.isPresent()) {
      List<PortfolioPosition> positions =
          findsertByDateAndAssetAndAccountIds(
              today, maybeAsset.get(), accountIds.collect(Collectors.toSet()));
      basePortfolioPositions.addAll(positions);
    } else {
      if (filters.hasToRetrieveBalances()) {
        // TODO fijarse si cambiar esto por performance:
        accounts.forEach(
            account ->
                basePortfolioPositions.addAll(findsertBalancePositionsForAccount(today, account)));
      } else {
        basePortfolioPositions.addAll(
            findsertAllPortfolioPositionsForAccounts(accountIds.toList()));
      }
    }
  }

  private void handleHistoricalFilters(
      FindPositionByFiltersInput filters,
      PortfolioAccount account,
      Optional<PortfolioAsset> maybeAsset,
      List<BasePortfolioPosition> basePortfolioPositions) {
    LocalDate lastAvailableDay =
        workingDayService.lastAvailableDay(filters.getDate().plusDays(1), 1);

    if (maybeAsset.isPresent()) {
      Optional<PortfolioPositionHistory> positionHistory =
          findMaybeHistoryByDateAndAssetAndAccountId(
              lastAvailableDay, maybeAsset.get().getId(), account.getId());
      positionHistory.ifPresent(basePortfolioPositions::add);
    } else {
      List<PortfolioPositionHistory> positionHistories;
      if (filters.hasToRetrieveBalances()) {
        positionHistories = findsertBalanceHistoryPositionsForAccount(lastAvailableDay, account);
      } else {
        positionHistories = findAllHistoryByDateAndAssetAndAccountId(lastAvailableDay, account);
      }
      basePortfolioPositions.addAll(positionHistories);
    }
  }

  @Override
  public List<PortfolioPosition> findByPortfolioAccountAndAssetsWithoutUpdating(
      PortfolioAccount account, Set<PortfolioAsset> assets) {
    return portfolioPositionRepository.findByPortfolioAccountAndPortfolioAssetIn(account, assets);
  }

  private List<PortfolioPositionHistory> findAllHistoryByDateAndAssetAndAccountId(
      LocalDate date, PortfolioAccount account) {
    return historyRepository.findAllByDateAndPortfolioAccountId(date, account.getId());
  }

  private PortfolioPosition updatePortfolioPosition(
      PortfolioPosition portfolioPosition, Optional<PortfolioPositionHistory> yesterdayPosition) {
    PortfolioPositionBalance portfolioPositionBalance =
        portfolioPosition.applyHistory(objectMapper, yesterdayPosition);
    portfolioPositionBalanceService.save(portfolioPositionBalance);
    return save(portfolioPosition);
  }

  private boolean positionIsOutdated(
      PortfolioPosition portfolioPosition,
      Optional<PortfolioPositionHistory> yesterdayPosition,
      LocalDate date) {
    return yesterdayPosition
            .map(position -> portfolioPosition.getLastSyncedAt().isBefore(position.getCreatedAt()))
            .orElse(false)
        || !portfolioPosition.getLastSyncedAt().toLocalDate().isEqual(date);
  }

  private boolean positionIsOutdated(
      PortfolioPosition position,
      Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionHistory>
          existingHistoriesByKey,
      LocalDate date) {
    Optional<PortfolioPositionHistory> history =
        Optional.ofNullable(
            existingHistoriesByKey.getOrDefault(position.getAssetAndAccountIdPair(), null));
    return positionIsOutdated(position, history, date);
  }

  private boolean positionLastUpdatedAtIsBeforeDate(PortfolioPosition position, LocalDate date) {
    return position.getLastUpdatedAt().toLocalDate().isBefore(date);
  }

  @Override
  public List<PortfolioPosition> findByPortfolioPositionsByAssetAndAccountIdPairs(
      Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIdPairs) {
    Set<UUID> assetIds =
        assetAndAccountIdPairs.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAssetId)
            .collect(Collectors.toSet());
    Set<UUID> accountIds =
        assetAndAccountIdPairs.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAccountId)
            .collect(Collectors.toSet());
    List<PortfolioPosition> positions =
        portfolioPositionRepository.findByPortfolioAssetIdInAndPortfolioAccountIdIn(
            assetIds, accountIds);
    positions =
        positions.stream()
            .filter(
                position -> assetAndAccountIdPairs.contains(position.getAssetAndAccountIdPair()))
            .toList();
    return positions;
  }

  private List<PortfolioPositionBalance> updatePositionsWithoutSaving(
      List<PortfolioPosition> portfolioPositions,
      Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionHistory>
          existingHistoriesByKey) {
    List<PortfolioPositionBalance> balancesToSave = new ArrayList<>();
    portfolioPositions.forEach(
        position -> {
          PortfolioPositionBalance portfolioPositionBalance =
              portfolioPositionUtils.updatePositionByHistoryWithoutSaving(
                  position, existingHistoriesByKey);
          balancesToSave.add(portfolioPositionBalance);
        });
    syncPortfolioPositionFromMovementsUtils.updatePositionsByMovementsWithImpactTodayWithoutSaving(
        portfolioPositions);
    return balancesToSave;
  }

  @Override
  public List<PortfolioPositionBalance> updatePositionsByHistoryIfOutdatedWithoutSaving(
      List<PortfolioPosition> portfolioPositions, LocalDate date) {
    LocalDate lastAvailableDay = workingDayService.lastAvailableDay(date, 1);
    Set<PortfolioPositionHistory> histories =
        findHistoriesByDateAndAssetAccountsPair(
            lastAvailableDay,
            portfolioPositions.stream()
                .map(PortfolioPosition::getAssetAndAccountIdPair)
                .collect(Collectors.toSet()));
    Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionHistory>
        existingHistoriesByKey =
            histories.stream()
                .collect(
                    Collectors.toMap(
                        PortfolioPositionHistory::getAssetAndAccountIdPair,
                        history -> history,
                        (existing, replacement) -> existing));
    List<PortfolioPosition> outdatedPositions =
        portfolioPositions.stream()
            .filter(
                portfolioPosition ->
                    positionIsOutdated(portfolioPosition, existingHistoriesByKey, date))
            .toList();
    List<PortfolioPosition> positionsUpToDate =
        portfolioPositions.stream()
            .filter(
                portfolioPosition ->
                    !positionIsOutdated(portfolioPosition, existingHistoriesByKey, date))
            .toList();
    List<PortfolioPositionBalance> portfolioPositionBalance =
        updatePositionsWithoutSaving(outdatedPositions, existingHistoriesByKey);
    List<PortfolioPosition> allPositions = new ArrayList<>(outdatedPositions);
    allPositions.addAll(positionsUpToDate);
    return portfolioPositionBalance;
  }

  @Override
  public List<PortfolioPosition> findByPortfolioAssetIdInAndMaybePortfolioAccount(
      List<UUID> portfolioAssetIds, Optional<PortfolioAccount> maybePortfolioAccount) {
    return maybePortfolioAccount
        .map(
            account ->
                portfolioPositionRepository.findByPortfolioAssetIdInAndPortfolioAccountId(
                    portfolioAssetIds, account.getId()))
        .orElseGet(() -> portfolioPositionRepository.findByPortfolioAssetIdIn(portfolioAssetIds));
  }

  @Override
  public List<PortfolioPosition> findByMaybePortfolioAccount(
      Optional<PortfolioAccount> maybeAccount) {
    return maybeAccount
        .map(account -> portfolioPositionRepository.findByPortfolioAccountId(account.getId()))
        .orElseGet(portfolioPositionRepository::findAll);
  }

  @Override
  public Set<PortfolioPositionHistory> findHistoriesByPortfolioPositions(
      LocalDate lastWorkingDate, List<PortfolioPosition> positions) {
    return findHistoriesByDateAndAssetAccountsPair(
        lastWorkingDate,
        positions.stream()
            .map(PortfolioPosition::getAssetAndAccountIdPair)
            .collect(Collectors.toSet()));
  }

  private List<PortfolioPosition> findsertAllPortfolioPositionsForAccounts(List<UUID> accountIds) {
    LocalDate date = BuenosAiresTime.nowAsLocalDate();
    LocalDate lastAvailableDay = workingDayService.lastAvailableDay(date, 1);
    List<PortfolioPosition> positions =
        new ArrayList<>(portfolioPositionRepository.findDistinctByPortfolioAccountIdIn(accountIds));
    List<PortfolioPositionBalance> portfolioPositionBalance =
        updatePositionsByHistoryIfOutdatedWithoutSaving(positions, date);
    portfolioPositionBalanceService.saveAll(portfolioPositionBalance);
    List<PortfolioAsset> assetsWithExistingPositions =
        new ArrayList<>(positions.stream().map(PortfolioPosition::getPortfolioAsset).toList());
    List<PortfolioPositionHistory> portfolioHistories;
    if (assetsWithExistingPositions.isEmpty()) {
      portfolioHistories =
          historyRepository.findDistinctByPortfolioAccountIdInAndDate(accountIds, lastAvailableDay);
    } else {
      portfolioHistories =
          historyRepository.findDistinctByPortfolioAccountIdInAndDateAndPortfolioAssetNotIn(
              accountIds, lastAvailableDay, assetsWithExistingPositions);
    }
    List<PortfolioPosition> newPositions =
        portfolioHistories.stream()
            .map(
                history ->
                    portfolioPositionUtils.createPositionByHistoryWithoutSaving(
                        history.getPortfolioAsset(),
                        history.getPortfolioAccount(),
                        Optional.of(history)))
            .toList();
    positions.addAll(newPositions);
    return portfolioPositionRepository.saveAll(positions);
  }

  private PortfolioPosition updatePositionIfOutdated(
      PortfolioPosition portfolioPosition, LocalDate date) {
    Optional<PortfolioPositionHistory> yesterdayPosition =
        findHistoryByDateAndAssetAndAccountId(
            workingDayService.lastAvailableDay(date, 1),
            portfolioPosition.getPortfolioAsset(),
            portfolioPosition.getPortfolioAccount());
    boolean isPositionOutdated =
        yesterdayPosition
                .map(
                    position ->
                        portfolioPosition.getLastSyncedAt().isBefore(position.getCreatedAt()))
                .orElse(false)
            || !portfolioPosition.getLastSyncedAt().toLocalDate().isEqual(date);
    if (!isPositionOutdated) {
      return portfolioPosition;
    }
    if (portfolioPosition.getLastUpdatedAt().toLocalDate().isBefore(date)) {
      return updatePortfolioPosition(portfolioPosition, yesterdayPosition);
    }
    PortfolioPositionBalance portfolioPositionBalance =
        portfolioPosition.applyHistory(objectMapper, yesterdayPosition);
    List<PortfolioPositionBalance> balances =
        syncPortfolioPositionFromMovementsUtils
            .updatePositionsByMovementsWithImpactTodayWithoutSaving(List.of(portfolioPosition));
    balances.add(portfolioPositionBalance);
    portfolioPositionBalanceService.saveAll(balances);
    return save(portfolioPosition);
  }

  @Override
  public List<PortfolioPositionHistory> findHistoriesByDateAndMaybeAccount(
      LocalDate date, Optional<PortfolioAccount> maybeAccount) {
    return maybeAccount
        .map(account -> historyRepository.findByDateAndPortfolioAccountId(date, account.getId()))
        .orElseGet(() -> historyRepository.findByDate(date));
  }
}
