package ar.com.daruma.bymas.portfolioPosition.infrastructure.entities;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@EqualsAndHashCode(callSuper = true)
@Table(name = "portfolio_position_history")
public class PortfolioPositionHistory extends BasePortfolioPosition {

  @Column(nullable = false)
  private LocalDate date;

  @EqualsAndHashCode.Exclude
  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  public PortfolioPositionHistory() {
    super();
  }

  public PortfolioPositionHistory(
      LocalDate date,
      PortfolioAsset asset,
      PortfolioAccount portfolioAccount,
      PortfolioQuantities agreementQuantities,
      PortfolioQuantities settlementQuantities) {
    super();
    this.setPortfolioAccount(portfolioAccount);
    this.setPortfolioAsset(asset);
    this.setQuantities(agreementQuantities);
    this.setQuantities(settlementQuantities);
    this.date = date;
    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  public LocalDateTime getLastUpdatedAt() {
    return this.date.atStartOfDay();
  }

  @Override
  public boolean isHistory() {
    return true;
  }

  @Override
  public BigDecimal getLockedQuantity() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getAvailableQuantity() {
    return BigDecimal.ZERO;
  }

  public HistoryKey getHistoryKey() {
    return new HistoryKey(getPortfolioAsset().getId(), getPortfolioAccount().getId(), getDate());
  }

  public void setQuantities(PortfolioQuantities quantities) {
    if (quantities.isAgreement()) {
      this.setAgreementQuantity(quantities.getQuantity());
    } else {
      this.setSettlementQuantity(quantities.getQuantity());
    }
  }

  public record HistoryKey(UUID portfolioAssetId, UUID portfolioAccountId, LocalDate date) {}
}
