package ar.com.daruma.bymas.portfolioPosition.application.findsert;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import java.util.List;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindsertPortfolioPositionByFiltersAccountAndMaybeAsset {

  @Autowired private PortfolioPositionService portfolioPositionService;

  private static final Logger logger =
      LogManager.getLogger(FindsertPortfolioPositionByFiltersAccountAndMaybeAsset.class);

  public List<BasePortfolioPosition> findsert(
      FindPositionByFiltersInput filters,
      List<PortfolioAccount> accounts,
      Optional<PortfolioAsset> maybeAsset) {
    logger.info(logMessage(filters, accounts));
    List<BasePortfolioPosition> allPositions =
        portfolioPositionService.findByFiltersAccountsAndMaybeAsset(filters, accounts, maybeAsset);

    logger.info("{} portfolio positions found or inserted", allPositions.size());
    return allPositions;
  }

  private String logMessage(FindPositionByFiltersInput filters, List<PortfolioAccount> accounts) {
    String asset = filters.getAsset().map(assetName -> "asset: %s").orElse("all assets");
    String accountIds = accounts.stream().map(PortfolioAccount::getId).toList().toString();
    if (filters.isForTodayOrFuture()) {
      return String.format(
          "Finding and inserting portfolio positions for today or future dates, for %s and accounts: %s, grouped by: %s",
          asset, accountIds, filters.getGroup());
    }
    return String.format(
        "Finding portfolio positions with date: %s,for %s and account: %s, grouped by: %s",
        filters.getDate(), asset, accountIds, filters.getGroup());
  }
}
