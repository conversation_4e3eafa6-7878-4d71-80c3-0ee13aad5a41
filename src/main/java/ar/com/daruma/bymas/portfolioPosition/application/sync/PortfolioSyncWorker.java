package ar.com.daruma.bymas.portfolioPosition.application.sync;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.application.FindPortfoliosByDate;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioSyncWorker extends DiscordSender {

  private final FindPortfoliosByDate findPortfoliosByDate;

  private final SyncPortfolioByBatch syncPortfolioByBatch;

  private static final int FIRST_PAGE = 0;
  private static final int BATCH_SIZE = 200000;

  @Transactional(transactionManager = "allariaIntegrationsTransactionManager")
  public void sync(LocalDateTime date, Optional<Integer> accountId) {
    notifyDiscord(
        "Starting portfolio history positions from Allaria integrations to ByMas sync"
            + (accountId.map(integer -> " for accountId: " + integer).orElse("")));
    try {
      Pageable pageable = PageRequest.of(FIRST_PAGE, BATCH_SIZE);
      Page<Portfolio> portfoliosToProcess;
      BigDecimal batchesDone = BigDecimal.ONE;
      do {
        portfoliosToProcess = findPortfoliosByDate.find(date, accountId, pageable);
        BigDecimal batchesUntilComplete = BigDecimal.valueOf(portfoliosToProcess.getTotalPages());
        BigDecimal percentageDone =
            batchesDone
                .multiply(BigDecimal.valueOf(100))
                .divide(batchesUntilComplete, bigDecimalScale, RoundingMode.HALF_UP);
        if (portfoliosToProcess.hasContent()) {
          syncPortfolioByBatch.sync(portfoliosToProcess.getContent());
          notifyDiscord(
              "Successfully processed batch %s of portfolios, percentage done: %s "
                      .formatted(batchesDone.toString(), percentageDone)
                  + "%");
        }
        batchesDone = batchesDone.add(BigDecimal.ONE);
        pageable = portfoliosToProcess.nextPageable();
      } while (portfoliosToProcess.hasNext());
      notifyDiscord(
          "Portfolio history positions from Allaria integrations to ByMas sync for process finished successfully"
              + (accountId.map(integer -> " for accountId: " + integer).orElse("")));
    } catch (Exception e) {
      log.error("Error syncing portfolios: {}", e.getMessage(), e);
      notifyDiscord(
          "Error syncing portfolio history positions from Allaria integrations to ByMas: "
              + e.getMessage());
    }
  }
}
