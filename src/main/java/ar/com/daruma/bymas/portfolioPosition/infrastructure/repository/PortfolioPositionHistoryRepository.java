package ar.com.daruma.bymas.portfolioPosition.infrastructure.repository;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import java.time.LocalDate;
import java.util.*;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioPositionHistoryRepository
    extends JpaRepository<PortfolioPositionHistory, UUID> {
  List<PortfolioPositionHistory> findByPortfolioAccountId(UUID accountId);

  List<PortfolioPositionHistory> findByPortfolioAssetId(UUID assetId);

  List<PortfolioPositionHistory> findByDateBetween(LocalDate startDate, LocalDate endDate);

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  Optional<PortfolioPositionHistory> findByDateAndPortfolioAssetIdAndPortfolioAccountId(
      LocalDate date, UUID assetId, UUID portfolioAccountId);

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  List<PortfolioPositionHistory> findByPortfolioAccountIdAndDateAndPortfolioAssetNotIn(
      UUID portfolioAccount_id, LocalDate date, Collection<PortfolioAsset> portfolioAsset);

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  List<PortfolioPositionHistory> findByPortfolioAccountIdAndDate(
      UUID portfolioAccountId, LocalDate date);

  @Query(
      "SELECT h FROM PortfolioPositionHistory h WHERE h.portfolioAccount.id = :accountId "
          + "AND h.date = :date "
          + "AND h.portfolioAsset NOT IN :assets "
          + "AND h.portfolioAsset.category = :group")
  List<PortfolioPositionHistory> findByPortfolioAccountIdAndDateAndPortfolioAssetNotInAndGroup(
      @Param("accountId") UUID accountId,
      @Param("date") LocalDate date,
      @Param("assets") List<PortfolioAsset> assets,
      @Param("group") String group);

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  List<PortfolioPositionHistory> findAllByDateAndPortfolioAccountId(LocalDate date, UUID id);

  List<PortfolioPositionHistory> findByDateAndPortfolioAssetIdInAndPortfolioAccountId(
      LocalDate date, List<UUID> list, UUID id);

  Set<PortfolioPositionHistory> findDistinctByPortfolioAccountIdInAndPortfolioAssetIdInAndDateIn(
      List<UUID> accountIds, List<UUID> assetIds, List<LocalDate> dates);

  Set<PortfolioPositionHistory> findDistinctByDateAndPortfolioAssetIdInAndPortfolioAccountIdIn(
      LocalDate date, Set<UUID> assets, Set<UUID> accounts);

  List<PortfolioPositionHistory> findDistinctByPortfolioAccountIdInAndDate(
      List<UUID> accountIds, LocalDate lastAvailableDay);

  List<PortfolioPositionHistory> findDistinctByPortfolioAccountIdInAndDateAndPortfolioAssetNotIn(
      List<UUID> accountIds,
      LocalDate lastAvailableDay,
      List<PortfolioAsset> assetsWithExistingPositions);

  List<PortfolioPositionHistory> findByDate(LocalDate date);

  List<PortfolioPositionHistory> findByDateAndPortfolioAccountId(LocalDate date, UUID accountId);
}
