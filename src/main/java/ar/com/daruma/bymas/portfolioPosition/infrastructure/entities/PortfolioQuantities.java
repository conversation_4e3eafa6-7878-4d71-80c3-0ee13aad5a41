package ar.com.daruma.bymas.portfolioPosition.infrastructure.entities;

import java.math.BigDecimal;
import lombok.*;

@Setter
@Getter
@AllArgsConstructor
public class PortfolioQuantities {

  private BigDecimal quantity;

  private PortfolioQuantitiesType type;

  public PortfolioQuantities(PortfolioQuantitiesType type) {
    this.quantity = BigDecimal.ZERO;
    this.type = type;
  }

  public boolean isAgreement() {
    return this.type.isAgreement();
  }
}
