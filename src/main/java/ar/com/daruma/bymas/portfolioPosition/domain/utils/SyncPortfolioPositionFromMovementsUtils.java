package ar.com.daruma.bymas.portfolioPosition.domain.utils;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.portfolioMovement.application.find.FindImpactingMovementsForPositions;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SyncPortfolioPositionFromMovementsUtils extends DiscordSender {

  private final ObjectMapper objectMapper;
  private final FindImpactingMovementsForPositions findImpactingMovementsForPositions;
  private static final Integer numberOfPortfoliosToLog = 300;
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;

  public List<PortfolioPositionBalance> updatePositionsByMovementsWithImpactTodayWithoutSaving(
      List<PortfolioPosition> portfolioPositions) {
    Set<PortfolioMovement> movements =
        new HashSet<>(
            findImpactingMovementsForPositions.find(portfolioPositions).stream()
                .filter(movement -> !movement.isCancelledOrFailed() && movement.affectsPosition())
                .toList());
    return updatePositionsByMovementsWithoutSaving(portfolioPositions, movements);
  }

  private List<PortfolioPositionBalance> updatePositionsByMovementsWithoutSaving(
      List<PortfolioPosition> portfolioPositions, Set<PortfolioMovement> movements) {
    boolean shouldLog = portfolioPositions.size() >= numberOfPortfoliosToLog;
    if (shouldLog) {
      notifyDiscord(
          "Starting position update for "
              + portfolioPositions.size()
              + " positions with "
              + movements.size()
              + " movements");
    }
    Map<BasePortfolioPosition.AssetAndAccountIdPair, List<PortfolioMovement>> movementsByKey =
        movements.stream()
            .collect(
                Collectors.groupingBy(
                    PortfolioMovement::getAssetAndAccountIdPair, Collectors.toList()));
    BigDecimal processedCount = BigDecimal.ZERO;
    BigDecimal totalPositionsBD = BigDecimal.valueOf(portfolioPositions.size());
    Set<Integer> loggedPercentages = new HashSet<>();

    List<PortfolioPositionBalance> portfolioPositionBalance = new ArrayList<>();

    portfolioPositions.forEach(
        portfolioPosition -> {
          List<PortfolioPositionBalance> portfolioPositionBalancesSync =
              sync(
                  portfolioPosition,
                  movementsByKey.getOrDefault(
                      portfolioPosition.getAssetAndAccountIdPair(), List.of()));
          portfolioPositionBalance.addAll(portfolioPositionBalancesSync);
          if (shouldLog) {
            processLogging(processedCount, totalPositionsBD, loggedPercentages);
          }
        });
    if (shouldLog) {
      notifyDiscord(
          "Completed position update: 100% complete ("
              + totalPositionsBD
              + "/"
              + totalPositionsBD
              + " positions)");
    }
    return portfolioPositionBalance;
  }

  private void processLogging(
      BigDecimal processedCount, BigDecimal totalPositionsBD, Set<Integer> loggedPercentages) {
    processedCount = processedCount.add(BigDecimal.ONE);
    BigDecimal progressPercentage =
        processedCount
            .multiply(BigDecimal.valueOf(100))
            .divide(totalPositionsBD, bigDecimalScale, RoundingMode.HALF_UP);
    int percentage = progressPercentage.intValue();
    int loggingPercentage = (percentage / 20) * 20; // Round down to nearest 20%
    if ((loggingPercentage == 20
            || loggingPercentage == 40
            || loggingPercentage == 60
            || loggingPercentage == 80)
        && !loggedPercentages.contains(loggingPercentage)) {
      notifyDiscord(
          "Progress: "
              + loggingPercentage
              + "% complete ("
              + processedCount.intValue()
              + "/"
              + totalPositionsBD
              + " positions)");
      loggedPercentages.add(loggingPercentage);
    }
  }

  private List<PortfolioPositionBalance> sync(
      PortfolioPosition position, List<PortfolioMovement> movements) {
    log.info(
        "Syncing position: {} by history and {} movements", position.getId(), movements.size());
    if (!movements.isEmpty()) {
      position.setAgreementQuantity(position.getSettlementQuantity());
    }
    List<PortfolioPositionBalance> portfolioPositionBalances = new ArrayList<>();
    movements.forEach(
        movement -> {
          PortfolioPositionBalance applyBalance = position.applyMovement(objectMapper, movement);
          portfolioPositionBalances.add(applyBalance);
        });
    log.info("Position {} synced successfully", position.getId());
    return portfolioPositionBalances;
  }
}
