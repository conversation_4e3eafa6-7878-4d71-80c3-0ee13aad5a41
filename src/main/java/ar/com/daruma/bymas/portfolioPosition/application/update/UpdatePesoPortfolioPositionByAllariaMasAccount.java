package ar.com.daruma.bymas.portfolioPosition.application.update;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationPesoUtils.pesoName;

import ar.com.daruma.bymas.allariaMas.accounts.domain.AllariaMasAccount;
import ar.com.daruma.bymas.allariaMas.settlement.application.find.FindMoneyMarketSettlementForPersonType;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities.Settlement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByDateAndAssetAndAccount;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.application.update.UpdatePortfolioPositionValuationForPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdatePesoPortfolioPositionByAllariaMasAccount {

  @Autowired private PortfolioPositionService portfolioPositionService;

  @Autowired
  private FindsertPortfolioPositionByDateAndAssetAndAccount
      findsertPortfolioPositionByDateAndAssetAndAccount;

  @Autowired private ObjectMapper objectMapper;

  @Autowired
  private UpdatePortfolioPositionValuationForPosition updatePortfolioPositionValuationForPosition;

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  @Autowired private FindMoneyMarketSettlementForPersonType findMoneyMarketSettlementForPersonType;
  private static final Logger logger =
      LogManager.getLogger(UpdatePesoPortfolioPositionByAllariaMasAccount.class);

  public void update(
      AllariaMasAccount account,
      PortfolioAccount portfolioAccount,
      Optional<Settlement> maybeMoneyMarketSettlement) {
    logger.info(
        "Updating portfolioPosition for peso asset and portfolio account: {} by an update to account with id {} balance",
        portfolioAccount.getId(),
        account.getId());
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    PortfolioAsset pesoAsset = findPortfolioAssetByName.find(pesoName);
    PortfolioPosition porfolioPosition =
        findsertPortfolioPositionByDateAndAssetAndAccount.findsert(
            now, pesoAsset, portfolioAccount);
    Settlement moneyMarketSettlement =
        maybeMoneyMarketSettlement.orElseGet(
            () -> findMoneyMarketSettlementForPersonType.find(portfolioAccount.getPersonType()));
    porfolioPosition.applyAllariaMasAccount(
        objectMapper, account, moneyMarketSettlement.getShareValue());
    portfolioPositionService.update(porfolioPosition);
    updatePortfolioPositionValuationForPosition.update(porfolioPosition);
    logger.info("portfolioPosition with id {} successfully updated", porfolioPosition.getId());
  }
}
