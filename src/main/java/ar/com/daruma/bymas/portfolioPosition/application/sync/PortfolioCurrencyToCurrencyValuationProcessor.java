package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioCurrencyValuation.application.find.FindsertPortfolioCurrencyValuationsAllariaIntegrationsCurrencyValuations;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioCurrencyToCurrencyValuationProcessor {
  private final FindsertPortfolioCurrencyValuationsAllariaIntegrationsCurrencyValuations
      findsertPortfolioCurrencyValuationsAllariaIntegrationsCurrencyValuations;

  public Map<Integer, PortfolioCurrencyValuation> process(List<Portfolio> portfoliosToProcess) {
    log.info(
        "Processing portfolio currency valuations for {} portfolios", portfoliosToProcess.size());
    Set<CurrencyValuation> currencyValuations =
        portfoliosToProcess.stream()
            .map(Portfolio::getCurrencyValuation)
            .collect(Collectors.toSet());
    log.info("Found {} unique currency valuations", currencyValuations.size());
    Map<String, CurrencyValuation> codeToCurrencyValuation =
        currencyValuations.stream()
            .collect(
                Collectors.toMap(
                    CurrencyValuation::getCode, currencyValuation -> currencyValuation));
    List<PortfolioCurrencyValuation> portfolioCurrencyValuations =
        findsertPortfolioCurrencyValuationsAllariaIntegrationsCurrencyValuations.findsert(
            currencyValuations);
    return portfolioCurrencyValuations.stream()
        .collect(
            Collectors.toMap(
                currencyValuation ->
                    codeToCurrencyValuation.get(currencyValuation.getCode()).getId(),
                currencyValuation -> currencyValuation));
  }
}
