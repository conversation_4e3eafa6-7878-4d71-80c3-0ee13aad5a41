package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.movixEvent.domain.accountShares.entities.AccountShares;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByDateAndAssetAndAccount;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.application.update.UpdatePortfolioPositionValuationForPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.time.LocalDate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdatePortfolioPositionByAccountShares {

  @Autowired private PortfolioPositionService portfolioPositionService;

  @Autowired
  private FindsertPortfolioPositionByDateAndAssetAndAccount
      findsertPortfolioPositionByDateAndAssetAndAccount;

  @Autowired
  private UpdatePortfolioPositionValuationForPosition updatePortfolioPositionValuationForPosition;

  private static final Logger logger =
      LogManager.getLogger(UpdatePortfolioPositionByAccountShares.class);

  public void update(
      AccountShares accountShares, PortfolioAccount portfolioAccount, PortfolioAsset asset) {
    logger.info(
        "Updating portfolioPosition for asset: {} and account: {} by account shares",
        asset.getId(),
        portfolioAccount.getId());
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    PortfolioPosition portfolioPosition =
        findsertPortfolioPositionByDateAndAssetAndAccount.findsert(now, asset, portfolioAccount);
    portfolioPosition.applyAccountShares(accountShares);
    portfolioPositionService.update(portfolioPosition);
    updatePortfolioPositionValuationForPosition.update(portfolioPosition);
    logger.info("portfolioPosition with id {} successfully updated", portfolioPosition.getId());
  }
}
