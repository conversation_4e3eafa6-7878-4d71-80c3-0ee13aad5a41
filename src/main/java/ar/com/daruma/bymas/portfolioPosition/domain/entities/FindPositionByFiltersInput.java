package ar.com.daruma.bymas.portfolioPosition.domain.entities;

import ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class FindPositionByFiltersInput {

  @NotNull private Integer accountId;
  @NotNull private PortfolioCompany accountCompany;
  private Optional<String> asset;

  private LocalDate date;

  private Optional<String> group;
  private Optional<PortfolioCompany> company;

  private Integer page;

  private Integer count;
  private Boolean retrieveBalances = false;

  private Boolean groupBySubCategoryDescription;

  public Boolean isForTodayOrFuture() {
    return !date.isBefore(BuenosAiresTime.nowAsLocalDate());
  }

  public Boolean isForFuture() {
    return date.isAfter(BuenosAiresTime.nowAsLocalDate());
  }

  public FindPositionByFiltersInput(
      Integer accountId,
      String accountCompany,
      Optional<String> asset,
      LocalDate date,
      Optional<String> group,
      Optional<String> maybeCompany,
      Integer page,
      Integer count,
      Boolean groupBySubCategoryDescription) {
    this.accountId = accountId;
    this.accountCompany = ByMasCompanySecrets.fromString(accountCompany);
    this.asset = asset;
    this.date = date;
    this.group = group;
    this.company = maybeCompany.map(ByMasCompanySecrets::fromString);
    this.page = page;
    this.count = count;
    this.groupBySubCategoryDescription = groupBySubCategoryDescription;
  }

  public Boolean isOnlyAllariaCompany() {
    return company.map(ByMasCompanySecrets.allariaCompany::equals).orElse(false);
  }

  public Boolean hasToRetrieveBalances() {
    return retrieveBalances;
  }

  public Boolean isOnlyAllariaMasCompany() {
    return company.map(ByMasCompanySecrets.allariaMasCompany::equals).orElse(false);
  }

  public String toLogStream() {
    return String.format(
        "accountId = %d, asset = %s, date = %s, group = %s, page = %d, count = %d, company = %s",
        accountId, asset.orElse("all assets"), date, group.orElse("none"), page, count, company);
  }
}
