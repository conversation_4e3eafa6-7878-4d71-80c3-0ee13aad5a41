package ar.com.daruma.bymas.portfolioPosition.presentation.controller;

import ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets;
import ar.com.daruma.bymas.portfolioPosition.application.find.FindCurrentPortfolioPositionsForAccountByFilters;
import ar.com.daruma.bymas.portfolioPosition.application.sync.AsyncSyncPortfolioPositionFromMovementsWithImpactToday;
import ar.com.daruma.bymas.portfolioPosition.application.sync.SyncPortfolioHistoryPositionsFromAllariaIntegrations;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.security.application.BymasAuthorizationUtils;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/positions")
@Validated
@RequiredArgsConstructor
public class PositionsController {

  private final SyncPortfolioHistoryPositionsFromAllariaIntegrations
      syncPortfolioHistoryPositionsFromAllariaIntegrations;

  private final AsyncSyncPortfolioPositionFromMovementsWithImpactToday syncPositionsFromMovements;
  private final FindCurrentPortfolioPositionsForAccountByFilters
      findCurrentPortfolioPositionsForAccountByFilters;

  private final BymasAuthorizationUtils bymasAuthorizationUtils;

  private final WorkingDayService workingDayService;

  @BymasAuthorization
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PostMapping("/sync-from-allaria-integrations")
  public void syncPositions(
      @RequestParam(name = "from-date", required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
          LocalDate date,
      @RequestParam(name = "account-id", required = false) Integer accountId) {
    if (date == null) {
      date = workingDayService.lastAvailableDay(BuenosAiresTime.nowAsLocalDate(), 1);
    }
    syncPortfolioHistoryPositionsFromAllariaIntegrations.syncAllariaPositions(
        date.atStartOfDay(), Optional.ofNullable(accountId));
  }

  @BymasAuthorization()
  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PatchMapping("/sync-from-movements")
  public void syncPositionsFromMovements(
      @RequestHeader(name = "X-Client-Target", required = false) String clientTarget,
      @RequestParam(value = "account-id", required = false) Integer accountId) {
    String accountCompanyFromContext = bymasAuthorizationUtils.getAccountCompanyNameFromContext();
    if (bymasAuthorizationUtils.isAllariaMasAdmin()) {
      accountCompanyFromContext = clientTarget;
    }
    syncPositionsFromMovements.syncAsync(
        Optional.ofNullable(accountId), ByMasCompanySecrets.fromString(accountCompanyFromContext));
  }

  @BymasAuthorization(role = "WATCHER")
  @GetMapping("")
  public @ResponseBody ResponseEntity<JsonNode> getPosition(
      @RequestHeader(name = "X-Client-Target", required = false) String clientTarget,
      @RequestParam(name = "account-id") @Valid @Positive(message = "Account id must be positive")
          Integer accountId,
      @RequestParam(name = "asset", required = false) String asset,
      @RequestParam(name = "date", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
          LocalDate date,
      @RequestParam(name = "grouped-by", defaultValue = "NONE") String groupedBy,
      @RequestParam(name = "company", required = false) String company,
      @RequestParam(name = "page", defaultValue = "0") int page,
      @RequestParam(name = "count", defaultValue = "50")
          @Min(value = 1, message = "Count must be at least 1")
          @Max(value = 100, message = "Count cannot exceed 100")
          int count,
      @RequestParam(name = "group-by-sub-category-description", defaultValue = "false")
          Boolean groupBySubCategoryDescription) {
    String accountCompanyFromContext = bymasAuthorizationUtils.getAccountCompanyNameFromContext();
    if (bymasAuthorizationUtils.isAllariaMasAdmin()) {
      accountCompanyFromContext = clientTarget;
    }
    date = (date != null) ? date : BuenosAiresTime.nowAsLocalDate();
    if (groupedBy.equals("NONE")) {
      groupedBy = null;
    }
    FindPositionByFiltersInput findPositionByFiltersInput =
        new FindPositionByFiltersInput(
            accountId,
            accountCompanyFromContext,
            Optional.ofNullable(asset),
            date,
            Optional.ofNullable(groupedBy),
            Optional.ofNullable(company),
            page,
            count,
            groupBySubCategoryDescription);
    JsonNode response =
        findCurrentPortfolioPositionsForAccountByFilters.findPosition(findPositionByFiltersInput);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
