package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UpdatePositions {

  private final PortfolioPositionService portfolioPositionService;

  public void update(List<PortfolioPosition> portfolioPositions) {
    log.info("Updating {} portfolio positions", portfolioPositions.size());
    portfolioPositionService.saveAll(portfolioPositions);
    log.info("Updated {} portfolio positions", portfolioPositions.size());
  }
}
