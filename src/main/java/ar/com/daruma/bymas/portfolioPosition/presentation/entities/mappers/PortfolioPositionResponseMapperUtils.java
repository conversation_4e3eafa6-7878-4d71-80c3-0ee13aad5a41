package ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.*;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PortfolioPositionResponseMapperUtils {

  @Autowired ObjectMapper objectMapper;

  public AssetInfo buildAssetInfo(BasePortfolioPosition portfolioPosition) {
    return AssetInfo.builder()
        .name(portfolioPosition.getPortfolioAsset().getName())
        .abbreviation(portfolioPosition.getPortfolioAsset().getAbbreviation())
        .category(portfolioPosition.getPortfolioAsset().getCategory())
        .subCategory(portfolioPosition.getPortfolioAsset().getSubCategory())
        .currency(portfolioPosition.getPortfolioAsset().getCurrency())
        .build();
  }

  public List<Valuation> buildValuations(List<BasePortfolioPositionValuation> valuations) {
    return valuations.stream()
        .map(
            val ->
                Valuation.builder()
                    .name(val.getPortfolioCurrencyValuation().getName())
                    .agreementQuantity(val.getAgreementQuantity())
                    .settlementQuantity(val.getSettlementQuantity())
                    .build())
        .collect(Collectors.toList());
  }

  public Position buildPosition(
      BasePortfolioPosition position,
      List<BasePortfolioPositionValuation> valuations,
      BigDecimal totalSettlementQuantity,
      BigDecimal totalAgreementQuantity) {

    List<BasePortfolioPositionValuation> pesoValuations =
        valuations.stream().filter(BasePortfolioPositionValuation::isPesoValuation).toList();

    BigDecimal settlementPesoAmount =
        calculateTotalQuantityFromValuations(
            pesoValuations, BasePortfolioPositionValuation::getSettlementQuantity);

    BigDecimal agreementPesoAmount =
        calculateTotalQuantityFromValuations(
            pesoValuations, BasePortfolioPositionValuation::getAgreementQuantity);

    BigDecimal settlementPercentage =
        calculatePercentage(totalSettlementQuantity, settlementPesoAmount);
    BigDecimal agreementPercentage =
        calculatePercentage(totalAgreementQuantity, agreementPesoAmount);

    AssetInfo assetInfo = buildAssetInfo(position);

    List<Valuation> valuationsList = buildValuations(valuations);

    return Position.builder()
        .accountId(position.getPortfolioAccount().getAccountId())
        .asset(assetInfo)
        .agreementQuantity(position.getAgreementQuantity())
        .settlementQuantity(position.getSettlementQuantity())
        .availableQuantity(position.getAvailableQuantity())
        .lockedQuantity(position.getLockedQuantity())
        .valuations(valuationsList)
        .lastUpdatedAt(position.getLastUpdatedAt())
        .agreementPercentage(agreementPercentage)
        .settlementPercentage(settlementPercentage)
        .metadata(position.getMetadata())
        .build();
  }

  public QuantityPair calculateTotalQuantitiesFromValuations(
      List<BasePortfolioPositionValuation> valuations) {
    BigDecimal agreementQuantity =
        calculateTotalQuantityFromValuations(
            valuations, BasePortfolioPositionValuation::getAgreementQuantity);
    BigDecimal settlementQuantity =
        calculateTotalQuantityFromValuations(
            valuations, BasePortfolioPositionValuation::getSettlementQuantity);
    return new QuantityPair(agreementQuantity, settlementQuantity);
  }

  public QuantityPair calculateTotalPesoQuantitiesFromValuations(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper) {
    List<BasePortfolioPositionValuation> valuations =
        positionToValuationMapper.values().stream().flatMap(Collection::stream).toList();
    List<BasePortfolioPositionValuation> pesoValuations =
        valuations.stream().filter(BasePortfolioPositionValuation::isPesoValuation).toList();

    return calculateTotalQuantitiesFromValuations(pesoValuations);
  }

  private BigDecimal calculateTotalQuantityFromValuations(
      List<BasePortfolioPositionValuation> valuations,
      Function<BasePortfolioPositionValuation, BigDecimal> quantityExtractor) {
    return valuations.stream().map(quantityExtractor).reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  public BigDecimal calculatePercentage(BigDecimal totalAmount, BigDecimal amount) {
    return totalAmount.compareTo(BigDecimal.ZERO) > 0
        ? amount
            .multiply(BigDecimal.valueOf(100))
            .divide(totalAmount, bigDecimalScale, RoundingMode.HALF_DOWN)
        : BigDecimal.ZERO;
  }

  public List<Valuation> calculateTotalValuationsByCurrency(
      List<BasePortfolioPositionValuation> positionValuations) {

    Map<String, List<BasePortfolioPositionValuation>> valuationsByCurrency =
        positionValuations.stream()
            .collect(
                Collectors.groupingBy(
                    valuation -> valuation.getPortfolioCurrencyValuation().getName()));

    return valuationsByCurrency.entrySet().stream()
        .map(
            entry -> {
              List<BasePortfolioPositionValuation> valuationsForCurrency = entry.getValue();
              QuantityPair quantities =
                  calculateTotalQuantitiesFromValuations(valuationsForCurrency);
              BigDecimal totalAgreementQuantity = quantities.agreementQuantity();
              BigDecimal totalSettlementQuantity = quantities.settlementQuantity();
              return Valuation.builder()
                  .name(entry.getKey())
                  .agreementQuantity(totalAgreementQuantity)
                  .settlementQuantity(totalSettlementQuantity)
                  .build();
            })
        .toList();
  }
}
