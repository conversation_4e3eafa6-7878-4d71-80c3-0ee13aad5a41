package ar.com.daruma.bymas.portfolioPosition.domain.entities;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Balance {
  private BalanceType type;
  private BigDecimal settlementQuantity;
  private BigDecimal agreementQuantity;
  private BigDecimal availableQuantity;
  private BigDecimal lockedQuantity;

  public void addToAgreement(BigDecimal amount) {
    setAgreementQuantity(getAgreementQuantity().add(amount));
  }

  public void subtractFromAgreement(BigDecimal amount) {
    setAgreementQuantity(getAgreementQuantity().subtract(amount));
  }

  public void addToSettlement(BigDecimal amount) {
    setSettlementQuantity(getSettlementQuantity().add(amount));
  }

  public void subtractFromSettlement(BigDecimal amount) {
    setSettlementQuantity(getSettlementQuantity().subtract(amount));
  }

  public void addToAvailable(BigDecimal amount) {
    setAvailableQuantity(getAvailableQuantity().add(amount));
  }

  public void subtractFromAvailable(BigDecimal amount) {
    setAvailableQuantity(getAvailableQuantity().subtract(amount));
  }

  public void addToLocked(BigDecimal amount) {
    setLockedQuantity(getLockedQuantity().add(amount));
  }

  public void subtractFromLocked(BigDecimal amount) {
    setLockedQuantity(getLockedQuantity().subtract(amount));
  }
}
