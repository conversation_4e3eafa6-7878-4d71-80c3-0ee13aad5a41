package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.portfolioAccount.application.find.FindPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.domain.utils.SyncPortfolioPositionFromMovementsUtils;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SyncPortfolioPositionFromMovementsWithImpactToday extends DiscordSender {

  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;
  private final PortfolioPositionService portfolioPositionService;
  private final WorkingDayService workingDayService;
  private final SyncPortfolioPositionFromMovementsUtils syncPortfolioPositionFromMovementsUtils;
  private final ObjectMapper objectMapper;
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;

  @Transactional(
      transactionManager = "byMasTransactionManager",
      propagation = Propagation.REQUIRES_NEW)
  public List<PortfolioPositionBalance> sync(
      Optional<Integer> maybeAccountId, PortfolioCompany company) {
    log.info(
        "Syncing portfolio positions for account: {} ",
        maybeAccountId.map(Object::toString).orElse("[All]"));
    if (maybeAccountId.isEmpty()) {
      notifyDiscord("Syncing portfolio positions for all accounts by movements with impact today");
    }
    Optional<PortfolioAccount> maybePortfolioAccount =
        maybeAccountId.map(
            accountId ->
                findPortfolioAccountByAccountId.findAccountByIdAndCompany(accountId, company));
    List<PortfolioPosition> positions =
        portfolioPositionService.findByMaybePortfolioAccount(maybePortfolioAccount);
    LocalDate lastWorkingDate =
        workingDayService.lastAvailableDay(BuenosAiresTime.nowAsLocalDate(), 1);
    Set<PortfolioPositionHistory> histories =
        portfolioPositionService.findHistoriesByPortfolioPositions(lastWorkingDate, positions);
    List<PortfolioPositionBalance> balancesToSave = new ArrayList<>();
    Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionHistory>
        historyByAssetAndAccountIdPair =
            histories.stream()
                .collect(
                    Collectors.toMap(
                        BasePortfolioPosition::getAssetAndAccountIdPair, history -> history));
    positions.forEach(
        position -> {
          Optional<PortfolioPositionHistory> maybeHistory =
              Optional.ofNullable(
                  historyByAssetAndAccountIdPair.getOrDefault(
                      position.getAssetAndAccountIdPair(), null));
          PortfolioPositionBalance portfolioPositionBalance =
              position.applyHistory(objectMapper, maybeHistory);
          balancesToSave.add(portfolioPositionBalance);
        });
    syncPortfolioPositionFromMovementsUtils.updatePositionsByMovementsWithImpactTodayWithoutSaving(
        positions);
    updatePositions(positions);
    if (maybeAccountId.isEmpty()) {
      notifyDiscord(
          "Successfully synced portfolio positions for all accounts by movements with impact today");
    }
    portfolioPositionBalanceService.saveAll(balancesToSave);
    log.info(
        "Successfully synced portfolio positions for account: {}",
        maybeAccountId.map(Object::toString).orElse("[All]"));
    return balancesToSave;
  }

  private void updatePositions(List<PortfolioPosition> positions) {
    log.info("Updating {} positions", positions.size());
    portfolioPositionService.saveAll(positions);
    // datePortfolioPositionValuationForPosition
    //        ::update // TODO ver si queremos mantener valuaciones ?
    //  );
    log.info("{} positions updated successfully", positions.size());
  }
}
