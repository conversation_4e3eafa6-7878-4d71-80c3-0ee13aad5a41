package ar.com.daruma.bymas.portfolioPosition.infrastructure.utils;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortfolioPositionUtils {

  private final ObjectMapper objectMapper;
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;

  public PortfolioPositionBalance updatePositionByHistoryWithoutSaving(
      PortfolioPosition portfolioPosition,
      Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionHistory>
          existingHistoriesByKey) {
    Optional<PortfolioPositionHistory> history =
        Optional.ofNullable(
            existingHistoriesByKey.getOrDefault(
                portfolioPosition.getAssetAndAccountIdPair(), null));
    return portfolioPosition.applyHistory(objectMapper, history);
  }

  public PortfolioPosition createPositionByHistoryWithoutSaving(
      PortfolioAsset asset,
      PortfolioAccount account,
      Optional<PortfolioPositionHistory> positionHistory) {
    return positionHistory
        .map(PortfolioPosition::new)
        .orElseGet(() -> new PortfolioPosition(asset, account));
  }
}
