package ar.com.daruma.bymas.portfolioPosition.application.findsert;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.time.LocalDate;
import java.util.UUID;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FindsertPortfolioPositionByDateAndAssetAndAccount {

  @Autowired private PortfolioPositionService portfolioPositionService;

  private static final Logger logger =
      LogManager.getLogger(FindsertPortfolioPositionByDateAndAssetAndAccount.class);

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioPosition findsert(
      LocalDate date, PortfolioAsset asset, PortfolioAccount account) {
    logger.info(
        "Finding or inserting PortfolioPosition with date: {} and asset: {} and account: {}",
        date,
        asset.getId(),
        account.getId());
    PortfolioPosition porfolioPosition =
        portfolioPositionService.findsertByDateAndAssetAndAccountId(date, asset, account);
    logger.info("PortfolioPosition with id {} found or inserted", porfolioPosition.getId());
    return porfolioPosition;
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioPosition findsert(LocalDate date, UUID assetId, UUID accountId) {
    logger.info(
        "Finding or inserting PortfolioPosition with date: {} and asset: {} and account: {}",
        date,
        assetId,
        accountId);
    PortfolioPosition porfolioPosition =
        portfolioPositionService.findsertByDateAndAssetAndAccountId(date, assetId, accountId);
    logger.info("PortfolioPosition with id {} found or inserted", porfolioPosition.getId());
    return porfolioPosition;
  }
}
