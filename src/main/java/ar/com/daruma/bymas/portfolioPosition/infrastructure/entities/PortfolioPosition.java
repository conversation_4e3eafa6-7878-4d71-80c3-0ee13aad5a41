package ar.com.daruma.bymas.portfolioPosition.infrastructure.entities;

import ar.com.daruma.bymas.allariaMas.accounts.domain.AllariaMasAccount;
import ar.com.daruma.bymas.movixEvent.domain.accountShares.entities.AccountShares;
import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount.entities.MepPortfolioAccountResponse;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.Balance;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.BalanceType;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_positions")
public class PortfolioPosition extends BasePortfolioPosition {

  @Column(name = "locked_quantity", nullable = false)
  private BigDecimal lockedQuantity;

  @Column(name = "available_quantity", nullable = false)
  private BigDecimal availableQuantity;

  @Column(name = "last_updated_at", nullable = false)
  private LocalDateTime lastUpdatedAt;

  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "last_synced_at", nullable = false)
  private LocalDateTime lastSyncedAt;

  public void addToAgreement(BigDecimal amount) {
    setAgreementQuantity(getAgreementQuantity().add(amount));
  }

  public void subtractFromAgreement(BigDecimal amount) {
    setAgreementQuantity(getAgreementQuantity().subtract(amount));
  }

  public void addToSettlement(BigDecimal amount) {
    setSettlementQuantity(getSettlementQuantity().add(amount));
  }

  public void subtractFromSettlement(BigDecimal amount) {
    setSettlementQuantity(getSettlementQuantity().subtract(amount));
  }

  public void addToAvailable(BigDecimal amount) {
    setAvailableQuantity(getAvailableQuantity().add(amount));
  }

  public void subtractFromAvailable(BigDecimal amount) {
    setAvailableQuantity(getAvailableQuantity().subtract(amount));
  }

  public void addToLocked(BigDecimal amount) {
    setLockedQuantity(getLockedQuantity().add(amount));
  }

  public void subtractFromLocked(BigDecimal amount) {
    setLockedQuantity(getLockedQuantity().subtract(amount));
  }

  public PortfolioPosition() {
    super();
  }

  public PortfolioPosition(PortfolioAsset asset, PortfolioAccount account) {
    super();
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    setPortfolioAccount(account);
    setPortfolioAsset(asset);
    setSettlementQuantity(BigDecimal.ZERO);
    setAvailableQuantity(BigDecimal.ZERO);
    setLockedQuantity(BigDecimal.ZERO);
    setAgreementQuantity(BigDecimal.ZERO);
    setDescription("");
    setTags("");
    setMetadata(null);
    setLastUpdatedAt(now);
    setCreatedAt(now);
    setLastSyncedAt(now);
  }

  public PortfolioPosition(
      PortfolioAsset asset, PortfolioAccount account, ObjectMapper objectMapper) {
    super();
    List<Balance> balances = new ArrayList<>();
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    balances.add(
        new Balance(
            BalanceType.COMMITTED,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO));
    balances.add(
        new Balance(
            BalanceType.BEYOND_24_HOURS,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO));
    balances.add(
        new Balance(
            BalanceType.WITHIN_24_HOURS,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO,
            BigDecimal.ZERO));
    setId(UUID.randomUUID());
    setPortfolioAccount(account);
    setPortfolioAsset(asset);
    setSettlementQuantity(BigDecimal.ZERO);
    setAvailableQuantity(BigDecimal.ZERO);
    setLockedQuantity(BigDecimal.ZERO);
    setAgreementQuantity(BigDecimal.ZERO);
    setDescription("");
    setTags("");
    setBalancesInMetadata(objectMapper, balances);
    setLastUpdatedAt(now);
    setCreatedAt(now);
    setLastSyncedAt(now);
  }

  public PortfolioPosition(PortfolioPositionHistory yesterdayPosition) {
    super();
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    setPortfolioAccount(yesterdayPosition.getPortfolioAccount());
    setPortfolioAsset(yesterdayPosition.getPortfolioAsset());
    setSettlementQuantity(yesterdayPosition.getSettlementQuantity());
    setAvailableQuantity(yesterdayPosition.getSettlementQuantity());
    setLockedQuantity(BigDecimal.ZERO);
    setAgreementQuantity(yesterdayPosition.getAgreementQuantity());
    setDescription(yesterdayPosition.getDescription());
    setTags(yesterdayPosition.getTags());
    setMetadata(yesterdayPosition.getMetadata());
    setLastUpdatedAt(now);
    setCreatedAt(now);
    setLastSyncedAt(now);
  }

  private Balance applyPositionToBeyond24HourBalance() {
    Balance beyond24HoursBalance = getBeyond24HourBalance();
    beyond24HoursBalance.setAgreementQuantity(getAgreementQuantity());
    beyond24HoursBalance.setSettlementQuantity(getAgreementQuantity());
    beyond24HoursBalance.setAvailableQuantity(getAgreementQuantity());
    beyond24HoursBalance.setLockedQuantity(BigDecimal.ZERO);
    return beyond24HoursBalance;
  }

  private Balance applyMovementToWithin24HourBalance(PortfolioMovement movement) {
    Boolean within24HourBalanceExisted = within24HourBalanceExist();
    Balance within24HoursBalance = getWithin24HourBalance();
    if ((within24HourBalanceExisted && movement.settlementHappened())
        || movement.isWithin24Hours()) {
      within24HoursBalance.addToSettlement(movement.getAmountToApply());
      within24HoursBalance.addToAvailable(movement.getAmountToApply());
    }
    within24HoursBalance.setAgreementQuantity(getAgreementQuantity());
    if (movement.isBeyond24Hours()) {
      within24HoursBalance.addToLocked(movement.getAmountToLock());
      within24HoursBalance.subtractFromAvailable(movement.getAmountToLock());
    }
    return within24HoursBalance;
  }

  private Balance cancelMovementForWithin24HourBalance(PortfolioMovement movement) {
    Boolean within24HourBalanceExisted = within24HourBalanceExist();
    Balance within24HoursBalance = getWithin24HourBalance();
    if ((within24HourBalanceExisted && movement.settlementHappened())
        || movement.isWithin24Hours()) {
      within24HoursBalance.subtractFromSettlement(movement.getAmountToApply());
      within24HoursBalance.subtractFromAvailable(movement.getAmountToApply());
    }
    within24HoursBalance.setAgreementQuantity(getAgreementQuantity());
    if (movement.isBeyond24Hours()) {
      within24HoursBalance.subtractFromLocked(movement.getAmountToLock());
      within24HoursBalance.addToAvailable(movement.getAmountToLock());
    }
    return within24HoursBalance;
  }

  public void updateBalancesFromCancelMovement(
      ObjectMapper objectMapper, PortfolioMovement movement) {
    Balance beyond24HoursBalance = applyPositionToBeyond24HourBalance();
    Balance within24HoursBalance = cancelMovementForWithin24HourBalance(movement);
    List<Balance> newBalances =
        new java.util.ArrayList<>(List.of(beyond24HoursBalance, within24HoursBalance));
    if (hasCommitedBalance()) {
      // TODO ver esto
      Balance committedBalance = getCommittedBalance();
      newBalances.add(committedBalance);
    }
    setBalancesInMetadata(objectMapper, newBalances);
  }

  public void updateBalancesFromApplyMovement(
      ObjectMapper objectMapper, PortfolioMovement movement) {
    Balance beyond24HoursBalance = applyPositionToBeyond24HourBalance();
    Balance within24HoursBalance = applyMovementToWithin24HourBalance(movement);
    List<Balance> newBalances =
        new java.util.ArrayList<>(List.of(beyond24HoursBalance, within24HoursBalance));
    if (hasCommitedBalance()) {
      // TODO ver esto
      Balance committedBalance = getCommittedBalance();
      newBalances.add(committedBalance);
    }
    setBalancesInMetadata(objectMapper, newBalances);
  }

  private boolean hasCommitedBalance() {
    return false; // TODO ver si sacamos esto y dejamos la funcion para ver si tiene locked
  }

  public PortfolioPositionBalance applyMovement(
      ObjectMapper objectMapper, PortfolioMovement movement) {
    if (movement.affectsPosition()) {
      addToAgreement(movement.getAmountToApply());
      if (movement.settlementHappened()) {
        addToSettlement(movement.getAmountToApply());
        addToAvailable(movement.getAmountToApply());
      } else {
        addToLocked(movement.getAmountToLock());
        subtractFromAvailable(movement.getAmountToLock());
      }
      if (movement.getPortfolioAsset().isCurrencyAsset()) {
        updateBalancesFromApplyMovement(objectMapper, movement);
      }
      setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
      // TODO : preguntar comprometido / ver si se puede poner algun enum en el movimiento para
      // saber
    }
    return new PortfolioPositionBalance(this, movement, false);
  }

  public void applyAllariaMasMovement(ObjectMapper objectMapper, PortfolioMovement movement) {
    if (movement.settlementHappened()) {
      return;
    }
    addToAgreement(movement.getAmountToApply());
    if (this.getPortfolioAsset().isCurrencyAsset()) {
      updateBalancesFromApplyMovement(objectMapper, movement);
    }
    setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  public void cancelAllariaMasMovement(ObjectMapper objectMapper, PortfolioMovement movement) {
    if (movement.settlementHappened()) {
      return;
    }
    subtractFromAvailable(movement.getAmountToApply());
    if (movement.getPortfolioAsset().isCurrencyAsset()) {
      updateBalancesFromCancelMovement(objectMapper, movement);
    }
    setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  public PortfolioPositionBalance cancelMovement(
      ObjectMapper objectMapper, PortfolioMovement movement) {
    if (movement.affectsPosition()) {
      subtractFromAgreement(movement.getAmountToApply());
      if (movement.settlementHappened()) {
        subtractFromSettlement(movement.getAmountToApply());
        subtractFromAvailable(movement.getAmountToApply());
      } else {
        subtractFromLocked(movement.getAmountToLock());
        addToAvailable(movement.getAmountToLock());
      }
      if (movement.getPortfolioAsset().isCurrencyAsset()) {
        updateBalancesFromCancelMovement(objectMapper, movement);
      }
      setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    }
    return new PortfolioPositionBalance(this, movement, true);
  }

  private Balance applyHistoryDifferencesToWithin24HourBalance(BigDecimal settlementDifference) {
    Boolean within24HourBalanceExisted = within24HourBalanceExist();
    Balance within24HoursBalance = getWithin24HourBalance();
    within24HoursBalance.setAgreementQuantity(getAgreementQuantity());
    if (within24HourBalanceExisted) {
      within24HoursBalance.addToSettlement(settlementDifference);
    }
    return within24HoursBalance;
  }

  private void updateBalanceFromSettlementDifference(
      ObjectMapper objectMapper, BigDecimal settlementDifference) {
    Balance beyond24HoursBalance = applyPositionToBeyond24HourBalance();
    Balance within24HoursBalance =
        applyHistoryDifferencesToWithin24HourBalance(settlementDifference);
    Balance committedBalance = getCommittedBalance();
    List<Balance> newBalances =
        List.of(beyond24HoursBalance, within24HoursBalance, committedBalance);
    setBalancesInMetadata(objectMapper, newBalances);
  }

  public PortfolioPositionBalance applyHistory(
      ObjectMapper objectMapper, Optional<PortfolioPositionHistory> yesterdayPosition) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    setSettlementQuantity(
        yesterdayPosition
            .map(PortfolioPositionHistory::getSettlementQuantity)
            .orElse(BigDecimal.ZERO));
    setAgreementQuantity(
        yesterdayPosition
            .map(PortfolioPositionHistory::getSettlementQuantity)
            .orElse(BigDecimal.ZERO));
    resetAvailableAndLockedQuantities();
    if (this.getPortfolioAsset().isCurrencyAsset()) {
      reset24HourBalance(objectMapper);
    }
    setLastSyncedAt(now);
    return new PortfolioPositionBalance(this, null, false);
  } // TODO ver si esto tambien

  @Override
  public boolean isHistory() {
    return false;
  }

  public void applyMepPortfolioAccount(
      ObjectMapper objectMapper, MepPortfolioAccountResponse mepPortfolioAccount) {
    BigDecimal settlementDifference =
        getSettlementQuantity().subtract(mepPortfolioAccount.getSettlementQuantity());
    addToAgreement(settlementDifference);
    setSettlementQuantity(mepPortfolioAccount.getSettlementQuantity());
    setLockedQuantity(mepPortfolioAccount.getLockedQuantity());
    setAvailableQuantity(mepPortfolioAccount.getAvailableQuantity());
    updateBalanceFromSettlementDifference(objectMapper, settlementDifference);
    setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  public void applyAllariaMasAccount(
      ObjectMapper objectMapper, AllariaMasAccount account, BigDecimal lastSettlementValue) {
    BigDecimal settlementDifference =
        getSettlementQuantity().subtract(account.getSettlementQuantity(lastSettlementValue));
    addToAgreement(settlementDifference);
    setSettlementQuantity(account.getSettlementQuantity(lastSettlementValue));
    setAvailableQuantity(account.getAvailableQuantity());
    setLockedQuantity(account.getLockedQuantity());
    updateBalanceFromSettlementDifference(objectMapper, settlementDifference);
    setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  public void applyAccountShares(AccountShares accountShares) {
    setAgreementQuantity(accountShares.getAgreementQuantity());
    setAvailableQuantity(accountShares.getAvailableQuantity());
    setLockedQuantity(accountShares.getLockedQuantity());
    setSettlementQuantity(accountShares.getSettlementQuantity());
    setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
  }

  public boolean hasNoBalances() {
    return getBalances().isEmpty();
  }

  public void reset24HourBalance(ObjectMapper objectMapper) {
    Balance within24HoursBalance = getWithin24HourBalance();
    within24HoursBalance.setAgreementQuantity(getAgreementQuantity());
    within24HoursBalance.setSettlementQuantity(getSettlementQuantity());
    within24HoursBalance.setAvailableQuantity(getAvailableQuantity());
    within24HoursBalance.setLockedQuantity(BigDecimal.ZERO);
    Balance beyond24HoursBalance = applyPositionToBeyond24HourBalance();
    Balance committedBalance = getCommittedBalance();
    List<Balance> newBalances =
        List.of(beyond24HoursBalance, within24HoursBalance, committedBalance);
    setBalancesInMetadata(objectMapper, newBalances);
  }

  public void resetAvailableAndLockedQuantities() {
    this.availableQuantity = getSettlementQuantity();
    this.lockedQuantity = BigDecimal.ZERO;
  }
}
