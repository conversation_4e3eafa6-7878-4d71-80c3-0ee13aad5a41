package ar.com.daruma.bymas.portfolioPosition.application.find;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class FindPortfolioPositionHistoriesByDateAndMaybePortfolioAccount {
  private final PortfolioPositionService portfolioPositionService;

  public List<PortfolioPositionHistory> find(
      LocalDate date, Optional<PortfolioAccount> maybeAccount) {
    log.info(
        "Finding portfolio position histories for date: {} and account: {}",
        date,
        maybeAccount.map(account -> account.getId().toString()).orElse("all accounts"));
    List<PortfolioPositionHistory> histories =
        portfolioPositionService.findHistoriesByDateAndMaybeAccount(date, maybeAccount);
    log.info(
        "{} histories found for date: {} and account: {}",
        histories.size(),
        date,
        maybeAccount.map(account -> account.getId().toString()).orElse("all accounts"));
    return histories;
  }
}
