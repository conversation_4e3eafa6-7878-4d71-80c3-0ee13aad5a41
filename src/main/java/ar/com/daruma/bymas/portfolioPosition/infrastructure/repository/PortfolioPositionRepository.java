package ar.com.daruma.bymas.portfolioPosition.infrastructure.repository;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.util.*;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioPositionRepository extends JpaRepository<PortfolioPosition, UUID> {

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  List<PortfolioPosition> findByPortfolioAccountId(UUID accountId);

  List<PortfolioPosition> findByPortfolioAssetId(UUID assetId);

  @EntityGraph(attributePaths = {"portfolioAsset", "portfolioAccount"})
  Optional<PortfolioPosition> findByPortfolioAssetIdAndPortfolioAccountId(
      UUID portfolioAssetId, UUID portfolioAccountId);

  List<PortfolioPosition> findByPortfolioAssetIdInAndPortfolioAccountId(
      List<UUID> assetIds, UUID portfolioAccountId);

  List<PortfolioPosition> findByPortfolioAccountAndPortfolioAssetIn(
      PortfolioAccount account, Set<PortfolioAsset> assets);

  List<PortfolioPosition> findByPortfolioAssetIdAndPortfolioAccountIdIn(
      UUID id, Set<UUID> accountIds);

  List<PortfolioPosition> findDistinctByPortfolioAccountIdIn(List<UUID> accountIds);

  List<PortfolioPosition> findByPortfolioAssetIdInAndPortfolioAccountIdIn(
      Set<UUID> assetIds, Set<UUID> accountIds);

  List<PortfolioPosition> findByPortfolioAssetIdIn(List<UUID> portfolioAssetIds);
}
