package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UpdatePortfolioPositionsByHistoryIfOutdated {

  private final PortfolioPositionService portfolioPositionService;

  public List<PortfolioPositionBalance> updateWithoutSaving(
      List<PortfolioPosition> portfolioPositions, LocalDate date) {
    log.info(
        "Updating {} portfolio positions if outdated for date: {}",
        portfolioPositions.size(),
        date);
    if (portfolioPositions.isEmpty()) {
      log.info("No portfolio positions to update");
      return List.of();
    }
    List<PortfolioPositionBalance> portfolioPositionBalances =
        portfolioPositionService.updatePositionsByHistoryIfOutdatedWithoutSaving(
            portfolioPositions, date);
    log.info(
        "Updated {} portfolio positions if outdated for date: {}", portfolioPositions.size(), date);
    return portfolioPositionBalances;
  }
}
