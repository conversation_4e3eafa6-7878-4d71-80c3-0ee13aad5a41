package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.time.LocalDateTime;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class SyncPortfolioHistoryPositionsFromAllariaIntegrations extends DiscordSender {

  @Autowired private PortfolioSyncWorker portfolioSyncWorker;

  private static final Logger logger =
      LogManager.getLogger(SyncPortfolioHistoryPositionsFromAllariaIntegrations.class);

  @Async("asyncExecutor")
  public void syncAllariaPositions(LocalDateTime date, Optional<Integer> accountId) {
    try {
      portfolioSyncWorker.sync(date, accountId);
    } catch (Exception e) {
      String message = "Error syncing Allaria positions, reason: " + e.getMessage();
      notifyDiscord(message);
      logger.error(message);
    }
  }
}
