package ar.com.daruma.bymas.portfolioPosition.domain.entities;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.util.Optional;

public record PortfolioHistorySyncInput(
    Optional<Portfolio> liquidation,
    Optional<Portfolio> concertation,
    PortfolioAsset asset,
    PortfolioCurrencyValuation currencyValuation,
    PortfolioAccount account) {}
