package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount.entities.MepPortfolioAccountResponse;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.citadel.utils.CustomLogger;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UpdatePortfolioPositionByDolarMepAccount {

  private final PortfolioPositionService portfolioPositionService;
  private final ObjectMapper objectMapper;
  private static final CustomLogger logger =
      CustomLogger.getLogger(UpdatePortfolioPositionByDolarMepAccount.class);

  public PortfolioPosition execute(
      PortfolioPosition portfolioPosition, MepPortfolioAccountResponse mepPortfolioAccount) {
    logger.info(
        "Updating portfolio position {} by mepPortfolioAccount {}",
        portfolioPosition.getId(),
        mepPortfolioAccount.getId());
    portfolioPosition.applyMepPortfolioAccount(objectMapper, mepPortfolioAccount);
    PortfolioPosition updatedPosition = portfolioPositionService.save(portfolioPosition);
    logger.info(
        "Portfolio position {} updated by mepPortfolioAccount {}",
        updatedPosition.getId(),
        mepPortfolioAccount.getId());
    return updatedPosition;
  }
}
