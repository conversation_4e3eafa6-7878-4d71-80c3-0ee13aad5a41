package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.application.upsert.UpsertMultiplePortfolioAssetsByPortfolios;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.application.findsert.FindsertMultipleAssetQuotationsFromAllariaIntegrations;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioInvestmentIdToAssetProcessor {

  private final UpsertMultiplePortfolioAssetsByPortfolios upsertMultiplePortfolioAssetsByPortfolios;
  private final FindsertMultipleAssetQuotationsFromAllariaIntegrations
      findsertMultipleAssetQuotationsFromAllariaIntegrations;

  public Map<Integer, PortfolioAsset> process(List<Portfolio> portfoliosToProcess) {
    log.info("Upserting portfolio assets by portfolios");
    List<PortfolioAsset> portfolioAssets =
        upsertMultiplePortfolioAssetsByPortfolios.upsert(portfoliosToProcess);
    Map<PortfolioAsset.PortfolioAssetKey, PortfolioAsset> assetByKey =
        portfolioAssets.stream()
            .collect(Collectors.toMap(PortfolioAsset::getPortfolioAssetKey, asset -> asset));
    Map<PortfolioAsset, List<Portfolio>> assetToPortfoliosMap =
        portfoliosToProcess.stream()
            .collect(
                Collectors.groupingBy(
                    portfolio -> assetByKey.get(portfolio.getPortfolioAssetKey())));
    findsertMultipleAssetQuotationsFromAllariaIntegrations.findsert(assetToPortfoliosMap);
    log.info("Successfully upserted portfolio assets by portfolios");
    return portfolioAssets.stream()
        .collect(Collectors.toMap(PortfolioAsset::getAllariaAssetId, asset -> asset));
  }
}
