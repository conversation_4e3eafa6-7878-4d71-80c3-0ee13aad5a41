package ar.com.daruma.bymas.portfolioPosition.infrastructure.entities;

import ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization.JsonNodeUserType;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.Balance;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.BalanceType;
import ar.com.daruma.bymas.portfolioPosition.domain.errors.CannotDecodeBalanceTypeFromPositionMetadata;
import ar.com.daruma.bymas.portfolioPosition.domain.errors.CannotSetBalancesInMetadataException;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Setter
@Getter
@Entity
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public abstract class BasePortfolioPosition {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @EqualsAndHashCode.Include
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_account_id", nullable = false)
  private PortfolioAccount portfolioAccount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_asset_id", nullable = false)
  private PortfolioAsset portfolioAsset;

  @Column(name = "settlement_quantity", nullable = false)
  private BigDecimal settlementQuantity;

  @Column(name = "agreement_quantity", nullable = false)
  private BigDecimal agreementQuantity;

  @Column private String description;
  @Column private String tags;

  @Type(JsonNodeUserType.class)
  @Column(name = "metadata", columnDefinition = "jsonb")
  private JsonNode metadata;

  @Transient private List<Balance> balances;

  public abstract LocalDateTime getLastUpdatedAt();

  public String getAssetSubCategory() {
    return this.getPortfolioAsset().getSubCategory();
  }

  public String getAssetSubCategoryDescription() {
    return this.getPortfolioAsset().getSubCategoryDescription();
  }

  public String getAssetCurrency() {
    return this.getPortfolioAsset().getCurrency();
  }

  public abstract boolean isHistory();

  public boolean sharesCurrencyWithQuotation(PortfolioCurrencyQuotation currencyQuotation) {
    return this.getPortfolioAsset()
        .getCurrency()
        .equals(currencyQuotation.getValuation().getCode());
  }

  public List<Balance> getBalances() {
    if (this.balances != null) {
      return this.balances;
    }

    List<Balance> balances = new ArrayList<>();
    if (metadata == null || !metadata.has("balances")) {
      this.balances = balances;
      return balances;
    }

    JsonNode balancesNode = metadata.get("balances");
    if (!balancesNode.isArray()) {
      this.balances = balances;
      return balances;
    }

    for (JsonNode balanceNode : balancesNode) {
      if (balanceNode.has("type")
          && balanceNode.has("settlement_quantity")
          && balanceNode.has("agreement_quantity")
          && balanceNode.has("available_quantity")
          && balanceNode.has("locked_quantity")) {
        String typeValue = balanceNode.get("type").asText();
        try {
          Balance balance =
              new Balance(
                  BalanceType.valueOf(typeValue),
                  new BigDecimal(balanceNode.get("settlement_quantity").asText()),
                  new BigDecimal(balanceNode.get("agreement_quantity").asText()),
                  new BigDecimal(balanceNode.get("available_quantity").asText()),
                  new BigDecimal(balanceNode.get("locked_quantity").asText()));
          balances.add(balance);
        } catch (IllegalArgumentException e) {
          throw new CannotDecodeBalanceTypeFromPositionMetadata(typeValue);
        }
      }
    }

    this.balances = balances;
    return balances;
  }

  public void setBalancesInMetadata(ObjectMapper objectMapper, List<Balance> balances) {
    try {
      if (metadata == null || metadata.isNull()) {
        metadata = objectMapper.createObjectNode();
      }
      ObjectNode metadataNode = (ObjectNode) metadata;

      ArrayNode balancesArray = objectMapper.createArrayNode();
      for (Balance balance : balances) {
        ObjectNode balanceNode = objectMapper.createObjectNode();
        balanceNode.put("type", balance.getType().name());
        balanceNode.put("settlement_quantity", balance.getSettlementQuantity().toString());
        balanceNode.put("agreement_quantity", balance.getAgreementQuantity().toString());
        balanceNode.put("available_quantity", balance.getAvailableQuantity().toString());
        balanceNode.put("locked_quantity", balance.getLockedQuantity().toString());
        balancesArray.add(balanceNode);
      }
      metadataNode.set("balances", balancesArray);
      this.metadata = metadataNode;
      this.balances = new ArrayList<>(balances);
    } catch (Exception e) {
      throw new CannotSetBalancesInMetadataException();
    }
  }

  public void generateOwnBalances(ObjectMapper objectMapper) {
    Balance beyond24HourBalance = getBeyond24HourBalance();
    Balance within24HourBalance = getWithin24HourBalance();
    Balance committedBalance = getCommittedBalance();
    List<Balance> balances = new ArrayList<>();
    balances.add(beyond24HourBalance);
    balances.add(within24HourBalance);
    balances.add(committedBalance);
    setBalancesInMetadata(objectMapper, balances);
  }

  public Balance getBeyond24HourBalance() {
    return getBalances().stream()
        .filter(balance -> balance.getType() == BalanceType.BEYOND_24_HOURS)
        .findFirst()
        .orElse(
            new Balance(
                BalanceType.BEYOND_24_HOURS,
                getAgreementQuantity(),
                getAgreementQuantity(),
                getAgreementQuantity(),
                BigDecimal.ZERO));
  }

  public Boolean within24HourBalanceExist() {
    return getBalances().stream()
        .anyMatch(balance -> balance.getType() == BalanceType.WITHIN_24_HOURS);
  }

  public Balance getWithin24HourBalance() {
    return getBalances().stream()
        .filter(balance -> balance.getType() == BalanceType.WITHIN_24_HOURS)
        .findFirst()
        .orElse(
            new Balance(
                BalanceType.WITHIN_24_HOURS,
                getSettlementQuantity(),
                getAgreementQuantity(),
                getAvailableQuantity(),
                getLockedQuantity()));
  }

  public Balance getCommittedBalance() {
    return getBalances().stream()
        .filter(balance -> balance.getType() == BalanceType.COMMITTED)
        .findFirst()
        .orElse(
            new Balance(
                BalanceType.COMMITTED,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                BigDecimal.ZERO));
  }

  public Boolean hasZeroQuantities() {
    return getSettlementQuantity().compareTo(BigDecimal.ZERO) == 0
        && getAgreementQuantity().compareTo(BigDecimal.ZERO) == 0;
  }

  public AssetAndAccountIdPair getAssetAndAccountIdPair() {
    return new AssetAndAccountIdPair(
        this.getPortfolioAsset().getId(), this.getPortfolioAccount().getId());
  }

  public abstract BigDecimal getLockedQuantity();

  public abstract BigDecimal getAvailableQuantity();

  public record AssetAndAccountIdPair(UUID portfolioAssetId, UUID portfolioAccountId) {}
}
