package ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers;

import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.*;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioPositionResponseByGroupMapper {

  @Autowired PortfolioPositionResponseMapperUtils portfolioPositionResponseMapperUtils;

  public PortfolioPositionResponseByGroup mapToResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput filters,
      Function<BasePortfolioPosition, String> groupBy,
      Integer totalPages,
      Long totalElements) {

    Map<String, Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>>> positionsByGroup =
        positionToValuationMapper.entrySet().stream()
            .collect(
                Collectors.groupingBy(
                    entry -> groupBy.apply(entry.getKey()),
                    Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);
    BigDecimal totalSettlementQuantity = quantities.settlementQuantity();
    BigDecimal totalAgreementQuantity = quantities.agreementQuantity();

    List<PositionGroup> positionGroups =
        positionsByGroup.entrySet().stream()
            .map(
                entry ->
                    createPositionGroup(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .sorted(Comparator.comparing(PositionGroup::getCategory))
            .collect(Collectors.toList());

    List<Valuation> totalValuations =
        portfolioPositionResponseMapperUtils.calculateTotalValuationsByCurrency(
            positionToValuationMapper.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));

    return PortfolioPositionResponseByGroup.builder()
        .totalElements(totalElements)
        .totalAgreementQuantity(totalAgreementQuantity)
        .totalSettlementQuantity(totalSettlementQuantity)
        .totalValuations(totalValuations)
        .positionsByGroup(positionGroups)
        .date(filters.getDate())
        .groupedBy(filters.getGroup().orElse("NONE"))
        .page(filters.getPage())
        .totalPages(totalPages)
        .build();
  }

  private PositionGroup createPositionGroup(
      String category,
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      BigDecimal totalSettlementQuantity,
      BigDecimal totalAgreementQuantity) {

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);

    BigDecimal categoryAgreementQuantity = quantities.agreementQuantity();

    BigDecimal categorySettlementQuantity = quantities.settlementQuantity();

    BigDecimal agreementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalAgreementQuantity, categoryAgreementQuantity);

    BigDecimal settlementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalSettlementQuantity, categorySettlementQuantity);

    List<Position> positions =
        positionToValuationMapper.entrySet().stream()
            .map(
                entry ->
                    portfolioPositionResponseMapperUtils.buildPosition(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .collect(Collectors.toList());

    List<Valuation> valuations =
        portfolioPositionResponseMapperUtils.calculateTotalValuationsByCurrency(
            positionToValuationMapper.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));

    return PositionGroup.builder()
        .category(category)
        .agreementPercentage(agreementPercentage)
        .settlementPercentage(settlementPercentage)
        .agreementQuantity(categoryAgreementQuantity)
        .settlementQuantity(categorySettlementQuantity)
        .positions(positions)
        .valuations(valuations)
        .build();
  }
}
