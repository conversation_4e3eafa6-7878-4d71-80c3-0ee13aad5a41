package ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioPositionResponseByGroupAndDescription {
  @JsonProperty("account_id")
  private Integer accountId;

  @JsonProperty("total_elements")
  private Long totalElements;

  @JsonProperty("total_settlement_quantity")
  private BigDecimal totalSettlementQuantity;

  @JsonProperty("total_agreement_quantity")
  private BigDecimal totalAgreementQuantity;

  @JsonProperty("total_valuations")
  private List<Valuation> totalValuations;

  @JsonProperty("positions_by_group")
  private List<PositionGroupWithSubgroups> positionsByGroup;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate date;

  @JsonProperty("grouped_by")
  private String groupedBy;

  private Integer page;

  @JsonProperty("total_pages")
  private Integer totalPages;
}
