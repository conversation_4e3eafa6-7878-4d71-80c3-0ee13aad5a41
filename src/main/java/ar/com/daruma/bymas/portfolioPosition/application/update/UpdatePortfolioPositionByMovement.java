package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByDateAndAssetAndAccount;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.portfolioPositionValuation.application.update.UpdatePortfolioPositionValuationForPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdatePortfolioPositionByMovement {

  @Autowired private PortfolioPositionService portfolioPositionService;

  @Autowired
  private FindsertPortfolioPositionByDateAndAssetAndAccount
      findsertPortfolioPositionByDateAndAssetAndAccount;

  @Autowired private ObjectMapper objectMapper;

  @Autowired
  private UpdatePortfolioPositionValuationForPosition updatePortfolioPositionValuationForPosition;

  @Autowired private PortfolioPositionBalanceService portfolioPositionBalanceService;

  private static final Logger logger =
      LogManager.getLogger(UpdatePortfolioPositionByMovement.class);

  public void update(PortfolioMovement movement, Boolean cancelMovement) {
    logger.info(
        "Updating portfolioPosition for asset: {} and account: {} by movement {}",
        movement.getPortfolioAsset().getId(),
        movement.getPortfolioAccount().getId(),
        movement.getId());
    if (movement.isCancelledOrFailed()) return;
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    PortfolioPosition porfolioPosition =
        findsertPortfolioPositionByDateAndAssetAndAccount.findsert(
            now, movement.getPortfolioAsset(), movement.getPortfolioAccount());
    if (cancelMovement) {
      PortfolioPositionBalance cancelPortfolioPosition =
          porfolioPosition.cancelMovement(objectMapper, movement);
      portfolioPositionBalanceService.save(cancelPortfolioPosition);
    } else {
      PortfolioPositionBalance portfolioPositionBalance =
          porfolioPosition.applyMovement(objectMapper, movement);
      portfolioPositionBalanceService.save(portfolioPositionBalance);
    }
    portfolioPositionService.update(porfolioPosition);
    updatePortfolioPositionValuationForPosition.update(porfolioPosition);
    logger.info("portfolioPosition with id {} successfully updated", porfolioPosition.getId());
  }
}
