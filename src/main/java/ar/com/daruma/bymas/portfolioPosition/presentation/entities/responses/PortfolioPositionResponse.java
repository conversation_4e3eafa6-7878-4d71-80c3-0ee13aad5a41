package ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioPositionResponse {

  private List<Position> positions;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate date;

  @JsonProperty("grouped_by")
  private String groupedBy;

  private Integer page;

  @JsonProperty("total_pages")
  private Integer totalPages;

  @JsonProperty("total_elements")
  private Long totalElements;
}
