package ar.com.daruma.bymas.portfolioPosition.application.update;

import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByDateAndAssetAndAccount;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UpdatePortfolioPositionByAllariaMasMovement {

  private final PortfolioPositionService portfolioPositionService;

  private final FindsertPortfolioPositionByDateAndAssetAndAccount
      findsertPortfolioPositionByDateAndAssetAndAccount;

  private final ObjectMapper objectMapper;

  private static final Logger logger =
      LogManager.getLogger(UpdatePortfolioPositionByAllariaMasMovement.class);

  public void update(PortfolioMovement movement, Boolean cancelMovement) {
    if (movement.isCancelledOrFailed()) return;
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    logger.info(
        "Updating portfolioPosition balances for asset: {} and account: {} by allaria mas movement {}",
        movement.getPortfolioAsset().getId(),
        movement.getPortfolioAccount().getId(),
        movement.getId());
    PortfolioPosition porfolioPosition =
        findsertPortfolioPositionByDateAndAssetAndAccount.findsert(
            now, movement.getPortfolioAsset(), movement.getPortfolioAccount());
    if (cancelMovement) {
      porfolioPosition.cancelAllariaMasMovement(objectMapper, movement);
    } else {
      porfolioPosition.applyAllariaMasMovement(objectMapper, movement);
    }
    portfolioPositionService.update(porfolioPosition);
    logger.info("portfolioPosition with id {} successfully updated", porfolioPosition.getId());
  }
}
