package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AsyncSyncPortfolioPositionFromMovementsWithImpactToday extends DiscordSender {
  private final SyncPortfolioPositionFromMovementsWithImpactToday
      syncPortfolioPositionFromMovementsWithImpactToday;

  @Async("asyncExecutor")
  public void syncAsync(Optional<Integer> maybeAccountId, PortfolioCompany company) {
    syncPortfolioPositionFromMovementsWithImpactToday.sync(maybeAccountId, company);
  }
}
