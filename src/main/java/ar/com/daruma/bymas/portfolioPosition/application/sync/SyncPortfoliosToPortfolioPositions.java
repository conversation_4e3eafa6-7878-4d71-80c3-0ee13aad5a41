package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionValuation.application.findsert.FindsertPortfolioPositionHistoryValuationsByPortfolios;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SyncPortfoliosToPortfolioPositions {

  private final PortfolioPositionService portfolioPositionService;
  private final FindsertPortfolioPositionHistoryValuationsByPortfolios
      findsertPortfolioPositionHistoryValuationsByPortfolios;

  public void process(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {
    log.info("Processing {} grouped portfolios to portfolio positions", groupedPortfolios.size());
    List<PortfolioPositionHistory> histories =
        portfolioPositionService.findsertHistoriesByPortfolios(
            groupedPortfolios,
            portfolioAssetsByInvestmentId,
            portfolioAssetsByCurrencyValuationId,
            portfolioAccountsByAccountNumber);
    log.info("Found or created {} portfolio position histories", histories.size());
    Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryKey =
        groupedPortfolios.values().stream()
            .collect(
                Collectors.groupingBy(
                    portfolioList ->
                        new PortfolioPositionHistory.HistoryKey(
                            portfolioAssetsByInvestmentId
                                .get(portfolioList.get(0).investmentId())
                                .getId(),
                            portfolioAccountsByAccountNumber
                                .get(portfolioList.get(0).accountNumber())
                                .getId(),
                            portfolioList.get(0).getDate().toLocalDate())));
    findsertPortfolioPositionHistoryValuationsByPortfolios.findsert(
        histories, portfolioGroupsByHistoryKey, portfolioAssetsByCurrencyValuationId);
  }
}
