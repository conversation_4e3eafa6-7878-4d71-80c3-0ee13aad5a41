package ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Valuation {
  private String name;

  @JsonProperty("agreement_quantity")
  private BigDecimal agreementQuantity;

  @JsonProperty("settlement_quantity")
  private BigDecimal settlementQuantity;
}
