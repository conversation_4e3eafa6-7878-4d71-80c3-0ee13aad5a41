package ar.com.daruma.bymas.portfolioPosition.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDate;
import java.util.*;

public interface PortfolioPositionService {
  PortfolioPosition save(PortfolioPosition position);

  PortfolioPositionHistory saveHistory(PortfolioPositionHistory positionHistory);

  Optional<PortfolioPosition> findById(UUID id);

  List<PortfolioPosition> findAll();

  Optional<PortfolioPositionHistory> findMaybeHistoryByDateAndAssetAndAccountId(
      LocalDate date, UUID asset, UUID portfolioAccount);

  PortfolioPosition findsertByDateAndAssetAndAccountId(
      LocalDate date, PortfolioAsset asset, PortfolioAccount account);

  PortfolioPosition findsertByDateAndAssetAndAccountId(
      LocalDate date, UUID assetId, UUID accountId);

  void update(PortfolioPosition porfolioPosition);

  List<BasePortfolioPosition> findByFiltersAccountsAndMaybeAsset(
      FindPositionByFiltersInput filters,
      List<PortfolioAccount> accounts,
      Optional<PortfolioAsset> maybeAsset);

  List<PortfolioPosition> findByPortfolioAccountAndAssetsWithoutUpdating(
      PortfolioAccount account, Set<PortfolioAsset> assets);

  Boolean hasHistoryPositionByDate(LocalDate date, UUID assetId, UUID accountId);

  List<PortfolioPositionHistory> findsertHistoriesByPortfolios(
      Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios,
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber);

  void saveAll(List<PortfolioPosition> positionsToUpdate);

  List<PortfolioPosition> findByPortfolioAccount(PortfolioAccount account);

  List<PortfolioPosition> findByPortfolioPositionsByAssetAndAccountIdPairs(
      Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIdPairs);

  List<PortfolioPositionBalance> updatePositionsByHistoryIfOutdatedWithoutSaving(
      List<PortfolioPosition> portfolioPositions, LocalDate date);

  List<PortfolioPosition> findByPortfolioAssetIdInAndMaybePortfolioAccount(
      List<UUID> portfolioAssetIds, Optional<PortfolioAccount> maybePortfolioAccount);

  List<PortfolioPosition> findByMaybePortfolioAccount(Optional<PortfolioAccount> maybeAccount);

  Set<PortfolioPositionHistory> findHistoriesByPortfolioPositions(
      LocalDate lastWorkingDate, List<PortfolioPosition> positions);

  List<PortfolioPositionHistory> findHistoriesByDateAndMaybeAccount(
      LocalDate date, Optional<PortfolioAccount> maybeAccount);
}
