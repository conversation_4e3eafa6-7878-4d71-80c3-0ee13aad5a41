package ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers;

import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.*;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioPositionResponseByGroupAndDescriptionMapper {

  @Autowired PortfolioPositionResponseMapperUtils portfolioPositionResponseMapperUtils;

  public PortfolioPositionResponseByGroupAndDescription mapToResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput filters,
      Function<BasePortfolioPosition, String> groupBy,
      Integer totalPages,
      Long totalElements) {

    Map<String, Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>>> positionsByGroup =
        positionToValuationMapper.entrySet().stream()
            .collect(
                Collectors.groupingBy(
                    entry -> groupBy.apply(entry.getKey()),
                    Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);
    BigDecimal totalSettlementQuantity = quantities.settlementQuantity();
    BigDecimal totalAgreementQuantity = quantities.agreementQuantity();

    List<PositionGroupWithSubgroups> positionGroups =
        positionsByGroup.entrySet().stream()
            .map(
                entry ->
                    createPositionGroupWithSubgroups(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .sorted(Comparator.comparing(PositionGroupWithSubgroups::getCategory))
            .collect(Collectors.toList());

    List<Valuation> totalValuations =
        portfolioPositionResponseMapperUtils.calculateTotalValuationsByCurrency(
            positionToValuationMapper.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));

    return PortfolioPositionResponseByGroupAndDescription.builder()
        .accountId(filters.getAccountId())
        .totalElements(totalElements)
        .totalAgreementQuantity(totalAgreementQuantity)
        .totalSettlementQuantity(totalSettlementQuantity)
        .totalValuations(totalValuations)
        .positionsByGroup(positionGroups)
        .date(filters.getDate())
        .groupedBy(filters.getGroup().orElse("NONE"))
        .page(filters.getPage())
        .totalPages(totalPages)
        .build();
  }

  private PositionGroupWithSubgroups createPositionGroupWithSubgroups(
      String category,
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      BigDecimal totalSettlementQuantity,
      BigDecimal totalAgreementQuantity) {

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);

    BigDecimal categoryAgreementQuantity = quantities.agreementQuantity();
    BigDecimal categorySettlementQuantity = quantities.settlementQuantity();

    BigDecimal agreementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalAgreementQuantity, categoryAgreementQuantity);

    BigDecimal settlementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalSettlementQuantity, categorySettlementQuantity);

    Map<String, Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>>>
        positionsByDescription =
            positionToValuationMapper.entrySet().stream()
                .collect(
                    Collectors.groupingBy(
                        entry -> entry.getKey().getAssetSubCategoryDescription(),
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    List<PositionGroup> subgroups =
        positionsByDescription.entrySet().stream()
            .map(
                entry ->
                    createPositionSubgroup(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .collect(Collectors.toList());

    List<Valuation> valuations =
        portfolioPositionResponseMapperUtils.calculateTotalValuationsByCurrency(
            positionToValuationMapper.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));

    return PositionGroupWithSubgroups.builder()
        .category(category)
        .agreementPercentage(agreementPercentage)
        .settlementPercentage(settlementPercentage)
        .agreementQuantity(categoryAgreementQuantity)
        .settlementQuantity(categorySettlementQuantity)
        .subgroups(subgroups)
        .valuations(valuations)
        .build();
  }

  private PositionGroup createPositionSubgroup(
      String description,
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      BigDecimal totalSettlementQuantity,
      BigDecimal totalAgreementQuantity) {

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);

    BigDecimal subgroupAgreementQuantity = quantities.agreementQuantity();
    BigDecimal subgroupSettlementQuantity = quantities.settlementQuantity();

    BigDecimal agreementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalAgreementQuantity, subgroupAgreementQuantity);

    BigDecimal settlementPercentage =
        portfolioPositionResponseMapperUtils.calculatePercentage(
            totalSettlementQuantity, subgroupSettlementQuantity);

    List<Position> positions =
        positionToValuationMapper.entrySet().stream()
            .map(
                entry ->
                    portfolioPositionResponseMapperUtils.buildPosition(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .collect(Collectors.toList());

    List<Valuation> valuations =
        portfolioPositionResponseMapperUtils.calculateTotalValuationsByCurrency(
            positionToValuationMapper.values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));

    return PositionGroup.builder()
        .category(description)
        .agreementPercentage(agreementPercentage)
        .settlementPercentage(settlementPercentage)
        .agreementQuantity(subgroupAgreementQuantity)
        .settlementQuantity(subgroupSettlementQuantity)
        .positions(positions)
        .valuations(valuations)
        .build();
  }
}
