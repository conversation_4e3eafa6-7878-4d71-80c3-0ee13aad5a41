package ar.com.daruma.bymas.portfolioPosition.application.find;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.*;

import ar.com.daruma.bymas.portfolioPosition.domain.PortfolioPositionService;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioPositionsByAssetAndAccountIdPairs {

  private final PortfolioPositionService portfolioPositionService;

  public List<PortfolioPosition> find(
      Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIdPairs) {
    log.info(
        "Finding portfolio positions by asset and account id pairs: {}", assetAndAccountIdPairs);
    List<PortfolioPosition> positions =
        portfolioPositionService.findByPortfolioPositionsByAssetAndAccountIdPairs(
            assetAndAccountIdPairs);
    log.info("Found {} portfolio positions", positions.size());
    return positions;
  }
}
