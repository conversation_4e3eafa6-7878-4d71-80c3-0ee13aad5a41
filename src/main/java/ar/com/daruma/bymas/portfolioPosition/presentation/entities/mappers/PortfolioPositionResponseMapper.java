package ar.com.daruma.bymas.portfolioPosition.presentation.entities.mappers;

import ar.com.daruma.bymas.portfolioPosition.domain.entities.FindPositionByFiltersInput;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.*;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PortfolioPositionResponseMapper {

  @Autowired PortfolioPositionResponseMapperUtils portfolioPositionResponseMapperUtils;

  public PortfolioPositionResponse mapToResponse(
      Map<BasePortfolioPosition, List<BasePortfolioPositionValuation>> positionToValuationMapper,
      FindPositionByFiltersInput filters,
      Integer totalPages,
      Long totalElements) {

    QuantityPair quantities =
        portfolioPositionResponseMapperUtils.calculateTotalPesoQuantitiesFromValuations(
            positionToValuationMapper);
    BigDecimal totalSettlementQuantity = quantities.settlementQuantity();
    BigDecimal totalAgreementQuantity = quantities.settlementQuantity();

    List<Position> positions =
        positionToValuationMapper.entrySet().stream()
            .map(
                entry ->
                    portfolioPositionResponseMapperUtils.buildPosition(
                        entry.getKey(),
                        entry.getValue(),
                        totalSettlementQuantity,
                        totalAgreementQuantity))
            .sorted(Comparator.comparing(position -> position.getAsset().getName()))
            .collect(Collectors.toList());

    return PortfolioPositionResponse.builder()
        .positions(positions)
        .date(filters.getDate())
        .groupedBy("NONE")
        .page(filters.getPage())
        .totalPages(totalPages)
        .totalElements(totalElements)
        .build();
  }
}
