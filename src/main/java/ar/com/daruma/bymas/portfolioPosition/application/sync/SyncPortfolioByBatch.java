package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class SyncPortfolioByBatch {

  private final SyncPortfoliosToPortfolioPositions syncPortfoliosToPortfolioPositions;
  private final PortfolioInvestmentIdToAssetProcessor portfolioInvestmentIdToAssetProcessor;

  private final PortfolioAccountProcessor portfolioAccountProcessor;

  private final PortfolioCurrencyToCurrencyValuationProcessor
      portfolioCurrencyToCurrencyValuationProcessor;

  @Transactional(
      transactionManager = "byMasTransactionManager",
      propagation = Propagation.REQUIRES_NEW)
  public void sync(List<Portfolio> portfoliosToProcess) {
    log.info(
        "Grouping {} portfolios by investmentId, accountNumber, currencyValuationId and date",
        portfoliosToProcess.size());
    Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId =
        portfolioInvestmentIdToAssetProcessor.process(portfoliosToProcess);
    Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber =
        portfolioAccountProcessor.process(portfoliosToProcess);
    Map<Integer, PortfolioCurrencyValuation> portfolioCurrencyValuationsByCurrencyValuationId =
        portfolioCurrencyToCurrencyValuationProcessor.process(portfoliosToProcess);
    Map<Portfolio.PortfolioIdentity, List<Portfolio>> groupedPortfolios =
        getGroupedPortfolios(portfoliosToProcess);
    syncPortfoliosToPortfolioPositions.process(
        groupedPortfolios,
        portfolioAssetsByInvestmentId,
        portfolioCurrencyValuationsByCurrencyValuationId,
        portfolioAccountsByAccountNumber);
    log.info("Successfully synced {} portfolios", portfoliosToProcess.size());
  }

  public Map<Portfolio.PortfolioIdentity, List<Portfolio>> getGroupedPortfolios(
      List<Portfolio> portfoliosToProcess) {
    return portfoliosToProcess.stream()
        .collect(
            Collectors.groupingBy(
                portfolio ->
                    new Portfolio.PortfolioIdentity(
                        portfolio.investmentId(),
                        portfolio.accountNumber(),
                        portfolio.currencyValuationId(),
                        portfolio.getDate().toLocalDate())));
  }
}
