package ar.com.daruma.bymas.portfolioPosition.application.sync;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindsertPortfolioAccountsByPortfolios;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PortfolioAccountProcessor {

  private final FindsertPortfolioAccountsByPortfolios findsertPortfolioAccountsByPortfolios;

  public Map<Integer, PortfolioAccount> process(List<Portfolio> portfoliosToProcess) {
    List<PortfolioAccount> portfolioAccounts =
        findsertPortfolioAccountsByPortfolios.findsert(portfoliosToProcess);
    return portfolioAccounts.stream()
        .collect(Collectors.toMap(PortfolioAccount::getAccountId, account -> account));
  }
}
