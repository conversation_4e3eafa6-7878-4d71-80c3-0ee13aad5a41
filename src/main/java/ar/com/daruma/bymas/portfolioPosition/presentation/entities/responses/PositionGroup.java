package ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PositionGroup {
  private String category;

  @JsonProperty("agreement_percentage")
  private BigDecimal agreementPercentage;

  @JsonProperty("settlement_percentage")
  private BigDecimal settlementPercentage;

  @JsonProperty("agreement_quantity")
  private BigDecimal agreementQuantity;

  @JsonProperty("settlement_quantity")
  private BigDecimal settlementQuantity;

  private List<Position> positions;

  private List<Valuation> valuations;
}
