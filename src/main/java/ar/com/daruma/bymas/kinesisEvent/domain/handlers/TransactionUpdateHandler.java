package ar.com.daruma.bymas.kinesisEvent.domain.handlers;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.service.KinesisHandler;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.AccountDetail;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.OperationDetail;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioMovement.application.update.UpdatePortfolioMovementByExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.application.validations.ValidatePortfolioMovementExistsForOperationId;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByMovement;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionUpdateHandler implements KinesisHandler {

  private final ValidatePortfolioMovementExistsForOperationId
      validatePortfolioMovementExistsForOperationId;

  private final UpdatePortfolioPositionByMovement updatePortfolioPositionByMovement;

  private final UpdatePortfolioMovementByExternalMovement updatePortfolioMovementByExternalMovement;

  private final FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public KinesisHandlerResponse execute(KinesisRequest input) {
    log.info("Executing TransactionUpdateHandler with input: {}", input);
    KinesisHandlerResponse response = new KinesisHandlerResponse();
    AccountDetail accountDetails = input.getAccountDetail();
    PortfolioAccount portfolioAccount =
        findsertPortfolioAccountByAccountNumber.findsert(
            accountDetails.getAccountNumber(),
            accountDetails.getDescription(),
            accountDetails.isPhysical(),
            allariaCompany);
    OperationDetail operationDetail = input.getOperationDetail();
    PortfolioMovement updatedCurrentPortfolioMovement =
        processOperationDetail(operationDetail, portfolioAccount);
    response.setPortfolioAccount(updatedCurrentPortfolioMovement.getPortfolioAccount());
    log.info("TransactionUpdateHandler executed successfully with response: {}", response);
    return response;
  }

  public PortfolioMovement processOperationDetail(
      OperationDetail operationDetail, PortfolioAccount portfolioAccount) {
    operationDetail.makeCurrencyOperation();
    PortfolioMovement portfolioMovement =
        validatePortfolioMovementExistsForOperationId.validate(operationDetail.getOperationId());
    PortfolioMovement updatedPortfolioMovement =
        updatePortfolioMovementByExternalMovement.execute(
            portfolioMovement, operationDetail, false, portfolioAccount);
    applyPositionIfApply(updatedPortfolioMovement);
    return updatedPortfolioMovement;
  }

  private void applyPositionIfApply(PortfolioMovement updatedPortfolioMovement) {
    if (updatedPortfolioMovement.isApproved()
            && updatedPortfolioMovement.getSettlementAt().isEqual(BuenosAiresTime.nowAsLocalDate())
        || updatedPortfolioMovement.getSettlementAt().isAfter(BuenosAiresTime.nowAsLocalDate())) {
      log.info(
          "Applying position update for transaction movement: {}",
          updatedPortfolioMovement.getId());
      updatePortfolioPositionByMovement.update(updatedPortfolioMovement, false);
    } else {
      log.info(
          "Skipping position update for transaction movement: {} as it is not approved, actual state: {}",
          updatedPortfolioMovement.getId(),
          updatedPortfolioMovement.getState());
    }
  }
}
