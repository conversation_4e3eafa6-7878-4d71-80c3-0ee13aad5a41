package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AssetDetail {
  private Long assetId;
  private String assetName;
  private String assetType;
  private String assetAbbreviation;
  private String assetCurrency;

  @JsonIgnore
  public String getCurrencyAbbreviation() {
    return switch (assetCurrency) {
      case "Pesos" -> "ARS";
      case "Peso" -> "ARS";
      case "Dólar", "Dolar Garantia MTR", "DOLAR ROFEX  A3500", "DOLAR \"A 3500\"" -> "USD";
      case "MEP Dólar" -> "USD-M";
      case "Dólar MEP" -> "USD-M";
      case "Dólar - MtR" -> "USD-MtR";
      case "Euros" -> "EUR";
      case "Real" -> "BRL";
      case "Yuan" -> "CNY";
      default -> throw new RuntimeException(
          "No abbreviation for amountDetailCurrency: " + assetCurrency);
    };
  }
}
