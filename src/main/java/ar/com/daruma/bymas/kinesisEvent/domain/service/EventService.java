package ar.com.daruma.bymas.kinesisEvent.domain.service;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEvent;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEventState;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface EventService {

  void updateSuccessfulEvent(Integer newEventId, KinesisHandlerResponse response);

  void updateFailedEvent(Integer newEventId, Exception error);

  Integer savePendingProcessingEvent(KinesisRequest record);

  List<KinesisEvent> findByParameters(
      Optional<Integer> id, Optional<LocalDate> date, KinesisEventState state);
}
