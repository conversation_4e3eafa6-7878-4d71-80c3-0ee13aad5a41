package ar.com.daruma.bymas.kinesisEvent.domain.deserializer;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisBillAssignation;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

public class KinesisBillAssignationDeserializer extends JsonDeserializer<KinesisBillAssignation> {
  @Override
  public KinesisBillAssignation deserialize(JsonParser p, DeserializationContext ctxt)
      throws IOException {
    JsonNode node = p.getCodec().readTree(p);

    long billAssignationCode = node.get("CodMinutaAsig").asLong();
    long codAllariaAccount = node.get("CodComitente").asLong();

    return new KinesisBillAssignation(billAssignationCode, codAllariaAccount);
  }
}
