package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EventDetail {
  @NotNull private String operationType;
  @NotNull private String tableName;
  @NotNull private Long transactionId;

  @Schema(hidden = true)
  @JsonIgnore
  private JsonNode json;
}
