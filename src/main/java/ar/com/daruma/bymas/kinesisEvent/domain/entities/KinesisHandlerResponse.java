package ar.com.daruma.bymas.kinesisEvent.domain.entities;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class KinesisHandlerResponse {

  private PortfolioAccount portfolioAccount;
  private Boolean isUnknown = false;

  public Optional<PortfolioAccount> getPortfolioAccount() {
    return Optional.ofNullable(portfolioAccount);
  }
}
