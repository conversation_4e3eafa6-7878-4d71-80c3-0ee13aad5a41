package ar.com.daruma.bymas.kinesisEvent.presentation.entities.deserializer;

import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.OperationDetail;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

public class FutureOpDetailDeserializer extends JsonDeserializer<OperationDetail> {
  @Override
  public OperationDetail deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
    JsonNode node = p.getCodec().readTree(p);
    if (node == null || node.isNull()) return null;
    if ((node.has("empty") && node.get("empty").asBoolean())
        || (node.has("present") && !node.get("present").asBoolean())) {
      return null;
    }
    // Deserializá normalmente cuando sea un OperationDetail real
    return p.getCodec().treeToValue(node, OperationDetail.class);
  }
}
