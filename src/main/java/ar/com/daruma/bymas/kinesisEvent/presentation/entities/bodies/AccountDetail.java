package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AccountDetail {
  @NotNull private Integer accountNumber;
  @NotNull private Integer accountCode;
  @NotNull private String description;
  @NotNull private PersonType personType;

  @Schema(hidden = true)
  @JsonIgnore
  public Boolean isPhysical() {
    return personType.isPhysical();
  }
}
