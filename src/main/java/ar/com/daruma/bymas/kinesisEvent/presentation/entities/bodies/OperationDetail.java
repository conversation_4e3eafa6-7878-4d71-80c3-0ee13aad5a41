package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.TRANSFER;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementPositionEffect.AFFECTS_BALANCE;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementPositionEffect.DOES_NOT_AFFECT_BALANCE;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementVisibility.IS_SHOWN;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementVisibility.NOT_SHOWN;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.*;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OperationDetail implements ExternalMovement {
  @Valid @NotNull private AmountDetail amountDetail;
  @Valid private AssetDetail assetDetail;
  private Boolean hasTicket;

  @JsonProperty("operation")
  @NotNull
  @Getter(AccessLevel.NONE)
  private MarketOperationType grotescoMovementType;

  @Getter(AccessLevel.NONE)
  @NotNull
  @JsonProperty("operation_type")
  private OperationType operationType;

  @NotNull private LocalDateTime liquidationDateTime;
  @NotNull private LocalDateTime operationDateTime;

  @NotNull private MovementState state;
  @NotNull private String operationId;

  private String description;
  private Long term;
  private BigDecimal agreedRate;
  private JsonNode metadata;

  @Schema(hidden = true)
  @JsonIgnore
  public Optional<AssetDetail> getMaybeAssetDetail() {
    return Optional.ofNullable(assetDetail);
  }

  @Schema(hidden = true)
  @JsonIgnore
  public Optional<JsonNode> getMaybeMetadata() {
    return Optional.ofNullable(metadata);
  }

  @Schema(hidden = true)
  @Override
  @JsonIgnore
  public MovementState getMovementState() {
    if (!(getMovementType() == MovementType.MANUAL_MOVEMENT)
        && this.liquidationDateTime.isAfter(this.operationDateTime)
        && this.liquidationDateTime.isAfter(BuenosAiresTime.nowAsLocalDateTime())) {
      return MovementState.PENDING_LIQUIDATION;
    } else {
      return this.state;
    }
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public LocalDate getSettlementAt() {
    return getLiquidationDateTime().toLocalDate();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public LocalDate getAgreementAt() {
    return getOperationDateTime().toLocalDate();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public BigDecimal getFeeAmount() {
    return getAmountDetail().getAllariaFee().add(amountDetail.getMarketFee());
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public BigDecimal getTaxAmount() {
    return getAmountDetail().getAllariaFeeVat().add(amountDetail.getMarketFeeVat());
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public BigDecimal getGrossAmount() {
    return getAmountDetail().getGrossAmount();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public BigDecimal getNetAmount() {
    return getAmountDetail().getNetAmount();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public BigDecimal getQuantity() {
    return getAmountDetail().getAmount() != null
        ? getAmountDetail().getAmount()
        : getAmountDetail().getNetAmount();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public OperationType getOperationType() {
    return getMaybeAssetDetail()
        .map(assetDetail -> operationType.getOpposite())
        .orElse(operationType);
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public MarketOperationType getMarketOperationType() {
    return grotescoMovementType;
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public String getCurrency() {
    return getAmountDetail().getFormattedCurrency();
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public MovementType getMovementType() {
    return MovementTypeMapper.mapToMovementType(grotescoMovementType, this.getMaybeAssetDetail());
  }

  @Schema(hidden = true)
  @JsonIgnore
  @Override
  public Long getId() {
    int dashIndex = operationId.indexOf("-");
    return Long.parseLong(operationId.substring(0, dashIndex));
  }

  @Override
  @Schema(hidden = true)
  @JsonIgnore
  public JsonNode getDataAsJson() {
    return getMetadata();
  }

  @Schema(hidden = true)
  @JsonIgnore
  public Optional<Integer> getAccountId() {
    return Optional.empty();
  }

  public void makeCurrencyOperation() {
    if (getMaybeAssetDetail().isPresent()) {
      setOperationType(operationType.getOpposite());
      setOperationId(getOperationId().concat("-CURRENCY"));
      amountDetail.setAmount(amountDetail.getNetAmount());
    }
  }

  private boolean isCurrencyOperation() {
    return getOperationId().endsWith("-CURRENCY");
  }

  private boolean isNotCurrencyOperation() {
    return !isCurrencyOperation();
  }

  private boolean isNotTicket() {
    return !this.description.contains("Boleto /");
  }

  @Schema(hidden = true)
  @JsonIgnore
  private boolean isNotTransfer() {
    return getMovementType() != TRANSFER;
  }

  private boolean isNotBlockingManualMovement() {
    return !getDescription().contains("loqueo monetario por transferencia");
  }

  private boolean isNotFCIBlockingManualMovement() {
    return !getDescription().contains("Bloqueo Monetario por Suscripción")
        && !getDescription().contains("Desbloqueo Monetario por Liquidación");
  }

  @Override
  @Schema(hidden = true)
  @JsonIgnore
  public MovementPositionEffect getPositionEffect() {
    if (isNotTransfer() && isNotTicket() && isNotFCIBlockingManualMovement())
      return AFFECTS_BALANCE;
    return DOES_NOT_AFFECT_BALANCE;
  }

  @Override
  @Schema(hidden = true)
  @JsonIgnore
  public MovementVisibility getMovementVisibility() {
    if (isNotBlockingManualMovement()
        && isNotTicket()
        && isNotCurrencyOperation()
        && isNotFCIBlockingManualMovement()) return IS_SHOWN;
    return NOT_SHOWN;
  }
}
