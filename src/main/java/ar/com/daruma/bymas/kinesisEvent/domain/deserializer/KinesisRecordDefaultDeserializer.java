package ar.com.daruma.bymas.kinesisEvent.domain.deserializer;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisDefaultOperation;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

public class KinesisRecordDefaultDeserializer extends JsonDeserializer<KinesisDefaultOperation> {
  @Override
  public KinesisDefaultOperation deserialize(JsonParser p, DeserializationContext ctxt)
      throws IOException {
    JsonNode node = p.getCodec().readTree(p);

    long codAllariaAccount = node.get("CodComitente").asLong();

    return new KinesisDefaultOperation(codAllariaAccount);
  }
}
