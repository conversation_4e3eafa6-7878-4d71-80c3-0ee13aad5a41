package ar.com.daruma.bymas.kinesisEvent.domain.handlers;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.entities.ProcessOperationDetailResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.service.KinesisHandler;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.AccountDetail;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.OperationDetail;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioMovement.application.cancel.CancelPortfolioMovement;
import ar.com.daruma.bymas.portfolioMovement.application.update.UpdatePortfolioMovementByExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.application.validations.ValidatePortfolioMovementExistsForOperationId;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByMovement;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class KinesisRequestUpdateHandler extends DiscordSender implements KinesisHandler {

  private final CancelPortfolioMovement cancelPortfolioMovement;
  private final UpdatePortfolioPositionByMovement updatePortfolioPositionByMovement;

  private final ValidatePortfolioMovementExistsForOperationId
      validatePortfolioMovementExistsForOperationId;

  private final UpdatePortfolioMovementByExternalMovement updatePortfolioMovementByExternalMovement;

  private final FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public KinesisHandlerResponse execute(KinesisRequest input) {
    KinesisHandlerResponse response = new KinesisHandlerResponse();
    OperationDetail operationDetail = input.getOperationDetail();
    Optional<OperationDetail> maybeFutureOperationDetail = input.getFutureOperationDetail();
    AccountDetail accountDetails = input.getAccountDetail();
    PortfolioAccount portfolioAccount =
        findsertPortfolioAccountByAccountNumber.findsert(
            accountDetails.getAccountNumber(),
            accountDetails.getDescription(),
            accountDetails.isPhysical(),
            allariaCompany);
    ProcessOperationDetailResponse updatedCurrentPortfolioMovement =
        processOperationDetail(operationDetail, input.getIsAnnulled(), portfolioAccount);
    maybeFutureOperationDetail.map(
        futureOperationDetail ->
            processOperationDetail(futureOperationDetail, input.getIsAnnulled(), portfolioAccount));
    response.setPortfolioAccount(updatedCurrentPortfolioMovement.getPortfolioAccount());
    return response;
  }

  public ProcessOperationDetailResponse processOperationDetail(
      OperationDetail operationDetail, Boolean isAnnulled, PortfolioAccount portfolioAccount) {
    Optional<PortfolioMovement> maybeAssetPortfolioMovement =
        operationDetail
            .getMaybeAssetDetail()
            .map(
                assetDetails ->
                    validatePortfolioMovementExistsForOperationId.validate(
                        operationDetail.getOperationId()));
    Optional<PortfolioMovement> updatedMaybeAssetPortfolioMovement =
        maybeAssetPortfolioMovement.map(
            assetPortfolioMovement ->
                updatePortfolioMovementAndPosition(
                    assetPortfolioMovement, operationDetail, isAnnulled, portfolioAccount));
    Optional<PortfolioMovement> updatedPortfolioMovement = Optional.empty();
    if (operationDetail.getMaybeAssetDetail().isPresent()) {
      operationDetail.makeCurrencyOperation();
      PortfolioMovement currencyPortfolioMovement =
          validatePortfolioMovementExistsForOperationId.validate(operationDetail.getOperationId());
      updatedPortfolioMovement =
          Optional.of(
              updatePortfolioMovementAndPosition(
                  currencyPortfolioMovement, operationDetail, isAnnulled, portfolioAccount));
    }
    return new ProcessOperationDetailResponse(
        updatedMaybeAssetPortfolioMovement, updatedPortfolioMovement);
  }

  public PortfolioMovement updatePortfolioMovementAndPosition(
      PortfolioMovement portfolioMovement,
      OperationDetail operationDetail,
      Boolean isAnnulled,
      PortfolioAccount portfolioAccount) {
    if (portfolioMovement.getSettlementAt().isBefore(BuenosAiresTime.nowAsLocalDate())) {
      return portfolioMovement;
    }
    updatePortfolioPositionByMovement.update(portfolioMovement, true);
    PortfolioMovement updatedPortfolioMovement;
    if (isAnnulled) {
      updatedPortfolioMovement = cancelPortfolioMovement.execute(portfolioMovement);
    } else {
      updatedPortfolioMovement =
          updatePortfolioMovementByExternalMovement.execute(
              portfolioMovement, operationDetail, true, portfolioAccount);
      updatePortfolioPositionByMovement.update(updatedPortfolioMovement, false);
    }
    return updatedPortfolioMovement;
  }
}
