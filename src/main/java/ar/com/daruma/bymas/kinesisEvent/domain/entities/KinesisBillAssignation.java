package ar.com.daruma.bymas.kinesisEvent.domain.entities;

import ar.com.daruma.bymas.kinesisEvent.domain.deserializer.KinesisBillAssignationDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonDeserialize(using = KinesisBillAssignationDeserializer.class)
public class KinesisBillAssignation extends KinesisEntity {
  private Long billAssignationCode;

  public KinesisBillAssignation(Long billAssignationCode, Long allariaAccountCode) {
    this.billAssignationCode = billAssignationCode;
    this.allariaAccountCode = allariaAccountCode;
  }
}
