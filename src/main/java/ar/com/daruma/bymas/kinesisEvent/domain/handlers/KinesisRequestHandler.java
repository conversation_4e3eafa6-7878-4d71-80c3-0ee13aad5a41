package ar.com.daruma.bymas.kinesisEvent.domain.handlers;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.entities.ProcessOperationDetailResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.service.KinesisHandler;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.*;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.application.findsert.FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.application.create.CreatePortfolioMovementFromExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.application.validations.ValidateNoPortfolioMovementExistsForOperationId;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByMovement;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class KinesisRequestHandler implements KinesisHandler {

  @Autowired private UpdatePortfolioPositionByMovement updatePortfolioPositionByMovement;

  @Autowired
  private FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Autowired
  private FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
      findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;

  @Autowired
  private CreatePortfolioMovementFromExternalMovement createPortfolioMovementFromExternalMovement;

  @Autowired
  private ValidateNoPortfolioMovementExistsForOperationId
      validateNoPortfolioMovementExistsForOperationId;

  private static final CustomLogger logger = CustomLogger.getLogger(KinesisRequestHandler.class);

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public KinesisHandlerResponse execute(KinesisRequest input) {
    AccountDetail accountDetails = input.getAccountDetail();
    PortfolioAccount portfolioAccount =
        findsertPortfolioAccountByAccountNumber.findsert(
            accountDetails.getAccountNumber(),
            accountDetails.getDescription(),
            accountDetails.isPhysical(),
            allariaCompany);
    KinesisHandlerResponse response = processOperations(input, portfolioAccount);
    logger.info(
        "Kinesis event with external id {} processed successfully for account {}",
        input.getExternalId(),
        portfolioAccount.getId());
    return response;
  }

  private KinesisHandlerResponse processOperations(
      KinesisRequest input, PortfolioAccount portfolioAccount) {
    KinesisHandlerResponse response = new KinesisHandlerResponse();
    OperationDetail operationDetail = input.getOperationDetail();
    operationDetail.setState(operationDetail.getMovementState());
    Optional<OperationDetail> maybeFutureOperationDetail = input.getFutureOperationDetail();
    Optional.ofNullable(input.getFundsDetail())
        .ifPresent(
            fundDetails -> {
              BigDecimal sharesAmount =
                  calculateSharesAmount(fundDetails, operationDetail.getAmountDetail());
              operationDetail.getAmountDetail().setAmount(sharesAmount);
            });
    ProcessOperationDetailResponse currentOperationDetailResponse =
        processOperationDetail(operationDetail, portfolioAccount);
    Optional<ProcessOperationDetailResponse> maybeFutureOperationDetailResponse =
        maybeFutureOperationDetail.map(
            futureOperationDetail ->
                processOperationDetail(futureOperationDetail, portfolioAccount));
    updatePortfolioPositionsByProcessOperationDetailResponse(currentOperationDetailResponse);
    maybeFutureOperationDetailResponse.ifPresent(
        this::updatePortfolioPositionsByProcessOperationDetailResponse);
    response.setPortfolioAccount(portfolioAccount);
    return response;
  }

  private void updatePortfolioPositionsByProcessOperationDetailResponse(
      ProcessOperationDetailResponse response) {
    response
        .getMaybePortfolioAssetMovement()
        .filter(PortfolioMovement::isNotTransfer)
        .filter(PortfolioMovement::settlementIsTodayOrLater)
        .ifPresent(movement -> updatePortfolioPositionByMovement.update(movement, false));
    response
        .getMaybeCurrencyPortfolioMovement()
        .filter(PortfolioMovement::isNotTransfer)
        .filter(PortfolioMovement::settlementIsTodayOrLater)
        .ifPresent(movement -> updatePortfolioPositionByMovement.update(movement, false));
  }

  private ProcessOperationDetailResponse processOperationDetail(
      OperationDetail operationDetail, PortfolioAccount portfolioAccount) {
    Optional<AssetDetail> maybeAssetDetails = operationDetail.getMaybeAssetDetail();
    validateNoPortfolioMovementExistsForOperationId.validate(operationDetail.getOperationId());
    AmountDetail amountDetail = operationDetail.getAmountDetail();
    Optional<PortfolioAsset> maybePortfolioAsset =
        maybeAssetDetails.map(
            assetDetail ->
                findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency.findsert(
                    assetDetail.getAssetName(),
                    "Instrumentos",
                    assetDetail.getAssetType(),
                    assetDetail.getAssetAbbreviation(),
                    assetDetail.getCurrencyAbbreviation(),
                    Optional.of(Integer.valueOf(assetDetail.getAssetId().intValue()))));
    PortfolioAsset currencyAsset =
        findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency.findsert(
            amountDetail.getFormattedCurrency(),
            "Monedas",
            "Monedas",
            amountDetail.getCurrencyAbbreviation(),
            amountDetail.getCurrencyAbbreviation(),
            Optional.empty());
    Optional<PortfolioMovement> maybePortfolioAssetMovement =
        maybePortfolioAsset.map(
            asset ->
                createPortfolioMovementFromExternalMovement.create(
                    operationDetail, portfolioAccount, asset));
    operationDetail.makeCurrencyOperation();
    validateNoPortfolioMovementExistsForOperationId.validate(operationDetail.getOperationId());
    Optional<PortfolioMovement> currencyPortfolioMovement =
        Optional.of(
            createPortfolioMovementFromExternalMovement.create(
                operationDetail, portfolioAccount, currencyAsset));
    return new ProcessOperationDetailResponse(
        maybePortfolioAssetMovement, currencyPortfolioMovement);
  }

  private BigDecimal calculateSharesAmount(FundDetail details, AmountDetail amountDetail) {
    if (details.getQuantity() == null) {
      return amountDetail.getNetAmount().divide(details.getFundPrice(), RoundingMode.HALF_UP);
    }
    return details.getQuantity();
  }
}
