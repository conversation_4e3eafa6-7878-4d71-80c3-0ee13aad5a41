package ar.com.daruma.bymas.kinesisEvent.presentation.controller;

import ar.com.daruma.bymas.kinesisEvent.application.process.KinesisWebhookProcessor;
import ar.com.daruma.bymas.kinesisEvent.domain.service.ReprocessEventsService;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/kinesis")
public class KinesisController {

  @Autowired private KinesisWebhookProcessor kinesisWebhookProcessor;
  @Autowired private ReprocessEventsService reprocessEventService;

  @PostMapping("")
  @BymasAuthorization
  public ResponseEntity<Void> execute(@Valid @RequestBody KinesisRequest body) {
    kinesisWebhookProcessor.execute(body);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/reprocess/failed-events")
  @BymasAuthorization
  public ResponseEntity<Void> reprocess(
      @RequestParam(name = "id", required = false) Optional<Integer> eventId,
      @RequestParam(name = "date", required = false) Optional<LocalDate> date) {
    reprocessEventService.reprocessFailedEvents(eventId, date);
    return ResponseEntity.ok().build();
  }
}
