package ar.com.daruma.bymas.kinesisEvent.infrastructure.adapter;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.exceptions.KinesisNotFoundError;
import ar.com.daruma.bymas.kinesisEvent.domain.service.EventService;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEvent;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEventState;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.repository.EventRepository;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EventServiceImpl implements EventService {

  @Autowired private EventRepository eventRepository;
  @Autowired private ObjectMapper objectMapper;

  public void updateSuccessfulEvent(Integer newEventId, KinesisHandlerResponse response) {
    try {
      KinesisEvent event =
          eventRepository
              .findById(newEventId)
              .orElseThrow(
                  () ->
                      new KinesisNotFoundError(
                          String.format("Event with id [%s] not found", newEventId)));
      event.setState(
          response.getIsUnknown()
              ? KinesisEventState.SUCCESSFUL_UNKNOWN
              : KinesisEventState.SUCCESSFUL);
      event.setPortfolioAccountId(
          response.getPortfolioAccount().map(PortfolioAccount::getId).orElse(null));
      eventRepository.save(event);
    } catch (Exception e) {
      throw new RuntimeException(
          "Failed trying to update successful event, reason: " + e.getMessage());
    }
  }

  public void updateFailedEvent(Integer newEventId, Exception error) {
    try {
      KinesisEvent event =
          eventRepository
              .findById(newEventId)
              .orElseThrow(
                  () ->
                      new KinesisNotFoundError(
                          String.format("Event with id [%s] not found", newEventId)));
      event.setState(KinesisEventState.FAILED);
      event.setError(error.getMessage());
      eventRepository.save(event);
    } catch (Exception e) {
      throw new RuntimeException("Failed trying to update failed event" + e.getMessage());
    }
  }

  public Integer savePendingProcessingEvent(KinesisRequest record) {
    try {
      KinesisEvent event = new KinesisEvent();
      event.setKinesisEvent(objectMapper.valueToTree(record));
      event.setState(KinesisEventState.PENDING_PROCESSING);
      event.setTableName(record.getEventDetail().getTableName());
      event.setPortfolioMovementExternalId(record.getExternalId());
      KinesisEvent newEvent = eventRepository.save(event);
      return newEvent.getId();
    } catch (Exception e) {
      throw new RuntimeException(
          "Failed trying to save pending processing event: " + e.getMessage());
    }
  }

  @Override
  public List<KinesisEvent> findByParameters(
      Optional<Integer> id, Optional<LocalDate> date, KinesisEventState state) {
    return eventRepository.findByParameters(id.orElse(null), date.orElse(null), state.toString());
  }
}
