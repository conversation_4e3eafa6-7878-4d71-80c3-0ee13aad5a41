package ar.com.daruma.bymas.kinesisEvent.infrastructure.entities;

import ar.com.daruma.bymas.utils.infrastructure.AuditableEntity;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = "kinesis_events")
public class KinesisEvent extends AuditableEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "kinesis_event", nullable = false)
  @JdbcTypeCode(SqlTypes.JSON)
  private JsonNode kinesisEvent;

  @Enumerated(EnumType.STRING)
  @Column(name = "state", nullable = false)
  private KinesisEventState state;

  @Column(name = "portfolio_account_id")
  private UUID portfolioAccountId;

  @Column(name = "portfolio_movement_external_id")
  private Long portfolioMovementExternalId;

  @Column(name = "error")
  private String error;

  @Column(name = "last_attempt_time")
  private LocalDateTime lastAttemptTime;

  @Column(name = "last_error")
  private String lastError;

  @Column(name = "table_name", nullable = false)
  private String tableName;
}
