package ar.com.daruma.bymas.kinesisEvent.domain.handlers;

import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.service.KinesisHandler;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.util.logging.Logger;
import org.springframework.stereotype.Service;

@Service
public class DefaultRecordHandler extends DiscordSender implements KinesisHandler {

  private static final Logger logger = Logger.getLogger(DefaultRecordHandler.class.getName());

  @Override
  public KinesisHandlerResponse execute(KinesisRequest input) {
    KinesisHandlerResponse response = new KinesisHandlerResponse();
    response.setIsUnknown(true);
    String message =
        "Processing unknown kinesis event from table [%s] with external id [%s]"
            .formatted(input.getEventDetail().getTableName(), input.getExternalId());
    logger.info(message);
    return response;
  }
}
