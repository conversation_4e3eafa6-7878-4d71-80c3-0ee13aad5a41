package ar.com.daruma.bymas.kinesisEvent.application.reprocess;

import ar.com.daruma.bymas.kinesisEvent.application.process.KinesisWebhookProcessor;
import ar.com.daruma.bymas.kinesisEvent.domain.service.EventService;
import ar.com.daruma.bymas.kinesisEvent.domain.service.ReprocessEventsService;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEvent;
import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEventState;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.citadel.utils.CustomLogger;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReprocessKinesisEventWorker implements ReprocessEventsService {
  @Autowired private EventService eventService;
  @Autowired private ObjectMapper objectMapper;
  @Autowired private KinesisWebhookProcessor kinesisWebhookProcessor;

  private static final CustomLogger log = CustomLogger.getLogger(ReprocessKinesisEventWorker.class);

  @Override
  public void reprocessFailedEvents(Optional<Integer> id, Optional<LocalDate> date) {
    List<KinesisEvent> failedEvents =
        eventService.findByParameters(id, date, KinesisEventState.FAILED);
    failedEvents.stream().forEach(this::processEvent);
  }

  private void processEvent(KinesisEvent event) {
    try {
      JsonNode eventBody = event.getKinesisEvent();
      KinesisRequest kinesisRequest = objectMapper.treeToValue(eventBody, KinesisRequest.class);
      log.info("Reprocessing event with ID: " + event.getId());
      kinesisWebhookProcessor.process(kinesisRequest, Optional.of(event.getId()));
    } catch (Exception e) {
      throw new RuntimeException(
          "Failed to reprocess event with ID: " + event.getId() + ". Error: " + e.getMessage(), e);
    }
  }
}
