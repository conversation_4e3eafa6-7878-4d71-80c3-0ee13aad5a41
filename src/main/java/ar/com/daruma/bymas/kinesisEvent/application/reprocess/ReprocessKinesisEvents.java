package ar.com.daruma.bymas.kinesisEvent.application.reprocess;

import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReprocessKinesisEvents extends DiscordSender {

  @Autowired private ReprocessKinesisEventWorker reprocessKinesisEventWorker;
  private static final CustomLogger log = CustomLogger.getLogger(ReprocessKinesisEvents.class);

  @Async("asyncExecutor")
  public void execute(Optional<Integer> id, Optional<LocalDate> date) {
    try {
      reprocessKinesisEventWorker.reprocessFailedEvents(id, date);
    } catch (Exception e) {
      String message = "Error reprocessing kinesis events, reason: " + e.getMessage();
      notifyDiscord(message);
      log.info(message);
    }
  }
}
