package ar.com.daruma.bymas.kinesisEvent.application.process;

import ar.com.daruma.bymas.configuration.kinesis.KinesisConfig;
import ar.com.daruma.bymas.kinesisEvent.domain.entities.KinesisHandlerResponse;
import ar.com.daruma.bymas.kinesisEvent.domain.exceptions.KinesisBadRequestException;
import ar.com.daruma.bymas.kinesisEvent.domain.handlers.DefaultRecordHandler;
import ar.com.daruma.bymas.kinesisEvent.domain.handlers.KinesisRequestHandler;
import ar.com.daruma.bymas.kinesisEvent.domain.handlers.KinesisRequestUpdateHandler;
import ar.com.daruma.bymas.kinesisEvent.domain.handlers.TransactionUpdateHandler;
import ar.com.daruma.bymas.kinesisEvent.domain.service.EventService;
import ar.com.daruma.bymas.kinesisEvent.domain.service.KinesisHandler;
import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.KinesisRequest;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.citadel.utils.CustomLogger;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.concurrent.DelegatingSecurityContextRunnable;
import org.springframework.stereotype.Service;

@Service
public class KinesisWebhookProcessor extends DiscordSender {

  @Autowired private KinesisConfig kinesisConfig;
  @Autowired private ObjectMapper objectMapper;
  @Autowired private KinesisRequestHandler kinesisRequestHandler;
  @Autowired private KinesisRequestUpdateHandler kinesisRequestUpdateHandler;
  @Autowired private DefaultRecordHandler defaultRecordHandler;
  @Autowired private EventService eventService;
  @Autowired private TransactionUpdateHandler transactionUpdateHandler;

  private static final CustomLogger logger = CustomLogger.getLogger(KinesisWebhookProcessor.class);

  public void execute(KinesisRequest body) {
    if (kinesisConfig.getEnabled()) process(body, Optional.empty());
    else logger.info("Kinesis-webhook is disabled");
  }

  public void process(KinesisRequest kinesisRecord, Optional<Integer> maybeEventId) {
    switch (kinesisRecord.getEventDetail().getTableName()) {
      case "MINUTASASIG", "DISTRIBUCIONESPACA", "CTASCORRIENTESMON", "SOLICITUDESFDO" -> {
        if (kinesisRecord.isInsert()) {
          saveAndExecuteRequest(kinesisRecord, kinesisRequestHandler, maybeEventId);
        } else if (kinesisRecord.isUpdate()) {
          saveAndExecuteRequest(kinesisRecord, kinesisRequestUpdateHandler, maybeEventId);
        }
      }
      case "transferencia" -> {
        if (kinesisRecord.isInsert()) {
          saveAndExecuteRequest(kinesisRecord, kinesisRequestHandler, maybeEventId);
        } else if (kinesisRecord.isUpdate()) {
          saveAndExecuteRequest(kinesisRecord, transactionUpdateHandler, maybeEventId);
        }
      }
      default -> saveAndExecuteRequest(kinesisRecord, defaultRecordHandler, maybeEventId);
    }
  }

  private void saveAndExecuteRequest(
      KinesisRequest record, KinesisHandler handler, Optional<Integer> maybeEventId) {
    Integer newEventId;
    try {
      logger.info(
          "Saving and executing kinesis record with transaction id: {}",
          record.getEventDetail().getTransactionId());
      newEventId = maybeEventId.orElseGet(() -> eventService.savePendingProcessingEvent(record));
    } catch (Exception e) {
      String message =
          "Unexpected exception trying to process kinesis record: "
              + e.getMessage()
              + ", record: "
              + objectMapper.valueToTree(record).asText();
      notifyDiscord(message);
      throw new KinesisBadRequestException(message);
    }
    executeHandlerAsync(handler, record, newEventId);
  }

  private void executeHandlerAsync(
      KinesisHandler handler, KinesisRequest record, Integer newEventId) {

    Runnable task =
        () -> {
          try {
            logger.info(
                "Starting to process record with transaction id: {}",
                record.getEventDetail().getTransactionId());
            KinesisHandlerResponse response = handler.execute(record);
            logger.info(
                "Finished processing record with transaction id: {}",
                record.getEventDetail().getTransactionId());
            eventService.updateSuccessfulEvent(newEventId, response);
          } catch (Exception e) {
            String message =
                String.format(
                    "Failed to process record, reason [%s] with transaction id [%s]",
                    e.getMessage(), record.getEventDetail().getTransactionId());
            notifyDiscord(message);
            logger.info(message);
            eventService.updateFailedEvent(newEventId, e);
          }
        };

    CompletableFuture.runAsync(new DelegatingSecurityContextRunnable(task));
  }
}
