package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AmountDetail {
  private BigDecimal amount;
  private BigDecimal price;

  @NotNull private String currency;

  @NotNull private BigDecimal netAmount;
  @NotNull private BigDecimal grossAmount;
  private BigDecimal allariaFee;
  private BigDecimal allariaFeeVat;
  private BigDecimal marketFee;
  private BigDecimal marketFeeVat;
  private BigDecimal amountToAffectBalance;

  @Schema(hidden = true)
  @JsonIgnore
  public String getFormattedCurrency() {
    return switch (currency) {
      case "Pesos" -> pesoName;
      case "Dólar" -> dolarCableName;
      case "MEP Dólar" -> dolarMepName;
      case "Dólar - MtR" -> "Dólar MtR";
      case "Euros" -> "Euro";
      case "Real" -> "Real";
      default -> currency;
    };
  }

  @Schema(hidden = true)
  @JsonIgnore
  public String getCurrencyAbbreviation() {
    return switch (currency) {
      case "Pesos" -> "ARS";
      case "Peso" -> "ARS";
      case "Dólar", "Dolar Garantia MTR", "DOLAR ROFEX  A3500", "DOLAR \"A 3500\"" -> "USD";
      case "MEP Dólar" -> "USD-M";
      case "Dólar MEP" -> "USD-M";
      case "Dólar - MtR" -> "USD-MtR";
      case "Euros" -> "EUR";
      case "Real" -> "BRL";
      case "Yuan" -> "CNY";
      default -> throw new RuntimeException(
          "No abbreviation for amountDetailCurrency: " + currency);
    };
  }
}
