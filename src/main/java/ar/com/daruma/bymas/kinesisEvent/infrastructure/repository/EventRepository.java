package ar.com.daruma.bymas.kinesisEvent.infrastructure.repository;

import ar.com.daruma.bymas.kinesisEvent.infrastructure.entities.KinesisEvent;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EventRepository extends JpaRepository<KinesisEvent, Integer> {
  @Query(
      value =
          "SELECT * FROM kinesis_events e "
              + "WHERE (:id IS NULL OR e.id = :id) "
              + "AND (:date IS NULL OR e.created_at = :date) "
              + "AND e.state = CAST(:state AS event_state) "
              + "ORDER BY e.created_at ASC",
      nativeQuery = true)
  List<KinesisEvent> findByParameters(
      @Param("id") Integer id, @Param("date") LocalDate date, @Param("state") String state);
}
