package ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies;

import ar.com.daruma.bymas.kinesisEvent.presentation.entities.deserializer.FutureOpDetailDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class KinesisRequest {
  @Valid @NotNull private EventDetail eventDetail;
  @Valid @NotNull private AccountDetail accountDetail;

  @NotNull private Long externalId;
  @NotNull private Boolean isAnnulled;
  @NotNull @Valid private OperationDetail operationDetail;

  @Getter(AccessLevel.NONE)
  @Valid
  @JsonDeserialize(using = FutureOpDetailDeserializer.class)
  private OperationDetail futureOperationDetail;

  private Long term;
  private FundDetail fundsDetail;

  @Schema(hidden = true)
  @JsonIgnore
  public Optional<OperationDetail> getFutureOperationDetail() {
    return Optional.ofNullable(futureOperationDetail);
  }

  @Schema(hidden = true)
  @JsonIgnore
  public boolean isInsert() {
    return eventDetail.getOperationType().equals("insert");
  }

  @Schema(hidden = true)
  @JsonIgnore
  public boolean isUpdate() {
    return eventDetail.getOperationType().equals("update");
  }
}
