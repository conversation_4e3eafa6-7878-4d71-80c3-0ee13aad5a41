package ar.com.daruma.bymas.kinesisEvent.domain.entities;

import ar.com.daruma.bymas.kinesisEvent.domain.deserializer.KinesisRecordDefaultDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonDeserialize(using = KinesisRecordDefaultDeserializer.class)
public class KinesisDefaultOperation extends KinesisEntity {

  public KinesisDefaultOperation(Long allariaAccountCode) {
    this.allariaAccountCode = allariaAccountCode;
  }
}
