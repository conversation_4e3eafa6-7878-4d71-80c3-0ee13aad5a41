package ar.com.daruma.bymas.kinesisEvent.domain.entities;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ProcessOperationDetailResponse {
  private Optional<PortfolioMovement> maybePortfolioAssetMovement;
  private Optional<PortfolioMovement> maybeCurrencyPortfolioMovement;

  public PortfolioAccount getPortfolioAccount() {
    return maybePortfolioAssetMovement
        .map(PortfolioMovement::getPortfolioAccount)
        .orElseGet(
            () ->
                maybeCurrencyPortfolioMovement
                    .map(PortfolioMovement::getPortfolioAccount)
                    .orElse(null));
  }
}
