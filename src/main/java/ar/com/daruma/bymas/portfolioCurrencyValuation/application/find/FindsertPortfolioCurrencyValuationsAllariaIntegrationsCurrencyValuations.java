package ar.com.daruma.bymas.portfolioCurrencyValuation.application.find;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.portfolioCurrencyValuation.domain.PortfolioCurrencyValuationService;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindsertPortfolioCurrencyValuationsAllariaIntegrationsCurrencyValuations {

  @Autowired private PortfolioCurrencyValuationService portfolioCurrencyValuationService;

  public List<PortfolioCurrencyValuation> findsert(Set<CurrencyValuation> currencyValuations) {
    log.info(
        "Finding or inserting {} portfolio currency valuations from allaria integrations currency valuations",
        currencyValuations.size());
    List<PortfolioCurrencyValuation> portfolioCurrencyValuations =
        portfolioCurrencyValuationService.findsertByAllariaIntegrationsCurrencyValuations(
            currencyValuations);
    log.info(
        "Found or inserted {} portfolio currency valuations from allaria integrations currency valuations",
        portfolioCurrencyValuations.size());
    return portfolioCurrencyValuations;
  }
}
