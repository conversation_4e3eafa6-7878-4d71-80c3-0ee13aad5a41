package ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.repository;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioCurrencyValuationRepository
    extends JpaRepository<PortfolioCurrencyValuation, UUID> {
  List<PortfolioCurrencyValuation> findByCode(String code);

  List<PortfolioCurrencyValuation> findByCreatedAtBetween(LocalDate startDate, LocalDate endDate);

  Optional<PortfolioCurrencyValuation> findByNameAndCode(String name, String code);

  @Query(
      value =
          "SELECT pcv FROM PortfolioCurrencyValuation pcv WHERE "
              + "CONCAT(pcv.name, '|', pcv.code) IN :searchKeys")
  List<PortfolioCurrencyValuation> findByMultipleCriteria(
      @Param("searchKeys") List<String> searchKeys);
}
