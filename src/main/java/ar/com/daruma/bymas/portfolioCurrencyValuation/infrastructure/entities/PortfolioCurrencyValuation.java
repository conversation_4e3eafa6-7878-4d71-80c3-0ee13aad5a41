package ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities;

import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_currency_valuations")
public class PortfolioCurrencyValuation {
  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @Column(nullable = false)
  private String name;

  @Column(nullable = false)
  private String code;

  @Column(name = "created_at", nullable = false)
  private LocalDate createdAt;

  public PortfolioCurrencyValuation() {}

  public PortfolioCurrencyValuation(String name, String code) {
    this.name = name;
    this.code = code;
    this.createdAt = BuenosAiresTime.nowAsLocalDate();
  }
}
