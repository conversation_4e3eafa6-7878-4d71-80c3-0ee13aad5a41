package ar.com.daruma.bymas.portfolioCurrencyValuation.application.findsert;

import ar.com.daruma.bymas.portfolioCurrencyValuation.domain.PortfolioCurrencyValuationService;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindsertPortfolioCurrencyValuationByNameAndCode {

  @Autowired private PortfolioCurrencyValuationService portfolioCurrencyValuationService;

  private static final Logger logger =
      LogManager.getLogger(FindsertPortfolioCurrencyValuationByNameAndCode.class);

  public PortfolioCurrencyValuation findsert(String name, String code) {
    logger.info(
        "Finding or inserting portfolio currency valuation with name {} and code {}", name, code);
    PortfolioCurrencyValuation currencyValuation =
        portfolioCurrencyValuationService.findsertByNameAndCode(name, code);
    logger.info(
        "Portfolio currency valuation with id {} found or inserted", currencyValuation.getId());
    return currencyValuation;
  }
}
