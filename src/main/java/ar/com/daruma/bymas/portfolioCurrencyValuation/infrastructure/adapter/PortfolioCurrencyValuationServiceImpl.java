package ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.portfolioCurrencyValuation.domain.PortfolioCurrencyValuationService;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.repository.PortfolioCurrencyValuationRepository;
import java.time.LocalDate;
import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioCurrencyValuationServiceImpl implements PortfolioCurrencyValuationService {
  @Autowired private PortfolioCurrencyValuationRepository repository;

  @Override
  public PortfolioCurrencyValuation save(PortfolioCurrencyValuation valuation) {
    return repository.save(valuation);
  }

  @Override
  public Optional<PortfolioCurrencyValuation> findById(UUID id) {
    return repository.findById(id);
  }

  @Override
  public List<PortfolioCurrencyValuation> findByCode(String code) {
    return repository.findByCode(code);
  }

  @Override
  public List<PortfolioCurrencyValuation> findByCreatedAtBetween(
      LocalDate startDate, LocalDate endDate) {
    return repository.findByCreatedAtBetween(startDate, endDate);
  }

  @Override
  public List<PortfolioCurrencyValuation> findAll() {
    return repository.findAll();
  }

  @Override
  public PortfolioCurrencyValuation findsertByNameAndCode(String name, String code) {
    return repository
        .findByNameAndCode(name, code)
        .orElseGet(() -> save(new PortfolioCurrencyValuation(name, code)));
  }

  @Override
  public List<PortfolioCurrencyValuation> findsertByAllariaIntegrationsCurrencyValuations(
      Set<CurrencyValuation> currencyValuations) {
    List<String> searchKeys =
        currencyValuations.stream().map(cv -> cv.getDescription() + "|" + cv.getCode()).toList();
    List<PortfolioCurrencyValuation> existingValuations =
        repository.findByMultipleCriteria(searchKeys);
    List<PortfolioCurrencyValuation> newValuations =
        currencyValuations.stream()
            .filter(
                cv ->
                    existingValuations.stream()
                        .noneMatch(
                            existingValuation ->
                                existingValuation.getName().equals(cv.getDescription())
                                    && existingValuation.getCode().equals(cv.getCode())))
            .map(cv -> new PortfolioCurrencyValuation(cv.getDescription(), cv.getCode()))
            .toList();
    repository.saveAll(newValuations);
    List<PortfolioCurrencyValuation> allValuations = new ArrayList<>(existingValuations);
    allValuations.addAll(newValuations);
    return allValuations;
  }

  @Override
  public void saveAll(List<PortfolioCurrencyValuation> valuations) {
    repository.saveAll(valuations);
  }
}
