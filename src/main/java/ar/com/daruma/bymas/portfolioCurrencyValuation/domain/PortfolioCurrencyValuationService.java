package ar.com.daruma.bymas.portfolioCurrencyValuation.domain;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface PortfolioCurrencyValuationService {
  PortfolioCurrencyValuation save(PortfolioCurrencyValuation valuation);

  Optional<PortfolioCurrencyValuation> findById(UUID id);

  List<PortfolioCurrencyValuation> findByCode(String code);

  List<PortfolioCurrencyValuation> findByCreatedAtBetween(LocalDate startDate, LocalDate endDate);

  List<PortfolioCurrencyValuation> findAll();

  PortfolioCurrencyValuation findsertByNameAndCode(String name, String code);

  List<PortfolioCurrencyValuation> findsertByAllariaIntegrationsCurrencyValuations(
      Set<CurrencyValuation> currencyValuations);

  void saveAll(List<PortfolioCurrencyValuation> valuations);
}
