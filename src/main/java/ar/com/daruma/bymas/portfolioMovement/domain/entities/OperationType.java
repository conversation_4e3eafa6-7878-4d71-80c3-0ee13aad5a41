package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import lombok.Getter;

@Getter
public enum OperationType {
  DEBIT("DEBIT"),
  CREDIT("CREDIT");

  private final String displayName;

  OperationType(String displayName) {
    this.displayName = displayName;
  }

  public Boolean isCredit() {
    return this == CREDIT;
  }

  public OperationType getOpposite() {
    return this == DEBIT ? CREDIT : DEBIT;
  }
}
