package ar.com.daruma.bymas.portfolioMovement.application.update;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UpdateMovementState {

  private static final Logger logger = LogManager.getLogger(UpdateMovementState.class);
  @Autowired private PortfolioMovementService portfolioMovementService;

  public void execute(PortfolioMovement movement, MovementState state) {
    portfolioMovementService.updateMovementState(movement, state);
    logger.info("Updated movement with ID: {} to state {} ", movement.getId(), state);
  }
}
