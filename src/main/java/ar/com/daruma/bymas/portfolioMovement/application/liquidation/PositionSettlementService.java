package ar.com.daruma.bymas.portfolioMovement.application.liquidation;

import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class PositionSettlementService extends DiscordSender {

  private static final Logger logger = LogManager.getLogger(PositionSettlementService.class);
  @Autowired private PositionSettlementWorker positionSettlementWorker;

  @Async("asyncExecutor")
  public void execute(
      Optional<UUID> movementId,
      Optional<LocalDate> date,
      Boolean shouldProcessLiquidationFailed,
      Optional<UUID> maybeAccountId) {
    try {
      positionSettlementWorker.execute(
          movementId, date, shouldProcessLiquidationFailed, maybeAccountId);
    } catch (Exception e) {
      String message = "Error liquidating movements, reason: " + e.getMessage();
      notifyDiscord(message);
      logger.error(message);
    }
  }
}
