package ar.com.daruma.bymas.portfolioMovement.application.cancel;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CancelPortfolioMovement extends DiscordSender {

  private static final CustomLogger logger = CustomLogger.getLogger(CancelPortfolioMovement.class);

  @Autowired private PortfolioMovementService portfolioMovementService;

  public PortfolioMovement execute(PortfolioMovement portfolioMovement) {
    logger.info("Canceling portfolio movement: {}", portfolioMovement);

    if (portfolioMovement.getState() == MovementState.LIQUIDATED) {
      String message =
          "[WARNING] Canceling liquidated operation [%s]".formatted(portfolioMovement.getId());
      logger.info(message);
    }
    PortfolioMovement updatedMovement = portfolioMovementService.cancelOperation(portfolioMovement);
    logger.info(
        "Bill assignation canceled successfully for portfolio movement: [%s]",
        portfolioMovement.getId());
    return updatedMovement;
  }
}
