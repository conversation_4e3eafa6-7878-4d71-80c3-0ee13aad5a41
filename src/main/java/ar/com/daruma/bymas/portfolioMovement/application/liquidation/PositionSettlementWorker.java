package ar.com.daruma.bymas.portfolioMovement.application.liquidation;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.LiquidationResult;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementToLiquidateOrChangeProcessState;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import java.time.LocalDate;
import java.util.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PositionSettlementWorker extends DiscordSender {

  private static final Logger logger = LogManager.getLogger(PositionSettlementWorker.class);
  @Autowired private PortfolioMovementService portfolioMovementService;
  @Autowired private MovementLiquidationBatchService movementLiquidationBatchService;

  public void execute(
      Optional<UUID> movementId,
      Optional<LocalDate> date,
      Boolean shouldProcessLiquidationFailed,
      Optional<UUID> maybeAccountId) {
    processPendingLiquidation(movementId, date, maybeAccountId);
    if (shouldProcessLiquidationFailed) {
      processLiquidationFailed(movementId, date, maybeAccountId);
    }
  }

  public void processPendingLiquidation(
      Optional<UUID> movementId, Optional<LocalDate> date, Optional<UUID> maybeAccountId) {
    MovementState state = MovementState.PENDING_LIQUIDATION;
    Integer pageSize = 300;
    List<MovementToLiquidateOrChangeProcessState> batch;
    List<LiquidationResult> allResults = new ArrayList<>();
    do {
      batch =
          portfolioMovementService.findMovementsToLiquidateOrChangeProcessState(
              movementId, date, pageSize, state, maybeAccountId);
      if (batch.isEmpty()) break;
      List<LiquidationResult> results = movementLiquidationBatchService.processBatch(batch, date);
      allResults.addAll(results);
    } while (!batch.isEmpty());
    buildNotification(allResults);
  }

  private void processLiquidationFailed(
      Optional<UUID> movementId, Optional<LocalDate> date, Optional<UUID> maybeAccountId) {
    portfolioMovementService
        .changeLiquidationFailedToPendingForMaybeMovementIdMaybeAccountIdAndMaybeDate(
            movementId, maybeAccountId, date);
    processPendingLiquidation(movementId, date, maybeAccountId);
  }

  private void buildNotification(List<LiquidationResult> results) {
    long successCount = results.stream().filter(LiquidationResult::isSuccess).count();
    long failureCount = results.size() - successCount;
    StringBuilder discordMsg = new StringBuilder();
    discordMsg
        .append("\n**Position Settlement Results**\n")
        .append("- Successful: ")
        .append(successCount)
        .append("\n")
        .append("- Failed: ")
        .append(failureCount)
        .append("\n\n");

    logger.info(discordMsg.toString());
    if (!results.isEmpty()) notifyDiscord(discordMsg.toString());
  }
}
