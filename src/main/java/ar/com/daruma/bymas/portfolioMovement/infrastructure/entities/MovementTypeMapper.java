package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import ar.com.daruma.bymas.kinesisEvent.presentation.entities.bodies.AssetDetail;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import java.util.Optional;

public class MovementTypeMapper {

  public static MovementType mapToMovementType(
      MarketOperationType marketOperationType, Optional<AssetDetail> assetDetail) {
    if (marketOperationType == null) {
      throw new IllegalArgumentException("MarketOperationType cannot be null");
    }

    return switch (marketOperationType) {
      case CAUCION_COLOCADOR,
          CAUCION_COLOCADOR_CONTADO,
          CAUCION_COLOCADOR_FUTURO,
          CAUCION_COLOCADOR_NO_GARANTIZADA,
          CAUCION_COLOCADOR_NO_GARANTIZADA_CONTADO,
          CAUCION_COLOCADOR_NO_GARANTIZADA_FUTURO,
          CAUCION_TOMADOR,
          CAUCION_TOMADOR_CONTADO,
          CAUCION_TOMADOR_FUTURO,
          CAUCION_TOMADOR_NO_GARANTIZADA,
          CAUCION_TOMADOR_NO_GARANTIZADA_CONTADO,
          CAUCION_TOMADOR_NO_GARANTIZADA_FUTURO,
          CIERRE_COLOCADOR,
          CIERRE_TOMADOR_CAUCION,
          CIERRE_COLOCADOR_CONTADO,
          CIERRE_COLOCADOR_FUTURO,
          APERTURA_COLOCADOR,
          APERTURA_TOMADOR_CAUCION,
          APERTURA_COLOCADOR_CONTADO,
          APERTURA_TOMADORA_CAUCION,
          APERTURA_COLOCADOR_FUTURO,
          APERTURA_TOMADOR_CONTADO_CAUCION,
          APERTURA_TOMADOR_FUTURO_CAUCION,
          APERTURA_TOMADORA_PASE,
          APERTURA_TOMADOR_CONTADO_PASE,
          APERTURA_TOMADOR_FUTURO_PASE,
          CIERRE_TOMADOR_FUTURO_CAUCION,
          CIERRE_TOMADOR_CONTADO_CAUCION,
          CIERRE_TOMADOR_PASE -> MovementType.CAUTION;
      case COMPRA_CONTADO_3368,
          COMPRA_CONTADO_7637,
          COMPRA_CONTADO,
          COMPRA_CONTADO_CONTINUO_GARANTIZADO,
          COMPRA_CONTADO_CONTINUO_NO_GARANTIZADO,
          VENTA_CONTADO,
          VENTA_CONTADO_CONTINUO_GARANTIZADO,
          VENTA_CONTADO_CONTINUO_NO_GARANTIZADO,
          VENTA_CONTADO_CIRCULAR_3368,
          COLOCACION_PRIMARIA_VENTA,
          COLOCACION_PRIMARIA_COMPRA,
          COMPRA_COLOCACION_PRIMARIA,
          COMPRA_SENEBI_NO_GARANTIZADA,
          VENTA_SENEBI_NO_GARANTIZADA,
          SUBSCRIPCION_COMPRA,
          COMPRA_MAE,
          BLOCK_VENTA,
          BLOCK_COMPRA,
          VENTA_EXTERIOR,
          COMPRA_EXTERIOR,
          COMPRA_EXTERIOR_DESCUBIERTO -> assetDetail
              .map(asset -> asset.getAssetName().contains("CEDEAR"))
              .orElse(false)
          ? MovementType.CEDEARS
          : MovementType.ACTIONS;
      case OPCION_VENTA_LANZADOR,
          OPCION_VENTA_TITULAR,
          OPCION_COMPRA_LANZADOR,
          OPCION_COMPRA_TITULAR,
          EJERCICIO_COMPRA_TITULAR,
          EJERCICIO_VENTA_TITULAR,
          EJERCICIO_VENTA_LANZADOR,
          EJERCICIO_COMPRA_LANZADOR -> MovementType.OPTIONS;
      case FUTURO_VENTA, FUTURO_COMPRA, COMPRA_FUTURO -> MovementType.FUTURES;
      case CANCELACION,
          COMPROBANTE_PAGO,
          OTROS_EGRESOS,
          RECIBO_COBRO,
          OTHER,
          AMORTIZACION,
          RENTA,
          RENTA_Y_AMORTIZACION,
          CANJE,
          DIVIDENDO_EN_ACCIONES,
          DIVIDENDO_Y_CANJE,
          DIVIDENDO_EN_EFECTIVO,
          DIVIDENDO_Y_REVALUO,
          REDUCCION_DE_CAPITAL,
          REVALUO_EN_ACCIONES,
          REVALUO_Y_CANJE,
          SPLIT,
          DIVIDENDO_REVALUO_Y_CANJE,
          OTROS_INGRESOS,
          PRESTAMO_VALOR_TOMADOR,
          CIERRE_POSICION,
          PRESTAMO_VALOR_COLOCADOR,
          DOLAR_FUTURO_AJUSTE_COBRO,
          DOLAR_FUTURO_AJUSTE_PAGO,
          DOLAR_FUTURO_LIQUIDACION,
          MANUAL -> MovementType.MANUAL_MOVEMENT;
      case VENTA_CHEQ, COMPRA_CHEQ, CHEQUES_DIFERIDOS -> MovementType.CHEQ;
      case SUSCRIPCION, RESCATE -> MovementType.FCI;
      case TRANSFER -> MovementType.TRANSFER;
      default -> throw new IllegalArgumentException(
          "No mapping found for MarketOperationType: " + marketOperationType);
    };
  }
}
