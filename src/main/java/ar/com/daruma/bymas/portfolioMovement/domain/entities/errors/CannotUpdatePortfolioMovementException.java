package ar.com.daruma.bymas.portfolioMovement.domain.entities.errors;

import ar.com.daruma.citadel.exceptions.BadRequestException;
import java.util.UUID;

public class CannotUpdatePortfolioMovementException extends BadRequestException {

  public CannotUpdatePortfolioMovementException(UUID movementId, String reason) {
    super(
        "Cannot update portfolio movement with ID %s because of reason: %s"
            .formatted(movementId.toString(), reason));
  }
}
