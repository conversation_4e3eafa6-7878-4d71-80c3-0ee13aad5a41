package ar.com.daruma.bymas.portfolioMovement.application.create;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CreatePortfolioMovementFromExternalMovement {

  @Autowired private PortfolioMovementService portfolioMovementService;

  private static final CustomLogger logger =
      CustomLogger.getLogger(CreatePortfolioMovementFromExternalMovement.class);

  public PortfolioMovement create(
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount,
      PortfolioAsset portfolioAsset) {
    logger.info(
        "Creating portfolio movement from external movement {} for account {} and asset {}",
        externalMovement.getOperationId(),
        portfolioAccount.getId(),
        portfolioAsset.getId());
    PortfolioMovement movement =
        portfolioMovementService.createFromExternalMovement(
            externalMovement, portfolioAccount, portfolioAsset);
    logger.info("Successfully created portfolio movement with ID {}", movement.getId());
    return movement;
  }
}
