package ar.com.daruma.bymas.portfolioMovement.domain;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.FindMovementsInput;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementProcessState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementToLiquidateOrChangeProcessState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface PortfolioMovementService {

  PortfolioMovement save(PortfolioMovement movement);

  PortfolioMovement update(
      PortfolioMovement portfolioMovement,
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount);

  PortfolioMovement createFromExternalMovement(
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount,
      PortfolioAsset portfolioAsset);

  PortfolioMovement cancelOperation(PortfolioMovement portfolioMovement);

  Optional<PortfolioMovement> findMovementByOperationId(String operationId);

  Page<PortfolioMovement> findShowableByFiltersAndAccountIds(
      FindMovementsInput input, List<UUID> portfolioAccountIds, PageRequest pageRequest);

  Page<PortfolioMovement> findShowableByFiltersAndAssetAndAccountIds(
      FindMovementsInput input,
      UUID assetId,
      List<UUID> portfolioAccountIds,
      PageRequest pageRequest);

  List<PortfolioMovement> findAllByExternalId(Long externalId);

  List<MovementToLiquidateOrChangeProcessState> findMovementsToLiquidateOrChangeProcessState(
      Optional<UUID> maybeMovementId,
      Optional<LocalDate> date,
      int limit,
      MovementState state,
      Optional<UUID> maybeAccountId);

  void updateMovementState(PortfolioMovement movement, MovementState state);

  List<PortfolioMovement> findTodayMovementsForAccountAndMaybeAsset(
      PortfolioAccount account, Optional<PortfolioAsset> maybeAsset);

  void updateMovementsState(
      List<UUID> movementsIds, MovementState movementState, LocalDateTime now);

  void updateMovementsProcessState(
      List<UUID> movementsIds, MovementProcessState movementProcessState);

  List<PortfolioMovement> findMovementsThatImpactTodayForAccountsAndMaybeAsset(
      List<PortfolioAccount> accounts, Optional<PortfolioAsset> maybeAsset);

  List<PortfolioMovement> findByIdIn(List<UUID> ids);

  void saveAll(List<PortfolioMovement> portfolioMovements);

  List<PortfolioMovement> findWithin24HourMovementsForMaybeAccountAndAssets(
      Optional<PortfolioAccount> account, List<UUID> assetIds);

  List<PortfolioMovement> findFutureDebitMovementsForMaybeAccount(
      Optional<PortfolioAccount> account);

  List<PortfolioMovement> findImpactingMovementsForPositions(
      List<PortfolioPosition> portfolioPositions);

  void changeLiquidationFailedToPendingForMaybeMovementIdMaybeAccountIdAndMaybeDate(
      Optional<UUID> maybeMovementId, Optional<UUID> maybeAccountId, Optional<LocalDate> date);

  List<PortfolioMovement> findMovementsThatAreSettledForDateAndPositions(
      List<PortfolioPosition> portfolioPositions, LocalDate date);
}
