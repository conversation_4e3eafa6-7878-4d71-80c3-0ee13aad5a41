package ar.com.daruma.bymas.portfolioMovement.application.validations;

import ar.com.daruma.bymas.portfolioMovement.application.find.FindMovementByOperationId;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.errors.PortfolioMovementAlreadyExistsException;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ValidateNoPortfolioMovementExistsForOperationId {

  @Autowired private FindMovementByOperationId findMovementByOperationId;

  private static final CustomLogger logger =
      CustomLogger.getLogger(ValidateNoPortfolioMovementExistsForOperationId.class);

  @Transactional(transactionManager = "byMasTransactionManager")
  public void validate(String operationId) {
    logger.info("Validating no portfolio movement exists for operation ID {}", operationId);
    findMovementByOperationId
        .findMaybeMovement(operationId)
        .ifPresent(
            existingMovement -> {
              logger.error(
                  "Portfolio movement with operation ID {} already exists for ID: {}",
                  operationId,
                  existingMovement.getId());
              throw new PortfolioMovementAlreadyExistsException(operationId);
            });
    logger.info("No portfolio movement found for operation ID {}, validation passed", operationId);
  }
}
