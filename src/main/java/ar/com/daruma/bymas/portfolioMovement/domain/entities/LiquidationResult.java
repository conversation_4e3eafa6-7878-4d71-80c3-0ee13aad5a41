package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementToLiquidateOrChangeProcessState;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LiquidationResult {
  private MovementToLiquidateOrChangeProcessState movement;
  private boolean success;
  private String errorMessage;

  public boolean isFailed() {
    return !success;
  }
}
