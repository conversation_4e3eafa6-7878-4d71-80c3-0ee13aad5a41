package ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter;

import static java.util.Map.entry;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.errors.InvalidMovementMappingException;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.errors.MovementStateNotFoundException;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.errors.MovementTypeMappingNotFoundException;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class StateMapper {
  private static Map<MovementType, Map<MovementState, List<String>>> stateMapping;
  private static StateMapper INSTANCE;

  @PostConstruct
  public void init() {
    INSTANCE = this;
  }

  public StateMapper() {
    stateMapping =
        Map.ofEntries(
            entry(MovementType.SERVICE_PAYMENT, createServicePaymentMap()),
            entry(MovementType.ECHEQ, createEcheqsMap()),
            entry(MovementType.AUTOMATIC_DEBIT, createAutomaticDebitMap()),
            entry(MovementType.FCI_SUBSCRIPTION, createFciSusbcriptionMap()),
            entry(MovementType.FCI_REDEMPTION, createFciRedemptionMap()),
            entry(MovementType.CARD, createCardsMap()),
            entry(MovementType.PAYMENT_LINK, createPaymentLinkMap()),
            entry(MovementType.RETURN, createReturnMap()),
            entry(MovementType.TRANSFER, createTransferMap()),
            entry(MovementType.VEP, createVepMap()),
            entry(MovementType.FUNDS_TRANSFER, createFundsTransfer()),
            entry(MovementType.INVOICE_OPERATION, createInvoiceOperation()),
            entry(MovementType.PAYMENT_OPERATION, createPaymentOperation()),
            entry(MovementType.CARD_INVITE, createCardInvite()),
            entry(MovementType.PROGRAMMED_PAYMENT, createProgrammedPayment()));
  }

  private Map<MovementState, List<String>> createCardInvite() {
    return Map.of(
        MovementState.APPROVED, List.of("APPROVED"),
        MovementState.PENDING, List.of("PENDING", "PENDING_VALIDATION", "PARTIALLY_APPROVED"),
        MovementState.REJECTED, List.of("REJECTED", "VALIDATION_FAILED", "REJECTED_VALIDATION"),
        MovementState.EXPIRED, List.of("EXPIRED"));
  }

  private Map<MovementState, List<String>> createProgrammedPayment() {
    return Map.of(
        MovementState.COMPLETED,
        List.of("COMPLETED", "APPROVED_NO_FUNDS"),
        MovementState.APPROVED,
        List.of("APPROVED"),
        MovementState.CANCELED,
        List.of("CANCELLED"),
        MovementState.PENDING,
        List.of("PENDING_APPROVAL", "PARTIALLY_APPROVED"),
        MovementState.FAILED,
        List.of("FAILED"));
  }

  private Map<MovementState, List<String>> createServicePaymentMap() {
    return Map.of(
        MovementState.COMPLETED,
        List.of(
            "COMPLETED",
            "SERVICE_PROVIDER_SUCCESS",
            "APPROVED_NO_FUNDS",
            "APPROVED_LOCKED",
            "SERVICE_PROVIDER_UNKNOWN",
            "COMPLETED_BY_BULK",
            "SERVICE_PROVIDER_SUCCESS_BY_BULK"),
        MovementState.APPROVED,
        List.of("APPROVED", "APPROVED_BY_BULK"),
        MovementState.CANCELED,
        List.of("CANCELED", "CANCELLED_BY_BULK"),
        MovementState.PENDING,
        List.of(
            "PENDING_APPROVAL",
            "PARTIALLY_APPROVED",
            "PENDING_APPROVAL_BY_BULK",
            "PARTIALLY_APPROVED_BY_BULK"),
        MovementState.FAILED,
        List.of("FAILED", "FAILED_BY_BULK", "SERVICE_PROVIDER_UNKNOWN_BY_BULK"));
  }

  private Map<MovementState, List<String>> createEcheqsMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("COMPLETED", "PENDING_ORDER_CREATED"),
        MovementState.FAILED, List.of("FAILED"),
        MovementState.APPROVED, List.of("APPROVED", "PENDING_ORDER_CREATION", "PENDING_CREATION"),
        MovementState.CANCELED, List.of("ABORTED"),
        MovementState.REJECTED, List.of("REJECTED"),
        MovementState.PENDING, List.of("PENDING_APPROVAL", "PARTIALLY_APPROVED"));
  }

  private Map<MovementState, List<String>> createAutomaticDebitMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("PAID"),
        MovementState.PENDING_LIQUIDATION, List.of("PENDING_LIQUIDATION"));
  }

  private Map<MovementState, List<String>> createFciSusbcriptionMap() {
    return Map.of(
        MovementState.COMPLETED,
        List.of("COMPLETED", "PENDING_NOTIFY"),
        MovementState.FAILED,
        List.of("FAILED"));
  }

  private Map<MovementState, List<String>> createFciRedemptionMap() {
    return Map.of(
        MovementState.COMPLETED,
        List.of("COMPLETED", "LIQUIDATED"),
        MovementState.PENDING_LIQUIDATION,
        List.of("PENDING_SETTLEMENT", "PENDING_NOTIFY"),
        MovementState.PARTIALLY_LIQUIDATED,
        List.of("PARTIALLY_LIQUIDATED"),
        MovementState.FAILED,
        List.of("FAILED"));
  }

  private Map<MovementState, List<String>> createCardsMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("COMPLETED", "CHECKING_BALANCE"),
        MovementState.PENDING, List.of("CLAIMED"), // TODO confirmar
        MovementState.REJECTED, List.of("REJECTED"),
        MovementState.REFUNDED, List.of("REFUND", "REVERSAL_PURCHASE", "RETURNED"));
  }

  private Map<MovementState, List<String>> createPaymentLinkMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("LIQUIDATED"),
        MovementState.PENDING_LIQUIDATION, List.of("PENDING_LIQUIDATION"),
        MovementState.REFUNDED, List.of("REFUNDED"));
  }

  private Map<MovementState, List<String>> createReturnMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("COMPLETED"),
        MovementState.APPROVED, List.of("PARTIALLY_SIGNED"),
        MovementState.PENDING, List.of("PENDING_APPROVAL", "TRANSFERRED_PENDING_ORDER_CREATION"),
        MovementState.FAILED, List.of("FAILED", "UNKNOWN"),
        MovementState.CANCELED, List.of("CANCELED"));
  }

  private Map<MovementState, List<String>> createTransferMap() {
    return Map.of(
        MovementState.IN_PROGRESS,
        List.of("CREATION_IN_PROGRESS"),
        MovementState.COMPLETED,
        List.of("COMPLETED"),
        MovementState.APPROVED,
        List.of(
            "APPROVED_AND_LOCKED",
            "PROCESSED",
            "PENDING_FUNDS",
            "PENDING_FUNDS_MANUAL",
            "PENDING_FUNDS_OUT_MARKET",
            "APPROVED",
            "UNKNOWN"),
        MovementState.PENDING,
        List.of("PENDING_APPROVAL", "PARTIALLY_APPROVED"),
        MovementState.REJECTED,
        List.of("REJECTED"),
        MovementState.CANCELED,
        List.of("CANCELED"),
        MovementState.FAILED,
        List.of("FAILED"));
  }

  private Map<MovementState, List<String>> createVepMap() {
    return Map.of(
        MovementState.COMPLETED, List.of("COMPLETED"),
        MovementState.APPROVED, List.of("APPROVED", "PENDING_FUNDS"),
        MovementState.PENDING_LIQUIDATION, List.of(),
        MovementState.PENDING, List.of("PENDING_APPROVAL", "PARTIALLY_APPROVED"),
        MovementState.CANCELED, List.of("ABORTED"),
        MovementState.FAILED, List.of("FAILED"),
        MovementState.EXPIRED, List.of("EXPIRED"));
  }

  private Map<MovementState, List<String>> createFundsTransfer() {
    return Map.of(
        MovementState.COMPLETED, List.of("COMPLETED"),
        MovementState.APPROVED, List.of("PARTIALLY_SIGNED"),
        MovementState.PENDING_LIQUIDATION, List.of(),
        MovementState.PENDING, List.of("TRANSFERRED_PENDING_ORDER_CREATION", "PENDING_APPROVAL"),
        MovementState.CANCELED, List.of("CANCELED"),
        MovementState.FAILED, List.of("FAILED"),
        MovementState.EXPIRED, List.of());
  }

  private Map<MovementState, List<String>> createInvoiceOperation() {
    return Map.of(
        MovementState.COMPLETED,
        List.of("COMPLETED"),
        MovementState.APPROVED,
        List.of("APPROVED", "PENDING_FUNDS"),
        MovementState.PENDING,
        List.of("PENDING_CONFIRM", "PENDING_APPROVAL", "PARTIALLY_APPROVED"),
        MovementState.CANCELED,
        List.of("ABORTED"),
        MovementState.FAILED,
        List.of("FAILED"),
        MovementState.EXPIRED,
        List.of("EXPIRED"));
  }

  private Map<MovementState, List<String>> createPaymentOperation() {
    return Map.of(
        MovementState.COMPLETED,
        List.of("COMPLETED"),
        MovementState.APPROVED,
        List.of("APPROVED"),
        MovementState.PENDING,
        List.of("PENDING_APPROVAL", "PENDING_FUNDS", "PARTIALLY_APPROVED"),
        MovementState.CANCELED,
        List.of("ABORTED"),
        MovementState.FAILED,
        List.of("FAILED"),
        MovementState.EXPIRED,
        List.of("EXPIRED"));
  }

  public static MovementState getMovementState(MovementType movementType, String value) {
    try {
      if (movementType == null || value == null) {
        throw new InvalidMovementMappingException("The transaction type and value cannot be null");
      }
      Map<MovementState, List<String>> typeMap = stateMapping.get(movementType);
      if (typeMap == null) {
        throw new MovementTypeMappingNotFoundException(movementType);
      }
      return typeMap.entrySet().stream()
          .filter(entry -> entry.getValue().contains(value))
          .map(Map.Entry::getKey)
          .findFirst()
          .orElseThrow(() -> new MovementStateNotFoundException(movementType, value));

    } catch (IllegalStateException e) {
      throw new InvalidMovementMappingException(
          "Error while processing movement state mapping: " + e.getMessage());
    }
  }
}
