package ar.com.daruma.bymas.portfolioMovement.infrastructure.repository;

import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementProcessState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementToLiquidateView;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioMovementRepository extends JpaRepository<PortfolioMovement, UUID> {

  Optional<PortfolioMovement> findByOperationId(String operationId);

  List<PortfolioMovement> findAllByExternalId(Long externalId);

  @Query(
      value =
          "SELECT * FROM portfolio_movements "
              + "WHERE visibility = CAST('IS_SHOWN' as movement_visibility) "
              + "AND portfolio_account_id IN (:portfolioAccountIds) "
              + "AND agreement_at >= COALESCE(:fromDate, agreement_at) "
              + "AND agreement_at <= COALESCE(:toDate, agreement_at) "
              + "AND (:operation IS NULL OR market_operation_type = CAST(:operation as VARCHAR)) "
              + "AND (:id IS NULL OR id = CAST(:id as UUID)) "
              + "AND (:state IS NULL OR state = CAST(:state as VARCHAR)) "
              + "AND (:currency IS NULL OR currency = CAST(:currency as VARCHAR)) "
              + "AND (:movementId IS NULL OR external_id = :movementId) ",
      nativeQuery = true)
  Page<PortfolioMovement> findShowableByFiltersAndMultipleAccounts(
      @Param("portfolioAccountIds") List<UUID> portfolioAccountIds,
      @Param("fromDate") LocalDate fromDate,
      @Param("toDate") LocalDate toDate,
      @Param("operation") String operation,
      @Param("id") String id,
      @Param("state") String state,
      @Param("currency") String currency,
      @Param("movementId") Integer movementId,
      Pageable pageable);

  @Query(
      value =
          "SELECT * FROM portfolio_movements "
              + "WHERE visibility = CAST('IS_SHOWN' as movement_visibility) "
              + "AND portfolio_account_id IN (:portfolioAccountIds) "
              + "AND portfolio_asset_id = :assetId "
              + "AND agreement_at >= COALESCE(:fromDate, agreement_at) "
              + "AND agreement_at <= COALESCE(:toDate, agreement_at) "
              + "AND (:operation IS NULL OR market_operation_type = CAST(:operation as VARCHAR)) "
              + "AND (:id IS NULL OR id = CAST(:id as UUID)) "
              + "AND (:state IS NULL OR state = CAST(:state as VARCHAR)) "
              + "AND (:currency IS NULL OR currency = CAST(:currency as VARCHAR)) "
              + "AND (:movementId IS NULL OR external_id = :movementId) ",
      nativeQuery = true)
  Page<PortfolioMovement> findShowableByFiltersAndAssetAndMultipleAccounts(
      @Param("portfolioAccountIds") List<UUID> portfolioAccountIds,
      @Param("assetId") UUID assetId,
      @Param("fromDate") LocalDate fromDate,
      @Param("toDate") LocalDate toDate,
      @Param("operation") String operation,
      @Param("id") String id,
      @Param("state") String state,
      @Param("currency") String currency,
      @Param("movementId") Integer movementId,
      Pageable pageable);

  @Query(
      value =
          """
                          SELECT
                            pm.id AS id,
                            pm.portfolio_asset_id AS assetId,
                            pm.portfolio_account_id AS accountId,
                            pm.movement_type AS movementType,
                            pm.operation_type AS operationType,
                            pm.quantity,
                            pm.net_amount,
                            EXISTS (
                              SELECT 1
                              FROM portfolio_positions pp
                              WHERE pp.portfolio_asset_id = pm.portfolio_asset_id
                                AND pp.portfolio_account_id = pm.portfolio_account_id
                                AND DATE(pp.last_updated_at) = :date
                            ) AS positionHasTodayDate,
                            EXISTS (
                              SELECT 1
                              FROM portfolio_position_history pph
                              WHERE pph.portfolio_asset_id = pm.portfolio_asset_id
                                AND pph.portfolio_account_id = pm.portfolio_account_id
                                AND pph.date = :historyDate
                            ) AS hasSynchronizedHistory,
                            pm.settlement_at as settlementAt
                          FROM portfolio_movements pm
                          WHERE pm.state = :state
                            AND pm.agreement_at <= pm.settlement_at
                            AND (
                                 pm.settlement_at = :date OR
                                 (pm.settlement_at = :tomorrowDate AND pm.movement_process_state = 'BEYOND_24_HOURS')
                            )
                            AND pm.id = :id
                            AND pm.movement_type != 'TRANSFER'
                            ORDER BY pm.id
                            LIMIT :limit
                            FOR UPDATE SKIP LOCKED
                          """,
      nativeQuery = true)
  List<MovementToLiquidateView> findMovementToLiquidateOrChangeProcessStateById(
      @Param("id") UUID id,
      @Param("date") LocalDate date,
      @Param("tomorrowDate") LocalDate tomorrowDate,
      @Param("historyDate") LocalDate historyDate,
      @Param("limit") int limit,
      @Param("state") String state);

  @Query(
      value =
          """
                          SELECT
                            pm.id AS id,
                            pm.portfolio_asset_id AS assetId,
                            pm.portfolio_account_id AS accountId,
                            pm.movement_type AS movementType,
                            pm.operation_type AS operationType,
                            pm.quantity,
                            pm.net_amount,
                            EXISTS (
                              SELECT 1
                              FROM portfolio_positions pp
                              WHERE pp.portfolio_asset_id = pm.portfolio_asset_id
                                AND pp.portfolio_account_id = pm.portfolio_account_id
                                AND DATE(pp.last_updated_at) = :date
                            ) AS positionHasTodayDate,
                            EXISTS (
                              SELECT 1
                              FROM portfolio_position_history pph
                              WHERE pph.portfolio_asset_id = pm.portfolio_asset_id
                                AND pph.portfolio_account_id = pm.portfolio_account_id
                                AND pph.date = :historyDate
                            ) AS hasSynchronizedHistory,
                            pm.settlement_at AS settlementAt
                          FROM portfolio_movements pm
                          WHERE pm.state = :state
                            AND pm.agreement_at <= pm.settlement_at
                            AND (pm.settlement_at = :date OR (pm.settlement_at = :tomorrowDate  AND pm.movement_process_state = 'BEYOND_24_HOURS'))
                            AND pm.movement_type != 'TRANSFER'
                            ORDER BY pm.id
                            LIMIT :limit
                            FOR UPDATE SKIP LOCKED
                          """,
      nativeQuery = true)
  List<MovementToLiquidateView> findMovementsToLiquidateOrChangeProcessState(
      @Param("date") LocalDate date,
      @Param("tomorrowDate") LocalDate tomorrowDate,
      @Param("historyDate") LocalDate historyDate,
      @Param("limit") int limit,
      @Param("state") String state);

  @Query(
      value =
          """
                                  SELECT
                                    pm.id AS id,
                                    pm.portfolio_asset_id AS assetId,
                                    pm.portfolio_account_id AS accountId,
                                    pm.movement_type AS movementType,
                                    pm.operation_type AS operationType,
                                    pm.quantity,
                                    pm.net_amount,
                                    EXISTS (
                                      SELECT 1
                                      FROM portfolio_positions pp
                                      WHERE pp.portfolio_asset_id = pm.portfolio_asset_id
                                        AND pp.portfolio_account_id = pm.portfolio_account_id
                                        AND DATE(pp.last_updated_at) = :date
                                    ) AS positionHasTodayDate,
                                    EXISTS (
                                      SELECT 1
                                      FROM portfolio_position_history pph
                                      WHERE pph.portfolio_asset_id = pm.portfolio_asset_id
                                        AND pph.portfolio_account_id = pm.portfolio_account_id
                                        AND pph.date = :historyDate
                                    ) AS hasSynchronizedHistory,
                                    pm.settlement_at AS settlementAt
                                  FROM portfolio_movements pm
                                  WHERE pm.state = :state
                                    AND pm.agreement_at <= pm.settlement_at
                                    AND (pm.settlement_at = :date OR (pm.settlement_at = :tomorrowDate AND pm.movement_process_state = 'BEYOND_24_HOURS'))
                                    AND pm.movement_type != 'TRANSFER'
                                    AND pm.portfolio_account_id = :accountId
                                    ORDER BY pm.id
                                    LIMIT :limit
                                    FOR UPDATE SKIP LOCKED
                                  """,
      nativeQuery = true)
  List<MovementToLiquidateView> findMovementsToLiquidateOrChangeProcessStateByAccount(
      @Param("date") LocalDate date,
      @Param("tomorrowDate") LocalDate tomorrowDate,
      @Param("historyDate") LocalDate historyDate,
      @Param("limit") int limit,
      @Param("state") String state,
      @Param("accountId") UUID accountId);

  List<PortfolioMovement> findByPortfolioAccountIdAndPortfolioAssetIdAndSettlementAt(
      UUID portfolioAccountId, UUID portfolioAssetId, LocalDate settlementAt);

  List<PortfolioMovement> findByPortfolioAccountIdAndPortfolioAssetIdAndAgreementAt(
      UUID portfolioAccountId, UUID portfolioAssetId, LocalDate agreementAt);

  List<PortfolioMovement> findByPortfolioAccountIdAndSettlementAt(
      UUID portfolioAccountId, LocalDate settlementAt);

  List<PortfolioMovement> findByPortfolioAccountIdAndAgreementAt(
      UUID portfolioAccountId, LocalDate agreementAt);

  @Modifying
  @Query(
      "UPDATE PortfolioMovement p SET p.state = :movementState, p.updatedAt = :now WHERE p.id IN :movementsIds")
  void updateMovementsState(
      @Param("movementsIds") List<UUID> movementsIds,
      @Param("movementState") MovementState movementState,
      @Param("now") LocalDateTime now);

  List<PortfolioMovement> findByPortfolioAccountIdInAndPortfolioAssetIdAndSettlementAt(
      List<UUID> attr0, UUID portfolioAssetId, LocalDate settlementAt);

  List<PortfolioMovement> findByPortfolioAccountIdInAndPortfolioAssetIdAndAgreementAt(
      List<UUID> accountIds, UUID assetId, LocalDate date);

  List<PortfolioMovement> findByPortfolioAccountIdInAndSettlementAt(
      List<UUID> accountIds, LocalDate date);

  List<PortfolioMovement> findByPortfolioAccountIdInAndAgreementAt(
      List<UUID> accountIds, LocalDate date);

  List<PortfolioMovement> findByIdIn(List<UUID> ids);

  @Modifying
  @Query(
      "UPDATE PortfolioMovement p SET p.movementProcessState = :movementProcessState WHERE p.id IN :movementsIds")
  void updateMovementsProcessState(
      @Param("movementsIds") List<UUID> movementsIds,
      @Param("movementProcessState") MovementProcessState movementProcessState);

  @Query(
      value =
          """
                    SELECT pm.*
                    FROM portfolio_movements pm
                    WHERE pm.portfolio_account_id = :accountId
                    AND pm.portfolio_asset_id IN (:assetIds)
                    AND (pm.settlement_at = :now OR pm.settlement_at = :tomorrow)
                    AND pm.movement_process_state = 'WITHIN_24_HOURS'
                    """,
      nativeQuery = true)
  List<PortfolioMovement> findWithin24HourMovementsByAccountIdAndAssetsIdsIn(
      @Param("accountId") UUID accountId,
      @Param("assetIds") List<UUID> assetIds,
      @Param("now") LocalDate now,
      @Param("tomorrow") LocalDate tomorrow);

  @Query(
      value =
          """
                    SELECT pm.*
                    FROM portfolio_movements pm
                    WHERE pm.portfolio_asset_id IN (:assetIds)
                    AND (pm.settlement_at = :now OR pm.settlement_at = :tomorrow)
                    AND pm.movement_process_state = 'WITHIN_24_HOURS'
                    """,
      nativeQuery = true)
  List<PortfolioMovement> findWithin24HourMovementsForAssetIdsIn(
      @Param("assetIds") List<UUID> assetIds,
      @Param("now") LocalDate now,
      @Param("tomorrow") LocalDate tomorrow);

  @Query(
      value =
          """
                    SELECT pm.*
                    FROM portfolio_movements pm
                    WHERE pm.portfolio_account_id = :portfolioAccountId
                    AND pm.operation_type = CAST(:operationType as operation_type)
                    AND pm.settlement_at >= :settlementAt
                    AND pm.movement_process_state != 'SETTLED'
                    """,
      nativeQuery = true)
  List<PortfolioMovement> findByPortfolioAccountAndOperationTypeAndSettlementAtIsAfter(
      @Param("portfolioAccountId") UUID portfolioAccountId,
      @Param("operationType") String operationType,
      @Param("settlementAt") LocalDate settlementAt);

  @Query(
      value =
          """
                    SELECT pm.*
                    FROM portfolio_movements pm
                    WHERE pm.operation_type = CAST(:operationType as operation_type)
                    AND pm.settlement_at >= :settlementAt
                    AND pm.movement_process_state != 'SETTLED'
                    """,
      nativeQuery = true)
  List<PortfolioMovement> findByStateAndSettlementAtIsAfter(
      @Param("operationType") String operationType, @Param("settlementAt") LocalDate settlementAt);

  List<PortfolioMovement>
      findByPortfolioAccountIdInAndPortfolioAssetIdInAndSettlementAtIsGreaterThanEqual(
          Set<UUID> accountIds, Set<UUID> assetIds, LocalDate localDate);

  @Modifying
  @Query(
      "UPDATE PortfolioMovement p SET p.state = :movementState, p.updatedAt = :now WHERE p.portfolioAccount.id = :accountId AND p.settlementAt = :localDate")
  void updateMovementsStateForAccountIdAndDate(
      @Param("accountId") UUID accountId,
      @Param("localDate") LocalDate localDate,
      @Param("movementState") String movementState,
      @Param("now") LocalDateTime now);

  @Modifying
  @Query(
      "UPDATE PortfolioMovement p SET p.state = :movementState, p.updatedAt = :now WHERE p.settlementAt = :localDate")
  void updateMovementsStateForDate(
      @Param("localDate") LocalDate localDate,
      @Param("movementState") String movementState,
      @Param("now") LocalDateTime now);
}
