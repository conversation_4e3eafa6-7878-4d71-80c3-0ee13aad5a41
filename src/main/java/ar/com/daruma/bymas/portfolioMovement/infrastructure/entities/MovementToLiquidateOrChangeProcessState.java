package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class MovementToLiquidateOrChangeProcessState {
  private UUID id;
  private UUID assetId;
  private UUID accountId;
  private MovementType movementType;
  private OperationType operationType;
  private BigDecimal quantity;
  private Boolean positionHasTodayDate;
  private Boolean hasSynchronizedHistory;
  private LocalDate settlementAt;

  public BigDecimal getAmountToApply() {
    int sign = 1;
    if (isDebit()) {
      sign = -1;
    }
    BigDecimal amount = this.getQuantity();
    return amount.multiply(BigDecimal.valueOf(sign));
  }

  public Boolean isDebit() {
    return operationType == OperationType.DEBIT;
  }
}
