package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import lombok.Getter;

@Getter
public enum MovementVisibility {
  IS_SHOWN("IS_SHOWN", true),
  NOT_SHOWN("NOT_SHOWN", false);

  private final String displayName;
  private final Boolean shouldBeShown;

  MovementVisibility(String displayName, Boolean shouldBeShown) {
    this.displayName = displayName;
    this.shouldBeShown = shouldBeShown;
  }
}
