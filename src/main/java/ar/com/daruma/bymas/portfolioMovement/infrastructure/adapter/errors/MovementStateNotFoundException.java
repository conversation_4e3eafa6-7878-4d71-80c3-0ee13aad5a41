package ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.errors;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.citadel.exceptions.NotFoundException;

public class MovementStateNotFoundException extends NotFoundException {
  public MovementStateNotFoundException(MovementType movementType, String value) {
    super(
        "No movement state found for value '%s' in movement type: %s"
            .formatted(value, movementType));
  }
}
