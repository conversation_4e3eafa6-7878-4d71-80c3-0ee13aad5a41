package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import lombok.Getter;

@Getter
public enum MovementState {
  PENDING_LIQUIDATION("PENDING_LIQUIDATION"),
  LIQUIDATION_FAILED("LIQUIDATION_FAILED"),
  LIQUIDATED("LIQUIDATED"),
  CANCELLED("CANCELLED"),
  PENDING("PENDING"),
  PROCESSING("PROCESSING"),
  MANUALLY_PAID("MANUALLY_PAID"),
  FAILED("FAILED"),
  COMPLETED("COMPLETED"),
  APPROVED("APPROVED"),
  REJECTED("REJECTED"),
  REFUNDED("REFUNDED"),
  SCHEDULED("SCHEDULED"),
  CANCELED("CANCELED"),
  PARTIALLY_LIQUIDATED("PARTIALLY_LIQUIDATED"),
  EXPIRED("EXPIRED"),
  IN_PROGRESS("IN_PROGRESS");

  private final String displayName;

  MovementState(String displayName) {
    this.displayName = displayName;
  }

  public static MovementState fromString(String displayName) {
    for (MovementState type : MovementState.values()) {
      if (type.getDisplayName().equalsIgnoreCase(displayName)) {
        return type;
      }
    }
    throw new IllegalArgumentException("No enum constant with displayName " + displayName);
  }
}
