package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import lombok.Getter;

@Getter
public enum MovementType {
  CARD("CARD"),
  PAYMENT_LINK("PAYMENT_LINK"),
  SERVICE_PAYMENT("SERVICE_PAYMENT"),
  ECHEQ("ECHEQ"),
  VEP("VEP"),
  TRANSFER("TRANSFER"),
  RETURN("RETURN"),
  AUTOMATIC_DEBIT("AUTOMATIC_DEBIT"),
  CHEQ("CHEQUES DE PAGO DIFERIDO"),
  ACTIONS("Acciones"),
  TITULO_PUBLICO("Títulos Públicos"),
  TITULOS_PROVINCIALES("Títulos Provinciales"),
  OBLIGACIONES_NEGOCIABLES("Obligaciones Negociables"),
  FONDOS("Fondos"),
  INDICES("Indices"),
  CEDEARS("CEDEAR RENTA VARIABLE"),
  PAGARES("Pagares"),
  MERVAL("INDICE MERVAL"),
  CEDROS("Cedros"),
  LEBAC("LEBAC"),
  FCI("FONDOS DE INVERSION"),
  FCI_SUBSCRIPTION("FONDOS DE INVERSION"),
  FCI_REDEMPTION("FONDOS DE INVERSION"),
  FUTURES("Futuro de Acciones"),
  CEDIN("Cedin"),
  MANUAL_MOVEMENT("MANUAL_MOVEMENT"),
  DOLLAR_MEP("DOLLAR_MEP"),
  CAUTION("CAUTION"),
  OPTIONS("Opciones"),
  FUNDS_TRANSFER("FUNDS_TRANSFER"),
  INVOICE_OPERATION("INVOICE_OPERATION"),
  PAYMENT_OPERATION("PAYMENT_OPERATION"),
  CARD_INVITE("CARD INVITE"),
  PROGRAMMED_PAYMENT("PROGRAMMED_PAYMENT");
  private final String displayName;

  MovementType(String displayName) {
    this.displayName = displayName;
  }

  public static MovementType fromString(String displayName) {
    for (MovementType type : MovementType.values()) {
      if (type.displayName.equalsIgnoreCase(displayName)) {
        return type;
      }
    }
    throw new IllegalArgumentException("No enum constant with displayName " + displayName);
  }
}
