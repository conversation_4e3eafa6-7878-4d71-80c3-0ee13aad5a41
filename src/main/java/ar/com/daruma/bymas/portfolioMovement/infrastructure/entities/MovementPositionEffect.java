package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import lombok.Getter;

@Getter
public enum MovementPositionEffect {
  AFFECTS_BALANCE("AFFECTS_BALANCE", true),
  DOES_NOT_AFFECT_BALANCE("DOES_NOT_AFFECT_BALANCE", false);

  private final String displayName;
  private final Boolean affectsPosition;

  MovementPositionEffect(String displayName, Boolean affectsPosition) {
    this.displayName = displayName;
    this.affectsPosition = affectsPosition;
  }
}
