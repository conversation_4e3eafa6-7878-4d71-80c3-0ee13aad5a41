package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import lombok.Getter;

@Getter
public enum MarketOperationType {
  VENTA_CONTADO_CIRCULAR_3368("Venta Contado Circular 3368"),
  SUBSCRIPCION_COMPRA("Suscripción Compra"),
  EJERCICIO_COMPRA_TITULAR("Ejercicio Compra Titular"),
  EJERCICIO_COMPRA_LANZADOR("Ejercicio Compra Lanzador"),
  EJERCICIO_VENTA_TITULAR("Ejercicio Venta Titular"),
  COLOCACION_PRIMARIA_VENTA("Colocación Primaria Venta A3"),
  COLOCACION_PRIMARIA_COMPRA("Colocación Primaria Compra A3"),
  EJERCICIO_VENTA_LANZADOR("Ejercicio Venta Lanzador"),
  FUTURO_VENTA("Futuro Venta"),
  FUTURO_COMPRA("Futuro Compra"),
  PRESTAMO_VALOR_TOMADOR("Préstamo Valor Tomador"),
  PRESTAMO_VALOR_COLOCADOR("Préstamo Valor Colocador"),
  OPCION_COMPRA_TITULAR("Opción Compra Titular (Call Compra)"),
  OPCION_VENTA_TITULAR("Opción Venta Titular (Put Compra)"),
  OPCION_COMPRA_LANZADOR("Opción Compra Lanzador (Call Venta)"),
  OPCION_VENTA_LANZADOR("Opción Venta Lanzador (Put Venta)"),
  COMPRA_SENEBI_NO_GARANTIZADA("Compra SENEBI No Garantizada"),
  VENTA_SENEBI_NO_GARANTIZADA("Venta SENEBI No Garantizada"),
  VENTA_CHEQ("Venta Cheque de Pago Diferido / Pagaré / FCE / Echeq"),
  APERTURA_COLOCADOR("Apertura Colocador"),
  APERTURA_TOMADORA_PASE("Apertura Tomadora Pase"),
  APERTURA_TOMADOR_CAUCION("Apertura Tomador Caución"),
  APERTURA_TOMADORA_CAUCION("Apertura Tomadora Caución"), // todo: check
  CIERRE_COLOCADOR("Cierre Colocador"),
  CIERRE_TOMADOR_FUTURO_CAUCION("Cierre Tomador Futuro (Tipo Caución)"),
  CIERRE_TOMADOR_CONTADO_CAUCION("Cierre Tomador Contado (Tipo Caución)"),
  COMPRA_CHEQ("Compra Cheque de Pago Diferido / Pagaré / FCE / Echeq"),
  CIERRE_TOMADOR_CAUCION("Cierre Tomador Caución"),
  CIERRE_TOMADOR_PASE("Cierre Tomador Pase"),
  APERTURA_COLOCADOR_CONTADO("Apertura Colocadora Contado"),
  APERTURA_COLOCADOR_FUTURO("Apertura Colocadora Futuro"),
  APERTURA_TOMADOR_CONTADO_CAUCION("Apertura Tomadora Contado (Tipo Caución)"),
  APERTURA_TOMADOR_CONTADO_PASE("Apertura Tomadora Contado (Tipo Pase)"),
  APERTURA_TOMADOR_FUTURO_CAUCION("Apertura Tomadora Futuro (Tipo Caución)"),
  APERTURA_TOMADOR_FUTURO_PASE("Apertura Tomadora Futuro (Tipo Pase)"),
  BLOCK_COMPRA("Block Compra"),
  BLOCK_VENTA("Block Venta"),
  COMPRA_CONTADO_3368("Compra Contado Circular 3368"),
  COMPRA_CONTADO_7637("Compra Contado Circular 7637"),
  CAUCION_COLOCADOR("Caución Colocadora"),
  CAUCION_COLOCADOR_CONTADO("Caución Colocadora Contado"),
  CAUCION_COLOCADOR_FUTURO("Caución Colocadora Futuro"),
  CAUCION_COLOCADOR_NO_GARANTIZADA("Caución Colocadora No Garantizada"),
  CAUCION_COLOCADOR_NO_GARANTIZADA_CONTADO("Caución Colocadora No Garantizada Contado"),
  CAUCION_COLOCADOR_NO_GARANTIZADA_FUTURO("Caución Colocadora No Garantizada Futuro"),
  CAUCION_TOMADOR("Caución Tomadora"),
  CAUCION_TOMADOR_CONTADO("Caución Tomadora Contado"),
  CAUCION_TOMADOR_FUTURO("Caución Tomadora Futuro"),
  CAUCION_TOMADOR_NO_GARANTIZADA("Caución Tomadora No Garantizada"),
  CAUCION_TOMADOR_NO_GARANTIZADA_CONTADO("Caución Tomadora No Garantizada Contado"),
  CAUCION_TOMADOR_NO_GARANTIZADA_FUTURO("Caución Tomadora No Garantizada Futuro"),
  CIERRE_COLOCADOR_CONTADO("Cierre Colocador Contado"),
  CIERRE_COLOCADOR_FUTURO("Cierre Colocador Futuro"),
  COMPRA_MAE("Compra - MAE"),
  COMPRA_COLOCACION_PRIMARIA("Compra Colocación Primaria"),
  COMPRA_EXTERIOR("Compra Exterior"),
  COMPRA_EXTERIOR_DESCUBIERTO("Compra Exterior en Descubierto"),
  COMPRA_FUTURO("Compra Futuro"),
  COMPRA_CONTADO("Compra Contado"),
  COMPRA_CONTADO_CONTINUO_GARANTIZADO("Compra Contado Continuo Garantizado"),
  COMPRA_CONTADO_CONTINUO_NO_GARANTIZADO("Compra Contado Continuo No Garantizado"),
  VENTA_CONTADO("Venta Contado"),
  VENTA_CONTADO_CONTINUO_GARANTIZADO("Venta Contado Continuo Garantizado"),
  VENTA_CONTADO_CONTINUO_NO_GARANTIZADO("Venta Contado Continuo No Garantizado"),
  CIERRE_POSICION("Cierre Posición"),
  CHEQUES_DIFERIDOS("Cheques Diferidos"),
  MANUAL("Manual"),
  CANCELACION("Cancelación"),
  COMPROBANTE_PAGO("Comprobante de Pago"),
  OTROS_EGRESOS("Otros Egresos"),
  OTROS_INGRESOS("Otros Ingresos"),
  SUSCRIPCION("Suscripción"),
  RESCATE("Rescate"),
  VENTA_EXTERIOR("Venta Exterior"),
  RECIBO_COBRO("Recibo de Cobro"),
  TRANSFER("Transferencia"),
  COMPRA_MEP("Compra MEP"),
  VENTA_MEP("Venta MEP"),
  TRANSFER_MEP("Transfer MEP"),
  RETURNS("Returns"),
  ECHEQ("Echeq"),
  TRANSACTION_OUTBOUND("Transaction outbound"),
  TRANSACTION_INBOUND("Transaction inbound"),
  REDEMPTION("Redemption"),
  PAYMENT_LINK("Link de pago"),
  PAGO_SERVICIO("Pago de servicio"),
  OTHER("OTHER"),
  AMORTIZACION("Amortización"),
  RENTA("Renta"),
  RENTA_Y_AMORTIZACION("Renta y Amortización"),
  CANJE("Canje"),
  DIVIDENDO_EN_ACCIONES("Dividendo en acciones"),
  DIVIDENDO_Y_CANJE("Dividendo y canje"),
  DIVIDENDO_EN_EFECTIVO("Dividendo en efectivo"),
  DIVIDENDO_Y_REVALUO("Dividendo y revalúo"),
  REDUCCION_DE_CAPITAL("Reducción de capital"),
  REVALUO_EN_ACCIONES("Revalúo en acciones"),
  REVALUO_Y_CANJE("Revalúo y canje"),
  SPLIT("Split"),
  DIVIDENDO_REVALUO_Y_CANJE("Dividendo, revalúo y canje"),
  TRANSPASOS_DE_FONDOS("Traspasos de fondos"),
  CARD_MOVEMENT("Card movement"),
  DOLAR_FUTURO_AJUSTE_COBRO("Dólar Futuro Cobro por Ajuste Diario"),
  DOLAR_FUTURO_AJUSTE_PAGO("Dólar Futuro Pago por Ajuste Diario"),
  DOLAR_FUTURO_LIQUIDACION("Dólar Futuro Liquidación");
  private final String displayName;

  MarketOperationType(String displayName) {
    this.displayName = displayName;
  }

  public static MarketOperationType fromString(String displayName) {
    for (MarketOperationType type : MarketOperationType.values()) {
      if (type.displayName.equalsIgnoreCase(displayName)) {
        return type;
      }
    }
    throw new IllegalArgumentException("No enum constant with displayName " + displayName);
  }
}
