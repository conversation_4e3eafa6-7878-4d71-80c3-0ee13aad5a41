package ar.com.daruma.bymas.portfolioMovement.application.find;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindImpactingMovementsForPositions {

  private final PortfolioMovementService portfolioMovementService;

  public List<PortfolioMovement> find(List<PortfolioPosition> portfolioPositions) {
    log.info("Finding impacting movements for {} positions", portfolioPositions.size());
    List<PortfolioMovement> movements =
        portfolioMovementService.findImpactingMovementsForPositions(portfolioPositions);
    log.info("Found {} impacting movements", movements.size());
    return movements;
  }
}
