package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.util.List;

public record PortfolioPositionAndBalances(
    List<PortfolioPosition> positions, List<PortfolioPositionBalance> balances) {}
