package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

public interface MovementToLiquidateView {
  UUID getId();

  UUID getAssetId();

  UUID getAccountId();

  String getMovementType();

  String getOperationType();

  BigDecimal getQuantity();

  BigDecimal getNetAmount();

  Boolean getPositionHasTodayDate();

  Boolean getHasSynchronizedHistory();

  LocalDate getSettlementAt();
}
