package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import static ar.com.daruma.bymas.utils.numbers.StaticNumberUtils.one;

import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import java.time.LocalDate;
import lombok.Getter;

@Getter
public enum MovementProcessState {
  BEYOND_24_HOURS("BEYOND_24_HOURS"),
  WITHIN_24_HOURS("WITHIN_24_HOURS"),
  SETTLED("SETTLED");

  private final String displayName;

  MovementProcessState(String displayName) {
    this.displayName = displayName;
  }

  public static MovementProcessState fromString(String displayName) {
    for (MovementProcessState type : MovementProcessState.values()) {
      if (type.getDisplayName().equalsIgnoreCase(displayName)) {
        return type;
      }
    }
    throw new IllegalArgumentException("No enum constant with displayName " + displayName);
  }

  public static MovementProcessState fromLocalDate(
      LocalDate localDate, WorkingDayService workingDayService) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    LocalDate tomorrowWorkingDate = workingDayService.nextAvailableDay(today, one);
    if (localDate.isAfter(tomorrowWorkingDate)) {
      return BEYOND_24_HOURS;
    } else if (localDate.equals(tomorrowWorkingDate)) {
      return WITHIN_24_HOURS;
    } else {
      return SETTLED;
    }
  }

  public boolean isWithin24Hours() {
    return this == WITHIN_24_HOURS;
  }

  public boolean isBeyond24Hours() {
    return this == BEYOND_24_HOURS;
  }

  public MovementProcessState previousState() {
    return switch (this) {
      case BEYOND_24_HOURS -> BEYOND_24_HOURS; // no hay caso previo a beyond
      case WITHIN_24_HOURS -> BEYOND_24_HOURS;
      case SETTLED -> WITHIN_24_HOURS;
    };
  }

  public MovementProcessState nextState() {
    return switch (this) {
      case BEYOND_24_HOURS -> WITHIN_24_HOURS;
      case WITHIN_24_HOURS -> SETTLED;
      case SETTLED -> SETTLED; // no hay caso siguiente a settled
    };
  }

  public boolean isSettled() {
    return this == SETTLED;
  }
}
