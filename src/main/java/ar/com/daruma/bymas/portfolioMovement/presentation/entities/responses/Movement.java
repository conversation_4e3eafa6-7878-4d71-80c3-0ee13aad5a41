package ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.AssetInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class Movement {

  private String id;
  private String operationId;

  @JsonProperty("account_id")
  private Integer accountNumber;

  private String company;
  private BigDecimal quantity;
  private AssetInfo asset;
  private MovementType movementType;
  private String description;
  private MovementState state;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate settlementAt;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate agreementAt;

  private OperationType operationType;
  private MarketOperationType marketOperationType;
  private BigDecimal netAmount;
  private BigDecimal grossAmount;
  private BigDecimal taxAmount;
  private BigDecimal feeAmount;

  private JsonNode metadata;

  public Movement(
      String id,
      String operationId,
      Integer accountNumber,
      String company,
      BigDecimal quantity,
      AssetInfo asset,
      MovementType movementType,
      String description,
      MovementState state,
      LocalDate settlementAt,
      LocalDate agreementAt,
      OperationType operationType,
      MarketOperationType marketOperationType,
      BigDecimal netAmount,
      BigDecimal grossAmount,
      BigDecimal taxAmount,
      BigDecimal feeAmount,
      JsonNode metadata) {
    this.id = id;
    this.operationId = operationId;
    this.accountNumber = accountNumber;
    this.company = company;
    this.quantity = quantity;
    this.asset = asset;
    this.movementType = movementType;
    this.description = description;
    this.state = state;
    this.settlementAt = settlementAt;
    this.agreementAt = agreementAt;
    this.operationType = operationType;
    this.marketOperationType = marketOperationType;
    this.netAmount = netAmount;
    this.grossAmount = grossAmount;
    this.taxAmount = taxAmount;
    this.feeAmount = feeAmount;
    this.metadata = metadata;
  }
}
