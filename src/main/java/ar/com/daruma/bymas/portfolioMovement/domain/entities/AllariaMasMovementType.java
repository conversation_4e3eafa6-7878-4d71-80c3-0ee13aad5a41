package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import java.util.List;

public interface AllariaMasMovementType {
  static List<MovementType> getValues() {
    return List.of(
        MovementType.CARD,
        MovementType.PAYMENT_LINK,
        MovementType.SERVICE_PAYMENT,
        MovementType.ECHEQ,
        MovementType.VEP,
        MovementType.RETURN,
        MovementType.AUTOMATIC_DEBIT,
        MovementType.DOLLAR_MEP,
        MovementType.FUNDS_TRANSFER,
        MovementType.INVOICE_OPERATION,
        MovementType.PAYMENT_OPERATION,
        MovementType.CARD_INVITE,
        MovementType.TRANSFER,
        MovementType.PROGRAMMED_PAYMENT,
        MovementType.FCI_SUBSCRIPTION,
        MovementType.FCI_REDEMPTION);
  }
}
