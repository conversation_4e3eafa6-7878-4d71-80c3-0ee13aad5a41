package ar.com.daruma.bymas.portfolioMovement.application.find;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FindMovementByOperationId {

  @Autowired private PortfolioMovementService portfolioMovementService;

  private static final CustomLogger logger =
      CustomLogger.getLogger(FindMovementByOperationId.class);

  @Transactional(transactionManager = "byMasTransactionManager")
  public Optional<PortfolioMovement> findMaybeMovement(String operationId) {
    logger.info("Finding portfolio movement entity with operation ID {}", operationId);
    return portfolioMovementService.findMovementByOperationId(operationId);
  }
}
