package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import java.util.List;

public interface AllariaMovementType {
  static List<MovementType> getValues() {
    return List.of(
        MovementType.CHEQ,
        MovementType.ACTIONS,
        MovementType.TITULO_PUBLICO,
        MovementType.TITULOS_PROVINCIALES,
        MovementType.OBLIGACIONES_NEGOCIABLES,
        MovementType.FONDOS,
        MovementType.INDICES,
        MovementType.CEDEARS,
        MovementType.PAGARES,
        MovementType.MERVAL,
        MovementType.CEDROS,
        MovementType.LEBAC,
        MovementType.FCI,
        MovementType.FUTURES,
        MovementType.OPTIONS,
        MovementType.CEDIN,
        MovementType.TRANSFER,
        MovementType.MANUAL_MOVEMENT,
        MovementType.CAUTION);
  }
}
