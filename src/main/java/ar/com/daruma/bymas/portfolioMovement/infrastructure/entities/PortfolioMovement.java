package ar.com.daruma.bymas.portfolioMovement.infrastructure.entities;

import ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization.JsonNodeUserType;
import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses.Movement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.utils.infrastructure.AuditableEntity;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Setter
@Getter
@Entity
@Table(name = "portfolio_movements")
@EqualsAndHashCode(callSuper = false, onlyExplicitlyIncluded = true, doNotUseGetters = true)
public class PortfolioMovement extends AuditableEntity {
  @EqualsAndHashCode.Include @Id private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_account_id", nullable = false)
  private PortfolioAccount portfolioAccount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_asset_id")
  private PortfolioAsset portfolioAsset;

  @Enumerated(EnumType.STRING)
  @Column(name = "movement_type", nullable = false)
  private MovementType movementType;

  @Column(name = "external_id", nullable = false)
  private Long externalId;

  @Column(name = "operation_id", nullable = false)
  private String operationId;

  @Column(name = "description")
  private String description;

  @Column(name = "tags")
  private String tags;

  @Column(nullable = false)
  @Enumerated(EnumType.STRING)
  private MovementState state;

  @Column(name = "movement_process_state", nullable = false)
  @Enumerated(EnumType.STRING)
  private MovementProcessState movementProcessState;

  @Column(name = "settlement_at", nullable = false)
  private LocalDate settlementAt;

  @Column(name = "agreement_at", nullable = false)
  private LocalDate agreementAt;

  @Enumerated(EnumType.STRING)
  @Column(name = "operation_type", nullable = false)
  private OperationType operationType;

  @Enumerated(EnumType.STRING)
  @Column(name = "market_operation_type")
  private MarketOperationType marketOperationType;

  @Column(name = "quantity", nullable = false)
  private BigDecimal quantity;

  @Column(name = "currency", nullable = false)
  private String currency;

  @Column(name = "net_amount", nullable = false)
  private BigDecimal netAmount;

  @Column(name = "gross_amount", nullable = false)
  private BigDecimal grossAmount;

  @Column(name = "tax_amount", nullable = false)
  private BigDecimal taxAmount;

  @Column(name = "fee_amount", nullable = false)
  private BigDecimal feeAmount;

  @Type(JsonNodeUserType.class)
  @Column(name = "metadata", columnDefinition = "jsonb")
  private JsonNode metadata;

  @Enumerated(EnumType.STRING)
  @Column(name = "position_effect", nullable = false)
  private MovementPositionEffect positionEffect;

  @Enumerated(EnumType.STRING)
  @Column(name = "visibility", nullable = false)
  private MovementVisibility visibility;

  public PortfolioMovement() {}

  public PortfolioMovement(
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount,
      PortfolioAsset portfolioAsset,
      JsonNode jsonToSave,
      WorkingDayService workingDayService) {
    this.id = UUID.randomUUID();
    this.portfolioAccount = portfolioAccount;
    this.portfolioAsset = portfolioAsset;
    this.movementType = externalMovement.getMovementType();
    this.state = externalMovement.getMovementState();
    this.movementProcessState = externalMovement.getMovementProcessState(workingDayService);
    this.settlementAt = externalMovement.getSettlementAt();
    this.agreementAt = externalMovement.getAgreementAt();
    this.operationType = externalMovement.getOperationType();
    this.marketOperationType = externalMovement.getMarketOperationType();
    this.description = externalMovement.getDescription();
    this.quantity = externalMovement.getQuantity();
    this.currency = externalMovement.getCurrency();
    this.netAmount = externalMovement.getNetAmount();
    this.grossAmount = externalMovement.getGrossAmount();
    this.taxAmount = externalMovement.getTaxAmount();
    this.feeAmount = externalMovement.getFeeAmount();
    this.operationId = externalMovement.getOperationId();
    this.metadata = jsonToSave;
    this.externalId = externalMovement.getId();
    this.positionEffect = externalMovement.getPositionEffect();
    this.visibility = externalMovement.getMovementVisibility();
  }

  public Movement toDomainMovement() {
    return new Movement(
        id.toString(),
        operationId,
        portfolioAccount.getAccountId(),
        portfolioAccount.getCompany().getName(),
        quantity,
        portfolioAsset.toAssetInfo(),
        movementType,
        description,
        state,
        settlementAt,
        agreementAt,
        operationType,
        marketOperationType,
        netAmount,
        grossAmount,
        taxAmount,
        feeAmount,
        metadata);
  }

  public Boolean isDebit() {
    return operationType == OperationType.DEBIT;
  }

  public boolean settlementHappened() {
    return movementProcessState == MovementProcessState.SETTLED;
  }

  public boolean settlementIsTodayOrLater() {
    return settlementAt.isAfter(BuenosAiresTime.nowAsLocalDate())
        || settlementAt.isEqual(BuenosAiresTime.nowAsLocalDate());
  }

  public boolean isCancelledOrFailed() {
    return state == MovementState.CANCELLED
        || state == MovementState.FAILED
        || state == MovementState.LIQUIDATION_FAILED;
  }

  public boolean isCaution() {
    return movementType == MovementType.CAUTION;
  }

  public boolean isTransfer() {
    return movementType == MovementType.TRANSFER;
  }

  public boolean isNotTransfer() {
    return !isTransfer();
  }

  public boolean hasZeroQuantity() {
    return quantity.equals(BigDecimal.ZERO);
  }

  public BigDecimal getAmountToApply() {
    int sign = 1;
    if (isDebit()) {
      sign = -1;
    }
    BigDecimal amount = hasZeroQuantity() ? getNetAmount() : getQuantity();
    return amount.multiply(BigDecimal.valueOf(sign));
  }

  public BigDecimal getAmountToLock() {
    if (isDebit()) {
      return isCaution() ? getNetAmount() : getQuantity();
    }
    return BigDecimal.ZERO;
  }

  public boolean isWithin24Hours() {
    return movementProcessState.isWithin24Hours();
  }

  public boolean isBeyond24Hours() {
    return movementProcessState.isBeyond24Hours();
  }

  public boolean isLiquidated() {
    return state == MovementState.LIQUIDATED;
  }

  public boolean isSettled() {
    return movementProcessState.isSettled();
  }

  public boolean isCompleted() {
    return state == MovementState.COMPLETED;
  }

  public boolean isRejected() {
    return state == MovementState.REJECTED;
  }

  public boolean isRefunded() {
    return state == MovementState.REFUNDED;
  }

  public boolean isExpired() {
    return state == MovementState.EXPIRED;
  }

  public boolean isApproved() {
    return state == MovementState.APPROVED;
  }

  public boolean hasFinalState() {
    return isCompleted()
        || isLiquidated()
        || isCancelledOrFailed()
        || isRejected()
        || isRefunded()
        || isExpired()
        || isApproved();
  }

  public boolean shouldBeShown() {
    return this.getVisibility().getShouldBeShown();
  }

  public boolean affectsPosition() {
    return getPositionEffect().getAffectsPosition();
  }

  public boolean agreementAtIs(LocalDate now) {
    return this.agreementAt.equals(now);
  }

  public boolean settlementAtIs(LocalDate now) {
    return this.settlementAt.equals(now);
  }

  public BasePortfolioPosition.AssetAndAccountIdPair getAssetAndAccountIdPair() {
    return new BasePortfolioPosition.AssetAndAccountIdPair(
        this.portfolioAsset.getId(), this.portfolioAccount.getId());
  }

  public boolean isAsync() {
    return !this.settlementAt.equals(this.agreementAt);
  }

  public boolean settlementIsBeforeOrEqual(LocalDate localDate) {
    return !settlementAt.isAfter(localDate);
  }
}
