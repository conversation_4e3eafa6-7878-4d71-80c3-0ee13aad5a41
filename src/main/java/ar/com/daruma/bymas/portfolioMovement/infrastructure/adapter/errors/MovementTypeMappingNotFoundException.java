package ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.errors;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.citadel.exceptions.NotFoundException;

public class MovementTypeMappingNotFoundException extends NotFoundException {
  public MovementTypeMappingNotFoundException(MovementType movementType) {
    super("No state mapping configuration found for movement type: %s".formatted(movementType));
  }
}
