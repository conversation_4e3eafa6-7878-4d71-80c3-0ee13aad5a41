package ar.com.daruma.bymas.portfolioMovement.application.liquidation;

import static ar.com.daruma.bymas.utils.numbers.StaticNumberUtils.one;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.LiquidationResult;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.*;
import ar.com.daruma.bymas.portfolioPosition.application.find.FindPortfolioPositionsByAssetAndAccountIdPairs;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionsByHistoryIfOutdated;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePositions;
import ar.com.daruma.bymas.portfolioPosition.domain.errors.CannotLiquidatePortfolioMovementException;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionBalances.domain.PortfolioPositionBalanceService;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class MovementLiquidationBatchService extends DiscordSender {

  private final UpdatePositions updatePositions;
  private final PortfolioMovementService portfolioMovementService;

  private final FindPortfolioPositionsByAssetAndAccountIdPairs
      findPortfolioPositionsByAssetAndAccountIdPairs;
  private final UpdatePortfolioPositionsByHistoryIfOutdated
      updatePortfolioPositionsByHistoryIfOutdated;
  private final WorkingDayService workingDayService;
  private final ObjectMapper objectMapper;
  private final PortfolioPositionBalanceService portfolioPositionBalanceService;

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<LiquidationResult> processBatch(
      List<MovementToLiquidateOrChangeProcessState> batch, Optional<LocalDate> date) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    List<MovementToLiquidateOrChangeProcessState> movementsToLiquidate =
        batch.stream().filter(m -> m.getSettlementAt().isEqual(date.orElse(today))).toList();
    List<LiquidationResult> validationResults =
        movementsToLiquidate.stream().map(this::validateMovement).toList();
    List<MovementToLiquidateOrChangeProcessState> validatedMovementsToLiquidate =
        validationResults.stream()
            .filter(LiquidationResult::isSuccess)
            .map(LiquidationResult::getMovement)
            .toList();
    List<PortfolioMovement> portfolioMovementsToLiquidate =
        getMovementsToLiquidate(validatedMovementsToLiquidate);
    List<PortfolioMovement> portfolioMovementsToChangeTo24Hours =
        getMovementsToChangeTo24Hours(batch, date.orElse(today));
    PortfolioPositionAndBalances allPositionsAndBalances =
        getAllPositionsAndBalances(
            portfolioMovementsToLiquidate, portfolioMovementsToChangeTo24Hours);
    changeTo24Hours(portfolioMovementsToChangeTo24Hours, allPositionsAndBalances.positions());
    liquidate(portfolioMovementsToLiquidate, allPositionsAndBalances.positions());
    updatePositions.update(allPositionsAndBalances.positions());
    portfolioPositionBalanceService.saveAll(allPositionsAndBalances.balances());
    updateMovementsStateForFailedLiquidations(
        validationResults.stream().filter(LiquidationResult::isFailed).toList());
    return validationResults;
  }

  private List<PortfolioMovement> getMovementsToLiquidate(
      List<MovementToLiquidateOrChangeProcessState> validatedMovementsToLiquidate) {
    return portfolioMovementService.findByIdIn(
        validatedMovementsToLiquidate.stream()
            .map(MovementToLiquidateOrChangeProcessState::getId)
            .collect(Collectors.toList()));
  }

  private List<PortfolioMovement> getMovementsToChangeTo24Hours(
      List<MovementToLiquidateOrChangeProcessState> batch, LocalDate today) {
    LocalDate nextWorkingDay =
        workingDayService.nextAvailableDay(
            today,
            one); // TODO: cambiar flujo a buscar movements a procesar a 24 horas en general para
    // TODO FACU : within 24 horas
    List<MovementToLiquidateOrChangeProcessState> movementsToChangeTo24Hours =
        batch.stream().filter(m -> m.getSettlementAt().isEqual(nextWorkingDay)).toList();
    return portfolioMovementService.findByIdIn(
        movementsToChangeTo24Hours.stream()
            .map(MovementToLiquidateOrChangeProcessState::getId)
            .collect(Collectors.toList()));
  }

  private PortfolioPositionAndBalances getAllPositionsAndBalances(
      List<PortfolioMovement> portfolioMovementsToLiquidate,
      List<PortfolioMovement> portfolioMovementsToChangeTo24Hours) {
    Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIdPairs =
        portfolioMovementsToLiquidate.stream()
            .map(PortfolioMovement::getAssetAndAccountIdPair)
            .collect(Collectors.toSet());
    assetAndAccountIdPairs.addAll(
        portfolioMovementsToChangeTo24Hours.stream()
            .map(PortfolioMovement::getAssetAndAccountIdPair)
            .collect(Collectors.toSet()));
    List<PortfolioPosition> positions =
        findPortfolioPositionsByAssetAndAccountIdPairs.find(assetAndAccountIdPairs);
    List<PortfolioPositionBalance> portfolioPositionBalance =
        updatePortfolioPositionsByHistoryIfOutdated.updateWithoutSaving(
            positions, BuenosAiresTime.nowAsLocalDate());
    return new PortfolioPositionAndBalances(positions, portfolioPositionBalance);
  }

  private void updateMovementsStateForFailedLiquidations(List<LiquidationResult> failedResults) {
    List<UUID> failedIds = failedResults.stream().map(r -> r.getMovement().getId()).toList();
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    if (!failedIds.isEmpty()) {
      portfolioMovementService.updateMovementsState(
          failedIds, MovementState.LIQUIDATION_FAILED, now);
    }
  }

  private void changeTo24Hours(
      List<PortfolioMovement> portfolioMovements, List<PortfolioPosition> positions) {
    Map<BasePortfolioPosition.AssetAndAccountIdPair, List<PortfolioMovement>>
        movementsByAssetAndAccount =
            portfolioMovements.stream()
                .collect(Collectors.groupingBy(PortfolioMovement::getAssetAndAccountIdPair));
    List<PortfolioPositionBalance> balancesToSave = new ArrayList<>();
    positions.forEach(
        position -> {
          List<PortfolioMovement> movementsForPosition =
              movementsByAssetAndAccount.getOrDefault(
                  position.getAssetAndAccountIdPair(), List.of());
          movementsForPosition.forEach(
              movement -> {
                PortfolioPositionBalance cancelBalance =
                    position.cancelMovement(objectMapper, movement);
                balancesToSave.add(cancelBalance);
              });
          setMovementsProcessStateToWithin24Hours(movementsForPosition);
          movementsForPosition.forEach(
              movement -> {
                PortfolioPositionBalance applyBalance =
                    position.applyMovement(objectMapper, movement);
                balancesToSave.add(applyBalance);
              });
        });
    portfolioPositionBalanceService.saveAll(balancesToSave);
    portfolioMovementService.saveAll(portfolioMovements);
  }

  private void liquidate(
      List<PortfolioMovement> portfolioMovements, List<PortfolioPosition> positions) {
    Map<BasePortfolioPosition.AssetAndAccountIdPair, List<PortfolioMovement>>
        movementsByAssetAndAccount =
            portfolioMovements.stream()
                .collect(Collectors.groupingBy(PortfolioMovement::getAssetAndAccountIdPair));
    List<PortfolioPositionBalance> balancesToSave = new ArrayList<>();

    positions.forEach(
        position -> {
          List<PortfolioMovement> movementsForPosition =
              movementsByAssetAndAccount.getOrDefault(
                  position.getAssetAndAccountIdPair(), List.of());
          movementsForPosition.forEach(
              movement -> {
                PortfolioPositionBalance cancelBalance =
                    position.cancelMovement(objectMapper, movement);
                balancesToSave.add(cancelBalance);
              });
          liquidateMovements(movementsForPosition);
          movementsForPosition.forEach(
              movement -> {
                PortfolioPositionBalance applyBalance =
                    position.applyMovement(objectMapper, movement);
                balancesToSave.add(applyBalance);
              });
        });
    portfolioPositionBalanceService.saveAll(balancesToSave);
    portfolioMovementService.saveAll(portfolioMovements);
  }

  private void setMovementsProcessStateToWithin24Hours(List<PortfolioMovement> movements) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    movements.forEach(movement -> setProcessStateToWithin24Hours(movement, now));
  }

  private void setProcessStateToWithin24Hours(PortfolioMovement movement, LocalDateTime updatedAt) {
    movement.setMovementProcessState(MovementProcessState.WITHIN_24_HOURS);
    movement.setState(MovementState.PENDING_LIQUIDATION);
    movement.setUpdatedAt(updatedAt);
  }

  private void liquidateMovements(List<PortfolioMovement> movements) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    movements.forEach(movement -> liquidateMovement(movement, now));
  }

  private void liquidateMovement(PortfolioMovement movement, LocalDateTime updatedAt) {
    movement.setState(MovementState.LIQUIDATED);
    movement.setMovementProcessState(MovementProcessState.SETTLED);
    movement.setUpdatedAt(updatedAt);
  }

  private LiquidationResult validateMovement(MovementToLiquidateOrChangeProcessState movement) {
    try {
      validateQuantity(movement);
      validateHistorySync(movement);
      return new LiquidationResult(movement, true, null);
    } catch (Exception ex) {
      return new LiquidationResult(movement, false, ex.getMessage());
    }
  }

  private void validateHistorySync(MovementToLiquidateOrChangeProcessState movement) {
    Boolean hasSynchronizedHistory = movement.getHasSynchronizedHistory();
    if (!hasSynchronizedHistory) {
      throw new CannotLiquidatePortfolioMovementException(
          String.format(
              "No history position sync found for date: [%s], assetId: [%s], accountId: [%s]",
              BuenosAiresTime.nowAsLocalDate(), movement.getAssetId(), movement.getAccountId()));
    }
  }

  private void validateQuantity(MovementToLiquidateOrChangeProcessState movement) {
    if (movement.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
      throw new CannotLiquidatePortfolioMovementException(
          String.format("Movement: %s quantity cannot be zero or negative", movement.getId()));
    }
  }
}
