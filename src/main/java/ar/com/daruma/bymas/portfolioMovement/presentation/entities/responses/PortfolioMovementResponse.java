package ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PortfolioMovementResponse {

  @JsonProperty("total_elements")
  private Long totalElements;

  @JsonProperty("total_pages")
  private Integer totalPages;

  private Integer page;

  private List<Movement> movements;
}
