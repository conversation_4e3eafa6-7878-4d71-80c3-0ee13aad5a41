package ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.DEBIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState.PENDING_LIQUIDATION;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.AllariaMovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.FindMovementsInput;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.*;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.repository.PortfolioMovementRepository;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioMovementServiceImpl implements PortfolioMovementService {

  private final PortfolioMovementRepository repository;
  private final WorkingDayService workingDayService;

  @Override
  public PortfolioMovement save(PortfolioMovement movement) {
    return repository.save(movement);
  }

  @Override
  public PortfolioMovement update(
      PortfolioMovement portfolioMovement,
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount) {
    portfolioMovement.setPortfolioAccount(portfolioAccount);
    portfolioMovement.setState(externalMovement.getMovementState());
    portfolioMovement.setAgreementAt(externalMovement.getAgreementAt());
    portfolioMovement.setSettlementAt(externalMovement.getSettlementAt());
    portfolioMovement.setFeeAmount(externalMovement.getFeeAmount());
    portfolioMovement.setTaxAmount(externalMovement.getTaxAmount());
    portfolioMovement.setGrossAmount(externalMovement.getGrossAmount());
    portfolioMovement.setNetAmount(externalMovement.getNetAmount());
    portfolioMovement.setQuantity(externalMovement.getQuantity());
    portfolioMovement.setDescription(externalMovement.getDescription());
    portfolioMovement.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());

    return save(portfolioMovement);
  }

  @Override
  public PortfolioMovement createFromExternalMovement(
      ExternalMovement externalMovement,
      PortfolioAccount portfolioAccount,
      PortfolioAsset portfolioAsset) {
    JsonNode metadataAsJson = externalMovement.getDataAsJson();
    PortfolioMovement portfolioMovement =
        new PortfolioMovement(
            externalMovement, portfolioAccount, portfolioAsset, metadataAsJson, workingDayService);

    return save(portfolioMovement);
  }

  @Override
  public PortfolioMovement cancelOperation(PortfolioMovement portfolioMovement) {
    portfolioMovement.setState(MovementState.CANCELLED);
    portfolioMovement.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    return repository.save(portfolioMovement);
  }

  @Override
  public Optional<PortfolioMovement> findMovementByOperationId(String operationId) {
    return repository.findByOperationId(operationId);
  }

  @Override
  public Page<PortfolioMovement> findShowableByFiltersAndAccountIds(
      FindMovementsInput input, List<UUID> portfolioAccountIds, PageRequest pageRequest) {
    return repository.findShowableByFiltersAndMultipleAccounts(
        portfolioAccountIds,
        input.getFromDate().orElse(null),
        input.getToDate().orElse(null),
        input.getOperation().map(MarketOperationType::name).orElse(null),
        input.getId().map(UUID::toString).orElse(null),
        input.getState().map(MovementState::name).orElse(null),
        input.getCurrency().orElse(null),
        input.getMovementId(),
        pageRequest);
  }

  @Override
  public Page<PortfolioMovement> findShowableByFiltersAndAssetAndAccountIds(
      FindMovementsInput input,
      UUID assetId,
      List<UUID> portfolioAccountIds,
      PageRequest pageRequest) {
    return repository.findShowableByFiltersAndAssetAndMultipleAccounts(
        portfolioAccountIds,
        assetId,
        input.getFromDate().orElse(null),
        input.getToDate().orElse(null),
        input.getOperation().map(MarketOperationType::name).orElse(null),
        input.getId().map(UUID::toString).orElse(null),
        input.getState().map(MovementState::name).orElse(null),
        input.getCurrency().orElse(null),
        input.getMovementId(),
        pageRequest);
  }

  @Override
  public List<PortfolioMovement> findAllByExternalId(Long externalId) {
    return repository.findAllByExternalId(externalId);
  }

  @Override
  public void updateMovementState(PortfolioMovement movement, MovementState state) {
    movement.setState(state);
    movement.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    repository.save(movement);
  }

  @Override
  public List<PortfolioMovement> findTodayMovementsForAccountAndMaybeAsset(
      PortfolioAccount account, Optional<PortfolioAsset> maybeAsset) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    return maybeAsset
        .map(
            asset -> findByPortfolioAccountAndAssetIdAndDate(account.getId(), asset.getId(), today))
        .orElseGet(() -> findByPortfolioAccountAndDate(account.getId(), today));
  }

  private List<PortfolioMovement> findByPortfolioAccountAndAssetIdAndDate(
      UUID accountId, UUID assetId, LocalDate date) {
    LocalDate tomorrow = date.plusDays(1);
    List<PortfolioMovement> movements = new ArrayList<>();
    movements.addAll(
        repository.findByPortfolioAccountIdAndPortfolioAssetIdAndSettlementAt(
            accountId, assetId, date));
    movements.addAll(
        repository.findByPortfolioAccountIdAndPortfolioAssetIdAndSettlementAt(
            accountId, assetId, tomorrow));
    movements.addAll(
        repository.findByPortfolioAccountIdAndPortfolioAssetIdAndAgreementAt(
            accountId, assetId, date));

    return movements.stream().distinct().toList();
  }

  private List<PortfolioMovement> findByPortfolioAccountsAndAssetIdAndDate(
      List<UUID> accountIds, UUID assetId, LocalDate date) {
    LocalDate tomorrow = date.plusDays(1);
    List<PortfolioMovement> movements = new ArrayList<>();
    movements.addAll(
        repository.findByPortfolioAccountIdInAndPortfolioAssetIdAndSettlementAt(
            accountIds, assetId, date));
    movements.addAll(
        repository.findByPortfolioAccountIdInAndPortfolioAssetIdAndSettlementAt(
            accountIds, assetId, tomorrow));
    movements.addAll(
        repository.findByPortfolioAccountIdInAndPortfolioAssetIdAndAgreementAt(
            accountIds, assetId, date));
    return movements;
  }

  private List<PortfolioMovement> findByPortfolioAccountAndDate(UUID accountId, LocalDate date) {
    LocalDate tomorrow = date.plusDays(1);
    List<PortfolioMovement> movements = new ArrayList<>();
    movements.addAll(repository.findByPortfolioAccountIdAndSettlementAt(accountId, date));
    movements.addAll(repository.findByPortfolioAccountIdAndSettlementAt(accountId, tomorrow));
    movements.addAll(repository.findByPortfolioAccountIdAndAgreementAt(accountId, date));

    return movements.stream().distinct().toList();
  }

  private List<PortfolioMovement> findByPortfolioAccountsAndDate(
      List<UUID> accountIds, LocalDate date) {
    LocalDate tomorrow = date.plusDays(1);
    List<PortfolioMovement> movements = new ArrayList<>();
    movements.addAll(repository.findByPortfolioAccountIdInAndSettlementAt(accountIds, date));
    movements.addAll(repository.findByPortfolioAccountIdInAndSettlementAt(accountIds, tomorrow));
    movements.addAll(repository.findByPortfolioAccountIdInAndAgreementAt(accountIds, date));
    return movements;
  }

  @Override
  public void updateMovementsState(
      List<UUID> movementsIds, MovementState movementState, LocalDateTime now) {
    repository.updateMovementsState(movementsIds, movementState, now);
  }

  @Override
  public void updateMovementsProcessState(
      List<UUID> movementsIds, MovementProcessState movementProcessState) {
    repository.updateMovementsProcessState(movementsIds, movementProcessState);
  }

  @Override
  public List<PortfolioMovement> findMovementsThatImpactTodayForAccountsAndMaybeAsset(
      List<PortfolioAccount> accounts, Optional<PortfolioAsset> maybeAsset) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    return maybeAsset
        .map(
            asset ->
                findByPortfolioAccountsAndAssetIdAndDate(
                    accounts.stream().map(PortfolioAccount::getId).toList(), asset.getId(), today))
        .orElseGet(
            () ->
                findByPortfolioAccountsAndDate(
                    accounts.stream().map(PortfolioAccount::getId).toList(), today));
  }

  @Override
  public List<PortfolioMovement> findByIdIn(List<UUID> ids) {
    return repository.findByIdIn(ids);
  }

  @Override
  public void saveAll(List<PortfolioMovement> portfolioMovements) {
    repository.saveAll(portfolioMovements);
  }

  @Override
  public List<PortfolioMovement> findWithin24HourMovementsForMaybeAccountAndAssets(
      Optional<PortfolioAccount> account, List<UUID> assetIds) {
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    LocalDate tomorrow = now.plusDays(1);
    if (account.isPresent()) {
      return repository.findWithin24HourMovementsByAccountIdAndAssetsIdsIn(
          account.get().getId(), assetIds, now, tomorrow);
    } else {
      return repository.findWithin24HourMovementsForAssetIdsIn(assetIds, now, tomorrow);
    }
  }

  @Override
  public List<PortfolioMovement> findFutureDebitMovementsForMaybeAccount(
      Optional<PortfolioAccount> maybeAccount) {
    LocalDate today = BuenosAiresTime.nowAsLocalDate();
    return maybeAccount
        .map(
            account ->
                repository.findByPortfolioAccountAndOperationTypeAndSettlementAtIsAfter(
                    account.getId(), DEBIT.toString(), today))
        .orElseGet(() -> repository.findByStateAndSettlementAtIsAfter(DEBIT.toString(), today));
  }

  @Override
  public List<PortfolioMovement> findImpactingMovementsForPositions(
      List<PortfolioPosition> portfolioPositions) {
    Set<BasePortfolioPosition.AssetAndAccountIdPair> assetAndAccountIdPairs =
        portfolioPositions.stream()
            .map(PortfolioPosition::getAssetAndAccountIdPair)
            .collect(Collectors.toSet());
    Set<UUID> assetIds =
        assetAndAccountIdPairs.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAssetId)
            .collect(Collectors.toSet());
    Set<UUID> accountIds =
        assetAndAccountIdPairs.stream()
            .map(BasePortfolioPosition.AssetAndAccountIdPair::portfolioAccountId)
            .collect(Collectors.toSet());
    List<PortfolioMovement> movements =
        repository.findByPortfolioAccountIdInAndPortfolioAssetIdInAndSettlementAtIsGreaterThanEqual(
            accountIds, assetIds, BuenosAiresTime.nowAsLocalDate());
    movements =
        movements.stream()
            .filter(
                movement -> assetAndAccountIdPairs.contains(movement.getAssetAndAccountIdPair()))
            .toList();
    return movements;
  }

  @Override
  public void changeLiquidationFailedToPendingForMaybeMovementIdMaybeAccountIdAndMaybeDate(
      Optional<UUID> maybeMovementId, Optional<UUID> maybeAccountId, Optional<LocalDate> date) {
    LocalDateTime now = BuenosAiresTime.nowAsLocalDateTime();
    LocalDate nowDate = now.toLocalDate();
    if (maybeMovementId.isPresent()) {
      PortfolioMovement movement = repository.findById(maybeMovementId.get()).orElseThrow();
      movement.setState(PENDING_LIQUIDATION);
      movement.setUpdatedAt(now);
      repository.save(movement);
      return;
    }
    maybeAccountId.ifPresentOrElse(
        uuid ->
            repository.updateMovementsStateForAccountIdAndDate(
                uuid, date.orElse(nowDate), PENDING_LIQUIDATION.toString(), now),
        () ->
            repository.updateMovementsStateForDate(
                date.orElse(nowDate), PENDING_LIQUIDATION.toString(), now));
  }

  @Override
  public List<PortfolioMovement> findMovementsThatAreSettledForDateAndPositions(
      List<PortfolioPosition> portfolioPositions, LocalDate date) {
    return findImpactingMovementsForPositions(portfolioPositions).stream()
        .filter(movement -> !movement.isSettled() && movement.settlementIsBeforeOrEqual(date))
        .toList();
  }

  @Override
  public List<MovementToLiquidateOrChangeProcessState> findMovementsToLiquidateOrChangeProcessState(
      Optional<UUID> maybeMovementId,
      Optional<LocalDate> date,
      int limit,
      MovementState state,
      Optional<UUID> maybeAccountId) {
    LocalDate todayDate = BuenosAiresTime.nowAsLocalDate();
    LocalDate settlementDate = date.orElse(todayDate);
    LocalDate tomorrowWorkingDate = workingDayService.nextAvailableDay(settlementDate, 1);
    LocalDate lastWorkingDay = workingDayService.lastAvailableDay(settlementDate, 1);
    List<MovementType> allariaMovementTypes = AllariaMovementType.getValues();
    List<MovementToLiquidateView> projections =
        maybeMovementId
            .map(
                movementId ->
                    repository.findMovementToLiquidateOrChangeProcessStateById(
                        movementId,
                        settlementDate,
                        tomorrowWorkingDate,
                        lastWorkingDay,
                        limit,
                        state.toString()))
            .orElseGet(
                () ->
                    maybeAccountId.isPresent()
                        ? repository.findMovementsToLiquidateOrChangeProcessStateByAccount(
                            settlementDate,
                            tomorrowWorkingDate,
                            lastWorkingDay,
                            limit,
                            state.toString(),
                            maybeAccountId.get())
                        : repository.findMovementsToLiquidateOrChangeProcessState(
                            settlementDate,
                            tomorrowWorkingDate,
                            lastWorkingDay,
                            limit,
                            state.toString()));

    projections =
        projections.stream()
            .filter(p -> allariaMovementTypes.contains(MovementType.valueOf(p.getMovementType())))
            .toList();

    return projections.stream()
        .map(
            p ->
                new MovementToLiquidateOrChangeProcessState(
                    p.getId(),
                    p.getAssetId(),
                    p.getAccountId(),
                    MovementType.valueOf(p.getMovementType()),
                    OperationType.valueOf(p.getOperationType()),
                    !p.getQuantity().equals(BigDecimal.ZERO) ? p.getQuantity() : p.getNetAmount(),
                    p.getPositionHasTodayDate(),
                    p.getHasSynchronizedHistory(),
                    p.getSettlementAt()))
        .toList();
  }
}
