package ar.com.daruma.bymas.portfolioMovement.application.validations;

import ar.com.daruma.bymas.portfolioMovement.application.find.FindMovementByOperationId;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.errors.PortfolioMovementNotFoundException;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ValidatePortfolioMovementExistsForOperationId {

  @Autowired private FindMovementByOperationId findMovementByOperationId;

  private static final CustomLogger logger =
      CustomLogger.getLogger(ValidatePortfolioMovementExistsForOperationId.class);

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioMovement validate(String operationId) {
    logger.info("Validating portfolio movement exists for operation ID: {}", operationId);

    PortfolioMovement portfolioMovement =
        findMovementByOperationId
            .findMaybeMovement(operationId)
            .orElseThrow(
                () -> {
                  logger.error("No portfolio movement found for operation ID: {}", operationId);
                  return new PortfolioMovementNotFoundException(operationId);
                });
    logger.info(
        "Portfolio movement found for operation ID: {} with id: {}",
        operationId,
        portfolioMovement.getId());

    return portfolioMovement;
  }
}
