package ar.com.daruma.bymas.portfolioMovement.domain.entities;

import ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FindMovementsInput {
  @Positive(message = "Account id must be positive")
  private Integer accountId;

  private PortfolioCompany accountCompany;

  private Integer movementId;

  private Optional<String> assetName;

  @NotNull private Optional<LocalDate> fromDate;

  @NotNull private Optional<LocalDate> toDate;

  private Optional<MarketOperationType> operation;

  private Optional<UUID> id;

  private Optional<MovementState> state;

  private Optional<String> currency;

  private Optional<PortfolioCompany> company;

  // omito por el momento
  private Optional<String> group;

  private Integer page;

  private Integer count;

  public FindMovementsInput(
      Integer accountId,
      String accountCompany,
      Integer movementId,
      Optional<String> assetName,
      Optional<LocalDate> fromDate,
      Optional<LocalDate> toDate,
      Optional<String> maybeOperation,
      Optional<UUID> id,
      Optional<String> maybeState,
      Optional<String> currency,
      Optional<String> maybeCompany,
      Optional<String> group,
      Integer page,
      Integer count) {
    this.accountId = accountId;
    this.accountCompany = ByMasCompanySecrets.fromString(accountCompany);
    this.movementId = movementId;
    this.assetName = assetName;
    this.fromDate = fromDate;
    this.toDate = toDate;
    this.operation = maybeOperation.map(MarketOperationType::valueOf);
    this.id = id;
    this.state = maybeState.map(MovementState::fromString);
    this.currency = currency;
    this.company = maybeCompany.map(ByMasCompanySecrets::fromString);
    this.group = group;
    this.page = page;
    this.count = count;
  }

  public Boolean isOnlyAllariaCompany() {
    return company.map(ByMasCompanySecrets.allariaMasCompany::equals).orElse(false);
  }

  public Boolean isOnlyAllariaMasCompany() {
    return company.map(ByMasCompanySecrets.allariaCompany::equals).orElse(false);
  }

  public Boolean bothCompanies() {
    return !isOnlyAllariaCompany() || !isOnlyAllariaMasCompany();
  }

  public String logMessage(Optional<PortfolioAsset> maybeAsset) {
    StringBuilder builder = new StringBuilder("Finding movements ");

    maybeAsset.ifPresent(asset -> builder.append(String.format("for asset %s ", asset.getName())));
    builder.append("within dates [%s,%s] for account id %d".formatted(fromDate, toDate, accountId));
    operation.ifPresent(op -> builder.append(String.format(", operation: %s", op)));
    id.ifPresent(value -> builder.append(String.format(", id: %s", value)));
    state.ifPresent(value -> builder.append(String.format(", state: %s", value)));
    currency.ifPresent(value -> builder.append(String.format(", currency: %s", value)));
    company.ifPresent(value -> builder.append(String.format(", company: %s", value)));
    group.ifPresent(value -> builder.append(String.format(", group: %s", value)));
    assetName.ifPresent(value -> builder.append(String.format(", asset: %s", value)));

    builder.append(String.format(" in page %d with count %d", page, count));

    return builder.toString();
  }
}
