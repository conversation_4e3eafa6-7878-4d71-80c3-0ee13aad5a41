package ar.com.daruma.bymas.portfolioMovement.application.find;

import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindMovementsThatAreSettledForDateAndPositions {

  private final PortfolioMovementService portfolioMovementService;

  public List<PortfolioMovement> find(List<PortfolioPosition> portfolioPositions, LocalDate date) {
    log.info(
        "Finding movements that are settled for date: {} and positions: {}",
        date,
        portfolioPositions.size());
    List<PortfolioMovement> movements =
        portfolioMovementService.findMovementsThatAreSettledForDateAndPositions(
            portfolioPositions, date);
    log.info(
        "Found {} movements that are settled for date: {} and positions: {}",
        movements.size(),
        date,
        portfolioPositions.size());
    return movements;
  }
}
