package ar.com.daruma.bymas.portfolioMovement.presentation.controller;

import ar.com.daruma.bymas.portfolioMovement.application.find.FindMovementsByFilters;
import ar.com.daruma.bymas.portfolioMovement.application.liquidation.PositionSettlementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.FindMovementsInput;
import ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses.Movement;
import ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses.PortfolioMovementResponse;
import ar.com.daruma.bymas.security.application.BymasAuthorizationUtils;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/movements")
@Validated
@RequiredArgsConstructor
public class PortfolioMovementController {

  private final FindMovementsByFilters findMovementsByFilters;
  private final PositionSettlementService positionSettlementService;
  private final BymasAuthorizationUtils bymasAuthorizationUtils;

  @BymasAuthorization(role = "WATCHER")
  @GetMapping("")
  public @ResponseBody ResponseEntity<PortfolioMovementResponse> getMovements(
      @RequestHeader(name = "X-Client-Target", required = false) String clientTarget,
      @RequestParam(name = "account-id") @Valid @Positive(message = "Account id must be positive")
          Integer accountId,
      @RequestParam(name = "movement-id", required = false) Integer movementId,
      @RequestParam(name = "asset", required = false) String asset,
      @RequestParam(name = "from-date", required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
          LocalDate fromDate,
      @RequestParam(name = "to-date", required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
          LocalDate toDate,
      @RequestParam(name = "operation", required = false) String operation,
      @RequestParam(name = "id", required = false) UUID id,
      @RequestParam(name = "state", required = false) String state,
      @RequestParam(name = "currency", required = false) String currency,
      @RequestParam(name = "company", required = false) String company,
      @RequestParam(name = "group", required = false) String group,
      @RequestParam(name = "page", defaultValue = "1")
          @Min(value = 1, message = "page must be at least 1")
          int page,
      @RequestParam(name = "count", defaultValue = "10")
          @Min(value = 1, message = "Count must be at least 1")
          @Max(value = 100, message = "Count cannot exceed 100")
          int count) {
    String accountCompanyFromContext = bymasAuthorizationUtils.getAccountCompanyNameFromContext();
    if (bymasAuthorizationUtils.isAllariaMasAdmin()) {
      accountCompanyFromContext = clientTarget;
    }
    FindMovementsInput input =
        new FindMovementsInput(
            accountId,
            accountCompanyFromContext,
            movementId,
            Optional.ofNullable(asset),
            Optional.ofNullable(fromDate),
            Optional.ofNullable(toDate),
            Optional.ofNullable(operation),
            Optional.ofNullable(id),
            Optional.ofNullable(state),
            Optional.ofNullable(currency),
            Optional.ofNullable(company),
            Optional.ofNullable(group),
            page - 1,
            count);

    Page<Movement> movements = findMovementsByFilters.find(input);

    PortfolioMovementResponse response =
        new PortfolioMovementResponse(
            movements.getTotalElements(), movements.getTotalPages(), page, movements.getContent());
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @BymasAuthorization
  @PostMapping("liquidate")
  public ResponseEntity<Void> liquidatePendingMovements(
      @RequestParam(name = "movement-id", required = false) Optional<UUID> movementId,
      @RequestParam(name = "date", required = false) Optional<LocalDate> date,
      @RequestParam(name = "shouldProcessLiquidationFailed", required = false)
          Optional<Boolean> shouldProcessLiquidationFailed,
      @RequestParam(name = "account-id", required = false) Optional<UUID> maybeAccountId) {
    positionSettlementService.execute(
        movementId, date, shouldProcessLiquidationFailed.orElse(false), maybeAccountId);
    return new ResponseEntity<>(HttpStatus.OK);
  }
}
