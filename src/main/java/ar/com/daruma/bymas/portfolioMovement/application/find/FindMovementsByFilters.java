package ar.com.daruma.bymas.portfolioMovement.application.find;

import ar.com.daruma.bymas.portfolioAccount.application.find.FindRelatedAccounts;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.FindMovementsInput;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioMovement.presentation.entities.responses.Movement;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindMovementsByFilters {

  private final FindPortfolioAssetByName findPortfolioAssetByName;
  private final PortfolioMovementService portfolioMovementService;

  private final FindRelatedAccounts findRelatedAccounts;

  @Transactional(transactionManager = "byMasTransactionManager")
  public Page<Movement> find(FindMovementsInput input) {
    Page<PortfolioMovement> movements = findPortfolioMovements(input);
    return movements.map(PortfolioMovement::toDomainMovement);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public Page<PortfolioMovement> findPortfolioMovements(FindMovementsInput input) {
    log.info(
        "Finding movements within dates [{},{}] for account id {} in page {} with count {} and movement-id {}",
        input.getFromDate(),
        input.getToDate(),
        input.getAccountId(),
        input.getPage(),
        input.getCount(),
        input.getMovementId());
    List<PortfolioAccount> allAccounts =
        findRelatedAccounts.findAndReturnWithOwnAccount(
            input.getAccountId(), input.getAccountCompany());

    Optional<PortfolioAsset> asset = input.getAssetName().map(findPortfolioAssetByName::find);

    Sort.Direction direction = getSortDirection(input.getFromDate());

    PageRequest pageRequest =
        PageRequest.of(input.getPage(), input.getCount(), Sort.by(direction, "created_at"));

    if (input.isOnlyAllariaMasCompany()) {
      List<PortfolioAccount> allariaMasAccounts =
          allAccounts.stream().filter(PortfolioAccount::isAllariaMasAccount).toList();
      log.info("Finding movements for {} Allaria Mas company accounts", allariaMasAccounts.size());
      return getPortfolioMovementsFromAccounts(input, asset, pageRequest, allariaMasAccounts);
    }
    if (input.isOnlyAllariaCompany()) {
      List<PortfolioAccount> allariaAccounts =
          allAccounts.stream().filter(PortfolioAccount::isAllariaAccount).toList();
      log.info("Finding movements for {} Allaria company accounts", allariaAccounts.size());
      return getPortfolioMovementsFromAccounts(input, asset, pageRequest, allariaAccounts);
    }
    log.info("Finding movements for {} accounts from both companies", allAccounts.size());
    return getPortfolioMovementsFromAccounts(input, asset, pageRequest, allAccounts);
  }

  private Page<PortfolioMovement> getPortfolioMovementsFromAccounts(
      FindMovementsInput input,
      Optional<PortfolioAsset> asset,
      PageRequest pageRequest,
      List<PortfolioAccount> portfolioAccounts) {
    List<UUID> accountIds = portfolioAccounts.stream().map(PortfolioAccount::getId).toList();
    return asset
        .map(
            portfolioAsset ->
                portfolioMovementService.findShowableByFiltersAndAssetAndAccountIds(
                    input, portfolioAsset.getId(), accountIds, pageRequest))
        .orElseGet(
            () ->
                portfolioMovementService.findShowableByFiltersAndAccountIds(
                    input, accountIds, pageRequest));
  }

  private Sort.Direction getSortDirection(Optional<LocalDate> fromDate) {
    if (fromDate.isEmpty()) return Sort.Direction.DESC;
    else return Sort.Direction.ASC;
  }
}
