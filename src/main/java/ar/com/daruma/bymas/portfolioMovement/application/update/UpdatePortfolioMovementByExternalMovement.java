package ar.com.daruma.bymas.portfolioMovement.application.update;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioMovement.domain.PortfolioMovementService;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.errors.CannotUpdatePortfolioMovementException;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UpdatePortfolioMovementByExternalMovement extends DiscordSender {

  private static final CustomLogger logger =
      CustomLogger.getLogger(UpdatePortfolioMovementByExternalMovement.class);

  @Autowired private PortfolioMovementService portfolioMovementService;

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioMovement execute(
      PortfolioMovement portfolioMovement,
      ExternalMovement externalMovement,
      Boolean validateLiquidationDate,
      PortfolioAccount portfolioAccount) {
    logger.info(
        "Updating portfolio movement with ID: {} by external movement with id {}",
        portfolioMovement.getId(),
        externalMovement.getOperationId());
    if (validateLiquidationDate) validateLiquidationDateTime(portfolioMovement);
    else
      logger.info(
          "Skipping liquidation date validation for portfolio movement with ID: {}",
          portfolioMovement.getId());
    portfolioMovement =
        portfolioMovementService.update(portfolioMovement, externalMovement, portfolioAccount);
    logger.info(
        "portfolio movement: {}, updated successfully by external movement",
        portfolioMovement.getId());
    return portfolioMovement;
  }

  private void validateLiquidationDateTime(PortfolioMovement portfolioMovement) {
    if (portfolioMovement.getSettlementAt().isBefore(BuenosAiresTime.nowAsLocalDate())) {
      String message =
          "Cannot update portfolio movement %s after liquidation date [%s]"
              .formatted(portfolioMovement.getId(), portfolioMovement.getSettlementAt());
      notifyDiscord(message);
      throw new CannotUpdatePortfolioMovementException(portfolioMovement.getId(), message);
    }
  }
}
