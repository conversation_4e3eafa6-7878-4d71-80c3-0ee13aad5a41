package ar.com.daruma.bymas.balanceReconciliation.entities;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;

public record PortfolioBalanceWithHistoryAndValuations(
    PortfolioPositionBalance portfolioBalance,
    PortfolioPositionHistory positionHistory,
    PortfolioPositionValuation balanceValuation,
    PortfolioPositionHistoryValuation historyValuation) {}
