package ar.com.daruma.bymas.balanceReconciliation.controller;

import static ar.com.daruma.bymas.utils.numbers.StaticNumberUtils.one;

import ar.com.daruma.bymas.balanceReconciliation.application.excel.ReconciliationExcelService;
import ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets;
import ar.com.daruma.bymas.security.application.BymasAuthorizationUtils;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/balance-reconciliation")
@Validated
@RequiredArgsConstructor
public class BalanceReconciliationController {

  private final ReconciliationExcelService reconciliationExcelService;
  private final BymasAuthorizationUtils bymasAuthorizationUtils;

  @BymasAuthorization
  @GetMapping("")
  public ResponseEntity<byte[]> reconcile(
      @RequestHeader(name = "X-Client-Target", required = false) String clientTarget,
      @RequestParam(name = "date", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
          LocalDate date,
      @RequestParam(name = "account-id", required = false) Integer accountId) {
    String accountCompanyFromContext = bymasAuthorizationUtils.getAccountCompanyNameFromContext();
    if (bymasAuthorizationUtils.isAllariaMasAdmin()) {
      accountCompanyFromContext = clientTarget;
    }
    if (date == null) {
      date = BuenosAiresTime.nowAsLocalDate().minusDays(one);
    }
    try (XSSFWorkbook workbook =
            reconciliationExcelService.generateReconciliationExcel(
                date,
                Optional.ofNullable(accountId),
                ByMasCompanySecrets.fromString(accountCompanyFromContext));
        ByteArrayOutputStream out = new ByteArrayOutputStream()) {
      workbook.write(out);
      byte[] bytes = out.toByteArray();
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(
          MediaType.parseMediaType(
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
      headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=reconciliation.xlsx");
      return ResponseEntity.ok().headers(headers).body(bytes);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }
}
