package ar.com.daruma.bymas.balanceReconciliation.application.reconcile;

import ar.com.daruma.bymas.balanceReconciliation.entities.PortfolioBalanceWithHistoryAndValuations;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.portfolioPosition.application.find.FindPortfolioPositionHistoriesByDateAndMaybePortfolioAccount;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.application.find.FindLatestPortfolioPositionBalancesByDateAndMaybePortfolioAccount;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import ar.com.daruma.bymas.portfolioPositionValuation.application.find.FindPortfolioPositionHistoryValuations;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfoliosThatRequireConciliation {

  private final FindPortfolioPositionHistoriesByDateAndMaybePortfolioAccount
      findPortfolioPositionHistoriesByDateAndMaybePortfolioAccount;
  private final FindLatestPortfolioPositionBalancesByDateAndMaybePortfolioAccount
      findLatestPortfolioPositionBalancesByDateAndMaybePortfolioAccount;
  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;
  private final FindPortfolioPositionHistoryValuations findPortfolioPositionHistoryValuations;
  public List<PortfolioBalanceWithHistoryAndValuations> find(
      LocalDate date, Optional<Integer> accountId, PortfolioCompany company) {
    log.info("Finding portfolios that require reconciliation for date: {}", date);
    Optional<PortfolioAccount> maybeAccount =
        accountId.map(id -> findPortfolioAccountByAccountId.findAccountByIdAndCompany(id, company));
    List<PortfolioPositionHistory> histories =
        findPortfolioPositionHistoriesByDateAndMaybePortfolioAccount.find(date, maybeAccount);
    List< PortfolioPositionHistoryValuation> historyValuations = findPortfolioPositionHistoryValuations.byPositionHistoryIds(
        histories.stream().map(PortfolioPositionHistory::getId).toList());
    List<PortfolioPositionBalance> balances =
        findLatestPortfolioPositionBalancesByDateAndMaybePortfolioAccount.find(date, maybeAccount);
    Map<BasePortfolioPosition.AssetAndAccountIdPair, PortfolioPositionBalance>
        balancesByAssetAndAccountIdPair =
            balances.stream()
                .collect(
                    Collectors.toMap(
                        PortfolioPositionBalance::getAssetAndAccountIdPair, balance -> balance));
    return histories.stream()
        .map(
            history -> {
              Optional<PortfolioPositionBalance> maybeBalance =
                  Optional.ofNullable(
                      balancesByAssetAndAccountIdPair.getOrDefault(
                          history.getAssetAndAccountIdPair(), null));
              return maybeBalance
                  .map(
                      balance -> {
                        if (balance.hasDifferentQuantities(history)) {
                          return new PortfolioBalanceWithHistoryAndValuations(balance, history);
                        }
                        return null;
                      })
                  .orElse(null);
            })
        .filter(Objects::nonNull)
        .toList();
  }
}
