package ar.com.daruma.bymas.balanceReconciliation.application.excel;

import ar.com.daruma.bymas.balanceReconciliation.application.reconcile.FindPortfoliosThatRequireConciliation;
import ar.com.daruma.bymas.balanceReconciliation.entities.PortfolioBalanceWithHistoryAndValuations;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionBalances.infrastructure.entities.PortfolioPositionBalance;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ReconciliationExcelService {

  private final FindPortfoliosThatRequireConciliation findPortfoliosThatRequireConciliation;

  @Transactional(transactionManager = "byMasTransactionManager")
  public XSSFWorkbook generateReconciliationExcel(
      LocalDate date, Optional<Integer> accountId, PortfolioCompany company) {
    List<PortfolioBalanceWithHistoryAndValuations> portfolios =
        findPortfoliosThatRequireConciliation.find(date, accountId, company);
    XSSFWorkbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("Reconciliation");
    Row header = sheet.createRow(0);
    header.createCell(0).setCellValue("Cuenta");
    header.createCell(1).setCellValue("Activo");
    header.createCell(2).setCellValue("Saldo de liquidación nuestro");
    header.createCell(3).setCellValue("Saldo de liquidación de azul");
    header.createCell(4).setCellValue("diferencia de saldos de liquidación");
    header.createCell(5).setCellValue("Saldo de concertación nuestro");
    header.createCell(6).setCellValue("Saldo de concertación de azul");
    header.createCell(7).setCellValue("diferencia de saldos de concertación");
    for (int i = 0; i < portfolios.size(); i++) {
      PortfolioBalanceWithHistoryAndValuations portfolioBalanceWithHistoryAndValuations =
          portfolios.get(i);
      PortfolioPositionBalance balance =
          portfolioBalanceWithHistoryAndValuations.portfolioBalance();
      PortfolioPositionHistory history = portfolioBalanceWithHistoryAndValuations.positionHistory();
      Row row = sheet.createRow(i + 1);
      row.createCell(0)
          .setCellValue(balance.getPortfolioPosition().getPortfolioAccount().getName());
      row.createCell(1).setCellValue(balance.getPortfolioPosition().getPortfolioAsset().getName());
      row.createCell(2).setCellValue(balance.getSettlementQuantity().doubleValue());
      row.createCell(3).setCellValue(history.getSettlementQuantity().doubleValue());
      row.createCell(4)
          .setCellValue(
              balance
                  .getSettlementQuantity()
                  .subtract(history.getSettlementQuantity())
                  .doubleValue());
      row.createCell(5).setCellValue(balance.getAgreementQuantity().doubleValue());
      row.createCell(6).setCellValue(history.getAgreementQuantity().doubleValue());
      row.createCell(7)
          .setCellValue(
              balance
                  .getAgreementQuantity()
                  .subtract(history.getAgreementQuantity())
                  .doubleValue());
    }
    return workbook;
  }
}
