package ar.com.daruma.bymas.allariaMas.accounts.infrastructure.entities;

import ar.com.daruma.bymas.allariaMas.accounts.domain.AllariaMasAccount;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

@Getter
@Setter
@Entity
@Table(name = "accounts")
public class AccountEntity implements AllariaMasAccount {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", nullable = false)
  private Integer id;

  @Size(max = 11)
  @NotNull
  @Column(name = "cuit", nullable = false, length = 11)
  private String cuit;

  @NotNull
  @Column(name = "name", nullable = false, length = Integer.MAX_VALUE)
  private String name;

  @NotNull
  @Column(name = "person_type", nullable = false, length = Integer.MAX_VALUE)
  private String personType;

  @Size(max = 22)
  @Column(name = "cvu", length = 22)
  private String cvu;

  @Size(max = 22)
  @Column(name = "cbu", length = 22)
  private String cbu;

  @Column(name = "alias", length = Integer.MAX_VALUE)
  private String alias;

  @Column(name = "bank", length = Integer.MAX_VALUE)
  private String bank;

  @NotNull
  @Column(name = "currency", nullable = false, length = Integer.MAX_VALUE)
  private String currency;

  @NotNull
  @Column(name = "state", nullable = false, length = Integer.MAX_VALUE)
  private String state;

  @NotNull
  @Column(name = "owner", nullable = false, length = Integer.MAX_VALUE)
  private String owner;

  @NotNull
  @Column(name = "creation_date_time", nullable = false)
  private Instant creationDateTime;

  @NotNull
  @Column(name = "last_modification_date_time", nullable = false)
  private Instant lastModificationDateTime;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "available_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal availableCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "locked_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal lockedCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "borrowed_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal borrowedCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "shares", nullable = false, precision = 29, scale = 10)
  private BigDecimal shares;

  @Column(name = "investment_account_id", length = Integer.MAX_VALUE)
  private String investmentAccountId;

  @NotNull
  @ColumnDefault("false")
  @Column(name = "money_market_subscriber", nullable = false)
  private Boolean moneyMarketSubscriber = false;

  @NotNull
  @ColumnDefault("false")
  @Column(name = "lender", nullable = false)
  private Boolean lender = false;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "pending_subscription_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal pendingSubscriptionCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "internal_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal internalCash;

  @NotNull
  @ColumnDefault("false")
  @Column(name = "can_switch_lender", nullable = false)
  private Boolean canSwitchLender = false;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "pending_funds_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal pendingFundsCash;

  @NotNull
  @ColumnDefault("true")
  @Column(name = "can_receive_internal_cash", nullable = false)
  private Boolean canReceiveInternalCash = false;

  @Column(name = "authorizationless_transaction_max_amount", precision = 19, scale = 2)
  private BigDecimal authorizationlessTransactionMaxAmount;

  @ColumnDefault("false")
  @Column(name = "celeri_send_operations")
  private Boolean celeriSendOperations;

  @ColumnDefault("'NOT_NEEDED'")
  @Column(name = "esco_account_state", length = Integer.MAX_VALUE)
  private String escoAccountState;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "allaria_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal allariaCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "mirrored_advanced_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal mirroredAdvancedCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "locked_advanced_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal lockedAdvancedCash;

  @NotNull
  @ColumnDefault("0")
  @Column(name = "internal_mirrored_advanced_cash", nullable = false, precision = 29, scale = 10)
  private BigDecimal internalMirroredAdvancedCash;

  public Boolean isPhysical() {
    return getPersonType().equals("PHYSICAL");
  }

  private BigDecimal balance(BigDecimal lastSettlementValue) {
    BigDecimal amountInShares = shares.multiply(lastSettlementValue);
    BigDecimal positiveBalance =
        availableCash.add(internalCash).add(amountInShares).add(allariaCash);
    BigDecimal negativeBalance =
        borrowedCash
            .add(pendingFundsCash)
            .add(mirroredAdvancedCash)
            .add(internalMirroredAdvancedCash);
    return positiveBalance.subtract(negativeBalance).setScale(2, RoundingMode.HALF_UP);
  }

  @Override
  public BigDecimal getAgreementQuantity(BigDecimal lastSettlementValue) {
    return balance(lastSettlementValue);
  }

  @Override
  public BigDecimal getSettlementQuantity(BigDecimal lastSettlementValue) {
    return balance(lastSettlementValue);
  }

  @Override
  public BigDecimal getAvailableQuantity() {
    return availableCash;
  }

  @Override
  public BigDecimal getLockedQuantity() {
    return lockedCash;
  }
}
