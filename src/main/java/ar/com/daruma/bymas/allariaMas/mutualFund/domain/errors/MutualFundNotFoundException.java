package ar.com.daruma.bymas.allariaMas.mutualFund.domain.errors;

import ar.com.daruma.citadel.exceptions.NotFoundException;

public class MutualFundNotFoundException extends NotFoundException {
  public MutualFundNotFoundException(Integer id) {
    super("Mutual fund with id " + id + " not found");
  }

  public MutualFundNotFoundException(String param, String value) {
    super("Mutual fund with " + param + " " + value + " not found");
  }
}
