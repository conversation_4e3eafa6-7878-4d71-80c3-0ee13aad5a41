package ar.com.daruma.bymas.allariaMas.mutualFund.application.find;

import ar.com.daruma.bymas.allariaMas.mutualFund.domain.errors.MutualFundNotFoundException;
import ar.com.daruma.bymas.allariaMas.mutualFund.domain.service.MutualFundService;
import ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.entities.MutualFund;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindMutualFundById {

  @Autowired private MutualFundService mutualFundService;

  private static final Logger logger = LogManager.getLogger(FindMutualFundById.class);

  public MutualFund find(Integer id) {
    logger.info("Finding mutual fund with id {}", id);
    MutualFund mutualFund = mutualFundService.findById(id);
    if (mutualFund != null) {
      logger.info("Found mutual fund: {} - {}", mutualFund.getCode(), mutualFund.getDescription());
      return mutualFund;
    } else {
      logger.error("No mutual fund found with id {}", id);
      throw new MutualFundNotFoundException(id);
    }
  }
}
