package ar.com.daruma.bymas.allariaMas.accounts.infrastructure.adapter;

import ar.com.daruma.bymas.allariaMas.accounts.domain.service.AccountsService;
import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.entities.AccountEntity;
import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.repository.AccountsRepository;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AccountsServiceImpl implements AccountsService {

  private static final int BATCH_SIZE = 5000;
  private final AccountsRepository accountsRepository;

  @Override
  public List<AccountEntity> findAll() {
    List<AccountEntity> allAccounts = new ArrayList<>();
    Pageable pageable = PageRequest.of(0, BATCH_SIZE);
    Page<AccountEntity> accountPage;

    do {
      accountPage = accountsRepository.findAll(pageable);
      allAccounts.addAll(accountPage.getContent());
      pageable = accountPage.nextPageable();
    } while (accountPage.hasNext());

    return allAccounts;
  }
}
