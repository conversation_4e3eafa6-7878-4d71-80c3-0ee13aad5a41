package ar.com.daruma.bymas.allariaMas.settlement.infrastructure.adapter;

import ar.com.daruma.bymas.allariaMas.settlement.domain.enums.SettlementService;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities.Settlement;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.repository.SettlementRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SettlementServiceImpl implements SettlementService {

  @Autowired private SettlementRepository settlementRepository;

  @Override
  public Settlement findLast(Integer fundId) {
    return settlementRepository.findFirstByFundIdOrderByDateDesc(fundId);
  }
}
