package ar.com.daruma.bymas.allariaMas.accounts.application.find;

import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.entities.AccountEntity;
import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.repository.AccountsRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindAllariaMasAccountsInBatches {

  private final AccountsRepository accountsRepository;

  public Page<AccountEntity> find(List<Integer> accountIds, Pageable pageable) {
    if (accountIds == null || accountIds.isEmpty()) {
      return accountsRepository.findAll(pageable);
    } else {
      return accountsRepository.findByIdIn(accountIds, pageable);
    }
  }
}
