package ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.adapter;

import ar.com.daruma.bymas.allariaMas.mutualFund.domain.service.MutualFundService;
import ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.entities.MutualFund;
import ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.repository.MutualFundRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MutualFundServiceImpl implements MutualFundService {

  @Autowired private MutualFundRepository mutualFundRepository;

  @Override
  public MutualFund findById(Integer id) {
    return mutualFundRepository.findById(id).orElse(null);
  }
}
