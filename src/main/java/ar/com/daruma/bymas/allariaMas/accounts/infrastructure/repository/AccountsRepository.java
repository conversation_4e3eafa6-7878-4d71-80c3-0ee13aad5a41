package ar.com.daruma.bymas.allariaMas.accounts.infrastructure.repository;

import ar.com.daruma.bymas.allariaMas.accounts.infrastructure.entities.AccountEntity;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AccountsRepository extends JpaRepository<AccountEntity, Integer> {
  Page<AccountEntity> findByIdIn(List<Integer> ids, Pageable pageable);
}
