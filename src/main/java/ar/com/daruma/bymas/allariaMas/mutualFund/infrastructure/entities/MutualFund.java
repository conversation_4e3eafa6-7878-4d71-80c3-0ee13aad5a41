package ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.entities;

import jakarta.persistence.*;
import jakarta.persistence.Entity;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mutual_funds")
public class MutualFund {

  @Id private Integer id;

  private String code;
  private String currency;
  private String description;

  @Column(name = "creation_date_time")
  private LocalDateTime creationDateTime;

  @Column(name = "created_by")
  private Integer createdBy;

  @Column(name = "t_day")
  private Integer tDay;

  public String getCategory() {
    return "Instrumentos";
  }

  public String getSubCategory() {
    return "FCI Abiertos";
  }

  public String getAbbreviation() {
    return getCode();
  }
}
