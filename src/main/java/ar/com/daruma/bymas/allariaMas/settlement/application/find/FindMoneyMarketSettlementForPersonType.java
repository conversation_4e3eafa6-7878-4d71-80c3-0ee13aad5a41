package ar.com.daruma.bymas.allariaMas.settlement.application.find;

import ar.com.daruma.bymas.allariaMas.settlement.domain.enums.SettlementService;
import ar.com.daruma.bymas.allariaMas.settlement.domain.errors.SettlementNotFoundException;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities.Settlement;
import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.utils.SettlementUtils;
import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindMoneyMarketSettlementForPersonType {

  @Autowired private SettlementUtils settlementUtils;
  @Autowired private SettlementService settlementService;

  private static final Logger logger =
      LogManager.getLogger(FindMoneyMarketSettlementForPersonType.class);

  public Settlement find(PersonType personType) {
    logger.info("Finding last money market settlement for person type {}", personType);
    Integer fundId = settlementUtils.getMoneyMarketFundId(personType);
    Settlement settlement = settlementService.findLast(fundId);
    if (settlement == null) {
      logger.error("No money market settlement found for person type {}", personType);
      throw new SettlementNotFoundException(personType);
    }
    logger.info(
        "Found last money market settlement {} for person type {}", settlement.getId(), personType);
    return settlement;
  }
}
