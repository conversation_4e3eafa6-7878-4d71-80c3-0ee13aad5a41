package ar.com.daruma.bymas.allariaMas.settlement.infrastructure.utils;

import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import org.springframework.stereotype.Service;

@Service
public class SettlementUtils {

  private final Integer physicalMoneyMarketId =
      Integer.parseInt(System.getenv("PHYSICAL_MONEY_MARKET_ID"));
  private final Integer juridicalMoneyMarketId =
      Integer.parseInt(System.getenv("JURIDICAL_MONEY_MARKET_ID"));

  public Integer getMoneyMarketFundId(PersonType personType) {
    return switch (personType) {
      case PHYSICAL -> physicalMoneyMarketId;
      case JURIDICAL -> juridicalMoneyMarketId;
    };
  }
}
