package ar.com.daruma.bymas.allariaMas.settlement.infrastructure.repository;

import ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities.Settlement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SettlementRepository extends JpaRepository<Settlement, Integer> {
  Settlement findFirstByFundIdOrderByDateDesc(Integer fundId);
}
