package ar.com.daruma.bymas.allariaMas.settlement.infrastructure.entities;

import ar.com.daruma.bymas.allariaMas.settlement.domain.enums.SettlementState;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "settlements")
public class Settlement {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "settlements_id_gen")
  @SequenceGenerator(
      name = "settlements_id_gen",
      sequenceName = "settlements_id_seq",
      allocationSize = 1)
  @Column(name = "id", nullable = false)
  private Integer id;

  @Column(name = "fund_id", nullable = false)
  private Integer fundId;

  @NotNull
  @Column(name = "date", nullable = false)
  private LocalDate date;

  @Column(name = "share_value", precision = 29, scale = 10)
  private BigDecimal shareValue;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "state", nullable = false)
  private SettlementState state;

  @NotNull
  @Column(name = "creation_date_time", nullable = false)
  private Instant creationDateTime;

  @NotNull
  @Column(name = "last_modification_date_time", nullable = false)
  private Instant lastModificationDateTime;
}
