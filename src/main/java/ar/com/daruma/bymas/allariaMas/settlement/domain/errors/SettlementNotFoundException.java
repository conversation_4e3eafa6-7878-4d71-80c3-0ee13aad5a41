package ar.com.daruma.bymas.allariaMas.settlement.domain.errors;

import ar.com.daruma.bymas.portfolioAccount.domain.entities.PersonType;
import ar.com.daruma.citadel.exceptions.NotFoundException;

public class SettlementNotFoundException extends NotFoundException {
  public SettlementNotFoundException(Integer id) {
    super("Settlement with id " + id + " not found");
  }

  public SettlementNotFoundException(String param, String value) {
    super("Settlement with " + param + " " + value + " not found");
  }

  public SettlementNotFoundException(PersonType personType) {
    super("Money market settlement for person type " + personType + " not found");
  }
}
