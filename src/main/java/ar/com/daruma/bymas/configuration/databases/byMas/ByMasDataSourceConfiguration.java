package ar.com.daruma.bymas.configuration.databases.byMas;

import ar.com.daruma.bymas.configuration.databases.DatabaseConfig;
import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ByMasDataSourceConfiguration {

  @Autowired private DatabaseConfig config;

  @Bean
  @ConfigurationProperties("spring.datasource.by-mas")
  public DataSourceProperties byMasDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  @Primary
  public DataSource byMasDataSource() {
    HikariDataSource dataSource =
        byMasDataSourceProperties()
            .initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
    dataSource.setPoolName("ByMasDataSourcePool");
    dataSource.setMaximumPoolSize(config.getBymasMaximumPoolSize());
    dataSource.setConnectionTimeout(config.getBymasConnTimeOut());
    return dataSource;
  }
}
