package ar.com.daruma.bymas.configuration.databases.allariaMas;

import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.*;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "ar.com.daruma.bymas.allariaMas",
    entityManagerFactoryRef = "allariaMasEntityManagerFactory",
    transactionManagerRef = "allariaMasTransactionManager")
public class AllariaMasJpaConfiguration {

  @Bean
  public LocalContainerEntityManagerFactoryBean allariaMasEntityManagerFactory(
      @Qualifier("allariaMasDataSource") DataSource dataSource,
      EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(dataSource)
        .packages("ar.com.daruma.bymas.allariaMas")
        .persistenceUnit("allariaMas")
        .build();
  }

  @Bean
  public PlatformTransactionManager allariaMasTransactionManager(
      @Qualifier("allariaMasEntityManagerFactory")
          LocalContainerEntityManagerFactoryBean allariaMasEntityManagerFactory) {
    return new JpaTransactionManager(
        Objects.requireNonNull(allariaMasEntityManagerFactory.getObject()));
  }
}
