package ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization;

import org.hibernate.boot.model.TypeContributions;
import org.hibernate.boot.model.TypeContributor;
import org.hibernate.service.ServiceRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JsonNodeTypeContributor implements TypeContributor {

  @Autowired private JsonNodeUserType jsonNodeUserType;

  @Override
  public void contribute(TypeContributions typeContributions, ServiceRegistry serviceRegistry) {
    typeContributions.contributeType(jsonNodeUserType, "json-node");
  }
}
