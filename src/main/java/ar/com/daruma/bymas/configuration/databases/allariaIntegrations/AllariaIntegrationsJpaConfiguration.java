package ar.com.daruma.bymas.configuration.databases.allariaIntegrations;

import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "ar.com.daruma.bymas.allariaIntegrations",
    entityManagerFactoryRef = "allariaIntegrationsEntityManagerFactory",
    transactionManagerRef = "allariaIntegrationsTransactionManager")
public class AllariaIntegrationsJpaConfiguration {

  @Bean
  public LocalContainerEntityManagerFactoryBean allariaIntegrationsEntityManagerFactory(
      @Qualifier("allariaIntegrationsDataSource") DataSource dataSource,
      EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(dataSource)
        .packages("ar.com.daruma.bymas.allariaIntegrations")
        .persistenceUnit("allariaIntegrations")
        .build();
  }

  @Bean
  public PlatformTransactionManager allariaIntegrationsTransactionManager(
      @Qualifier("allariaIntegrationsEntityManagerFactory")
          LocalContainerEntityManagerFactoryBean allariaIntegrationsEntityManagerFactory) {
    return new JpaTransactionManager(
        Objects.requireNonNull(allariaIntegrationsEntityManagerFactory.getObject()));
  }
}
