package ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Objects;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.usertype.UserType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class JsonNodeUserType implements UserType<JsonNode> {

  private static ObjectMapper objectMapper;

  @Autowired
  public void setObjectMapper(ObjectMapper mapper) {
    JsonNodeUserType.objectMapper = mapper;
  }

  @Override
  public int getSqlType() {
    // Use Types.OTHER as a generic type for custom objects.
    // For PostgreSQL, you might use a specific type if your dialect supports it.
    return Types.OTHER;
  }

  @Override
  public Class<JsonNode> returnedClass() {
    return JsonNode.class;
  }

  @Override
  public boolean equals(JsonNode x, JsonNode y) {
    return Objects.equals(x, y);
  }

  @Override
  public int hashCode(JsonNode x) {
    return Objects.hashCode(x);
  }

  @Override
  public JsonNode nullSafeGet(
      ResultSet rs, int position, SharedSessionContractImplementor session, Object owner)
      throws SQLException {
    final String json = rs.getString(position);
    if (json == null) {
      return null;
    }
    try {
      // Use the injected objectMapper to ensure consistent deserialization
      return objectMapper.readTree(json);
    } catch (Exception e) {
      throw new RuntimeException("Failed to convert String to JsonNode", e);
    }
  }

  @Override
  public void nullSafeSet(
      PreparedStatement st, JsonNode value, int index, SharedSessionContractImplementor session)
      throws SQLException {
    if (value == null) {
      st.setNull(index, Types.OTHER);
      return;
    }
    try {
      // Set the value as a string in the PreparedStatement
      st.setObject(index, objectMapper.writeValueAsString(value), Types.OTHER);
    } catch (Exception e) {
      throw new RuntimeException("Failed to convert JsonNode to String", e);
    }
  }

  @Override
  public JsonNode deepCopy(JsonNode value) {
    if (value == null) {
      return null;
    }
    // JsonNode has a built-in deepCopy method
    return value.deepCopy();
  }

  @Override
  public boolean isMutable() {
    return true;
  }

  @Override
  public Serializable disassemble(JsonNode value) {
    return value.toString();
  }

  @Override
  public JsonNode assemble(Serializable cached, Object owner) {
    try {
      // Parse the string representation back into a JsonNode
      return objectMapper.readTree((String) cached);
    } catch (Exception e) {
      throw new RuntimeException("Failed to convert String to JsonNode", e);
    }
  }
}
