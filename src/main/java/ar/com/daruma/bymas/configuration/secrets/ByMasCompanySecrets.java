package ar.com.daruma.bymas.configuration.secrets;

import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.UUID;

public class ByMasCompanySecrets {

  public static final PortfolioCompany allariaMasCompany =
      new PortfolioCompany(UUID.fromString(System.getenv("ALLARIA_MAS_COMPANY_UUID")), "Allaria +");
  public static final PortfolioCompany allariaCompany =
      new PortfolioCompany(UUID.fromString(System.getenv("ALLARIA_COMPANY_UUID")), "Allaria");

  public static PortfolioCompany fromString(String name) {
    return switch (name) {
      case "ALLARIA_MAS" -> allariaMasCompany;
      case "ALLARIA" -> allariaCompany;
      default -> throw new IllegalArgumentException("No portfolio company with name " + name);
    };
  }
}
