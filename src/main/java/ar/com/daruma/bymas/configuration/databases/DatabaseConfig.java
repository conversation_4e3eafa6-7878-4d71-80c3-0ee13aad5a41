package ar.com.daruma.bymas.configuration.databases;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class DatabaseConfig {
  @Value("${by-mas.maximum-pool-size}")
  private Integer bymasMaximumPoolSize;

  @Value("${allaria-int.maximum-pool-size}")
  private Integer allariaIntMaximumPoolSize;

  @Value("${by-mas.conn-time-out}")
  private Integer bymasConnTimeOut;

  @Value("${allaria-int.conn-time-out}")
  private Integer allariaIntConnTimeOut;
}
