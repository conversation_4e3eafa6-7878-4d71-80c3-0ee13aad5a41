package ar.com.daruma.bymas.configuration;

import ar.com.daruma.bymas.security.application.BymasAuthFilter;
import ar.com.daruma.citadel.security.CustomAuthenticationEntryPoint;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

  @Autowired private CustomAuthenticationEntryPoint customAuthenticationEntryPoint;
  @Autowired private BymasAuthFilter bymasAuthFilter;

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http.csrf(AbstractHttpConfigurer::disable);
    http.cors(cors -> cors.configurationSource(corsConfigurationSource()));

    http.sessionManagement(
        sessionManagement ->
            sessionManagement.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

    http.exceptionHandling(
        errorHandling -> errorHandling.authenticationEntryPoint(customAuthenticationEntryPoint));

    http.authorizeHttpRequests(
        authorize ->
            authorize
                .requestMatchers("/xxx/**")
                .authenticated()
                .requestMatchers(HttpMethod.OPTIONS, "/**")
                .permitAll()
                .requestMatchers("/healthcheck")
                .permitAll()
                .requestMatchers("/version")
                .permitAll()
                .requestMatchers("/api-docs/**")
                .permitAll()
                .requestMatchers("/docs")
                .permitAll()
                .requestMatchers("/swagger-ui/**")
                .permitAll()
                .requestMatchers("/error/**")
                .permitAll()
                .anyRequest()
                .authenticated());

    http.addFilterBefore(bymasAuthFilter, UsernamePasswordAuthenticationFilter.class);

    return http.build();
  }

  @Bean
  CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowedOrigins(
        Arrays.asList(
            "https://app.midaruma.com.ar",
            "https://app.midaruma.dev",
            "https://app.allariamas.com.ar",
            "https://app.allariamas.dev",
            "https://app.allaria.com.ar",
            "https://app.allaria.dev",
            "localhost:3000",
            "http://localhost:3000",
            "https://localhost:3000",
            "localhost:8080",
            "localhost:8081",
            "https://grotesco.svc.internal.allaria.dev",
            "https://grotesco.svc.internal.allaria.cloud",
            "http://localhost:8080",
            "https://localhost:8080"));
    configuration.setAllowedMethods(List.of("*"));
    configuration.setAllowedHeaders(List.of("*"));
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }
}
