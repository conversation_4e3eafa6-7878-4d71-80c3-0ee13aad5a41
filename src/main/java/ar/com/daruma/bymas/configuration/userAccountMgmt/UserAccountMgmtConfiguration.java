package ar.com.daruma.bymas.configuration.userAccountMgmt;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserAccountMgmtConfiguration {
  @Value("${user-account-mgmt.host}")
  private String host;

  public String getHost() {
    return host;
  }

  public void setHost(String host) {
    this.host = host;
  }
}
