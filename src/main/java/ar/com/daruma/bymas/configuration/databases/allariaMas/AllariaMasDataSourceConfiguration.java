package ar.com.daruma.bymas.configuration.databases.allariaMas;

import javax.sql.DataSource;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AllariaMasDataSourceConfiguration {
  @Bean
  @ConfigurationProperties("spring.datasource.allaria-mas")
  public DataSourceProperties allariaMasDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  public DataSource allariaMasDataSource() {
    return allariaMasDataSourceProperties().initializeDataSourceBuilder().build();
  }
}
