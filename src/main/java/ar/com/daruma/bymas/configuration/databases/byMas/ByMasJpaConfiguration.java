package ar.com.daruma.bymas.configuration.databases.byMas;

import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.*;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "ar.com.daruma.bymas",
    excludeFilters =
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = {
              "ar.com.daruma.bymas.allariaIntegrations.*",
              "ar.com.daruma.bymas.allariaMas.*"
            }),
    entityManagerFactoryRef = "byMasEntityManagerFactory",
    transactionManagerRef = "byMasTransactionManager")
public class ByMasJpaConfiguration {

  @Bean
  @Primary
  public LocalContainerEntityManagerFactoryBean byMasEntityManagerFactory(
      @Qualifier("byMasDataSource") DataSource dataSource, EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(dataSource)
        .packages(packagesToScan())
        .persistenceUnit("byMas")
        .build();
  }

  @Bean
  public PlatformTransactionManager byMasTransactionManager(
      @Qualifier("byMasEntityManagerFactory")
          LocalContainerEntityManagerFactoryBean byMasEntityManagerFactory) {
    return new JpaTransactionManager(Objects.requireNonNull(byMasEntityManagerFactory.getObject()));
  }

  private String[] packagesToScan() {
    return new String[] {
      "ar.com.daruma.bymas",
      "!ar.com.daruma.bymas.allariaIntegrations",
      "!ar.com.daruma.bymas.allariaMas"
    };
  }
}
