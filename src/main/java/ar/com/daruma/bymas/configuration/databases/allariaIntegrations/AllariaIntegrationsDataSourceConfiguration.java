package ar.com.daruma.bymas.configuration.databases.allariaIntegrations;

import ar.com.daruma.bymas.configuration.databases.DatabaseConfig;
import com.zaxxer.hikari.HikariDataSource;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AllariaIntegrationsDataSourceConfiguration {
  @Autowired private DatabaseConfig config;

  @Bean
  @ConfigurationProperties("spring.datasource.allaria-integrations")
  public DataSourceProperties allariaIntegrationsDataSourceProperties() {
    return new DataSourceProperties();
  }

  @Bean
  public DataSource allariaIntegrationsDataSource() {
    HikariDataSource dataSource =
        allariaIntegrationsDataSourceProperties()
            .initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
    dataSource.setPoolName("AllariaIntDataSourcePool");
    dataSource.setMaximumPoolSize(config.getAllariaIntMaximumPoolSize());
    dataSource.setConnectionTimeout(config.getAllariaIntConnTimeOut());
    return dataSource;
  }
}
