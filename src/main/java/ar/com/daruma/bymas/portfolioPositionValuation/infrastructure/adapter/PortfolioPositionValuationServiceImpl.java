package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.adapter;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.PortfolioPositionValuationService;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionValuationRow;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.repository.PortfolioPositionHistoryValuationRepository;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.repository.PortfolioPositionValuationRepository;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class PortfolioPositionValuationServiceImpl implements PortfolioPositionValuationService {
  private final PortfolioPositionValuationRepository repository;

  private final PortfolioPositionHistoryValuationRepository historyRepository;

  // Words per criteria string : 75 (2 uuids + 1 delimiters(|))
  // 870 criterias = 65250
  private static final Integer MAX_CRITERIA_LENGTH = 65335;
  private static final Integer MAX_CRITERIAS = 870;

  @Override
  public PortfolioPositionValuationRow save(PortfolioPositionValuationRow positionValuation) {
    return repository.save(positionValuation);
  }

  @Override
  public PortfolioPositionHistoryValuation save(
      PortfolioPositionHistoryValuation positionHistoryValuation) {
    return historyRepository.save(positionHistoryValuation);
  }

  @Override
  public Optional<PortfolioPositionValuationRow> findById(UUID id) {
    return repository.findById(id);
  }

  @Override
  public Optional<PortfolioPositionHistoryValuation> findHistoryById(UUID id) {
    return historyRepository.findById(id);
  }

  @Override
  public List<PortfolioPositionValuationRow> findByPortfolioPositionId(UUID positionId) {
    return repository.findByPortfolioPositionId(positionId);
  }

  @Override
  public List<PortfolioPositionHistoryValuation> findHistoryByPortfolioPositionId(UUID positionId) {
    return historyRepository.findByPortfolioPositionHistoryId(positionId);
  }

  @Override
  public List<PortfolioPositionValuationRow> findByPortfolioCurrencyValuationId(
      UUID currencyValuationId) {
    return repository.findByPortfolioCurrencyValuationId(currencyValuationId);
  }

  @Override
  public List<PortfolioPositionHistoryValuation> findHistoryByPortfolioCurrencyValuationId(
      UUID currencyValuationId) {
    return historyRepository.findByPortfolioCurrencyValuationId(currencyValuationId);
  }

  @Override
  public void findsertPortfolioPositionHistoryValuationsByPortfolios(
      List<PortfolioPositionHistory> histories,
      Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryKey,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId) {
    Set<UUID> portfolioPositionHistoryIds =
        histories.stream().map(PortfolioPositionHistory::getId).collect(Collectors.toSet());
    Set<UUID> currencyValuationIds =
        portfolioAssetsByCurrencyValuationId.values().stream()
            .map(PortfolioCurrencyValuation::getId)
            .collect(Collectors.toSet());
    List<PortfolioPositionHistoryValuation.PortfolioPositionHistoryValuationKey> searchKeysKeys;
    Set<PortfolioPositionHistoryValuation> existingValuations =
        historyRepository
            .findDistinctByPortfolioPositionHistoryIdInAndPortfolioCurrencyValuationIdIn(
                portfolioPositionHistoryIds, currencyValuationIds);
    Set<PortfolioPositionHistoryValuation.PortfolioPositionHistoryValuationKey>
        existingValuationsKeys =
            existingValuations.stream()
                .map(PortfolioPositionHistoryValuation::getHistoryValuationKey)
                .collect(Collectors.toSet());
    List<PortfolioPositionHistoryValuation> newValuations =
        histories.stream()
            .flatMap(
                history ->
                    createNewValuationsForHistory(
                        history,
                        portfolioGroupsByHistoryKey,
                        portfolioAssetsByCurrencyValuationId,
                        existingValuationsKeys))
            .toList();
    historyRepository.saveAll(newValuations);
  }

  private List<PortfolioPositionHistoryValuation> findExistingValuations(List<String> criteria) {
    List<PortfolioPositionHistoryValuation> histories = new ArrayList<>(List.of());
    while (getTotalLengthFromCriteria(criteria) > MAX_CRITERIA_LENGTH) {
      histories.addAll(
          historyRepository.findByMultipleCriteria(criteria.subList(0, MAX_CRITERIAS)));
      criteria = criteria.subList(MAX_CRITERIAS, criteria.size());
    }
    histories.addAll(historyRepository.findByMultipleCriteria(criteria));
    return histories;
  }

  private Integer getTotalLengthFromCriteria(List<String> criteria) {
    return criteria.stream().mapToInt(String::length).sum();
  }

  private List<String> generateSearchKeysForHistory(
      PortfolioPositionHistory history,
      Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryKey,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId) {
    List<List<Portfolio>> portfoliosByHistory =
        portfolioGroupsByHistoryKey.get(history.getHistoryKey());
    return portfoliosByHistory.stream()
        .map(
            portfolios ->
                createSearchKeyForPortfolios(
                    history, portfolios, portfolioAssetsByCurrencyValuationId))
        .toList();
  }

  private String createSearchKeyForPortfolios(
      PortfolioPositionHistory history,
      List<Portfolio> portfolios,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId) {
    Portfolio firstPortfolio = portfolios.get(0);
    return history.getId()
        + "|"
        + portfolioAssetsByCurrencyValuationId.get(firstPortfolio.currencyValuationId()).getId();
  }

  private Stream<PortfolioPositionHistoryValuation> createNewValuationsForHistory(
      PortfolioPositionHistory history,
      Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryKey,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId,
      Set<PortfolioPositionHistoryValuation.PortfolioPositionHistoryValuationKey>
          existingValuationsKeys) {
    List<List<Portfolio>> portfoliosByHistory =
        portfolioGroupsByHistoryKey.get(history.getHistoryKey());
    return portfoliosByHistory.stream()
        .filter(
            portfolios ->
                isNotExistingValuation(
                    portfolios,
                    history,
                    portfolioAssetsByCurrencyValuationId,
                    existingValuationsKeys))
        .map(
            portfolios ->
                createPortfolioPositionHistoryValuation(
                    portfolios, history, portfolioAssetsByCurrencyValuationId));
  }

  private boolean isNotExistingValuation(
      List<Portfolio> portfolios,
      PortfolioPositionHistory history,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId,
      Set<PortfolioPositionHistoryValuation.PortfolioPositionHistoryValuationKey>
          existingValuationsKeys) {
    Portfolio firstPortfolio = portfolios.get(0);
    return !existingValuationsKeys.contains(
        new PortfolioPositionHistoryValuation.PortfolioPositionHistoryValuationKey(
            history.getId(),
            portfolioAssetsByCurrencyValuationId
                .get(firstPortfolio.currencyValuationId())
                .getId()));
  }

  private PortfolioPositionHistoryValuation createPortfolioPositionHistoryValuation(
      List<Portfolio> portfolios,
      PortfolioPositionHistory history,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId) {
    Portfolio firstPortfolio = portfolios.get(0);
    BigDecimal agreementQuantity =
        portfolios.stream()
            .filter(Portfolio::isConcertation)
            .findFirst()
            .map(Portfolio::getNetAmountCurrencyValuation)
            .orElse(BigDecimal.ZERO);
    BigDecimal settlementQuantity =
        portfolios.stream()
            .filter(Portfolio::isLiquidation)
            .findFirst()
            .map(Portfolio::getNetAmountCurrencyValuation)
            .orElse(BigDecimal.ZERO);
    PortfolioCurrencyValuation currencyValuation =
        portfolioAssetsByCurrencyValuationId.get(firstPortfolio.currencyValuationId());
    return new PortfolioPositionHistoryValuation(
        history, currencyValuation, agreementQuantity, settlementQuantity);
  }

  @Override
  public PortfolioPositionValuationRow upsertByPositionIdAndCurrencyValuationId(
      PortfolioPosition position,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal assetPriceInPesos,
      BigDecimal currencyPriceInPesos) {
    return repository
        .findByPortfolioPositionIdAndPortfolioCurrencyValuationId(
            position.getId(), currencyValuation.getId())
        .map(
            valuation ->
                updateValuation(position, assetPriceInPesos, currencyPriceInPesos, valuation))
        .orElseGet(
            () ->
                save(
                    new PortfolioPositionValuationRow(
                        position, currencyValuation, assetPriceInPesos, currencyPriceInPesos)));
  }

  @Override
  public List<PortfolioPositionHistoryValuation> findHistoryByPortfolioPositionIds(
      List<UUID> positionHistoryIds) {
    return historyRepository.findByPortfolioPositionHistoryIdIn(positionHistoryIds);
  }

  private PortfolioPositionValuationRow updateValuation(
      PortfolioPosition position,
      BigDecimal assetPrice,
      BigDecimal currencyPrice,
      PortfolioPositionValuationRow valuation) {
    log.info(
        "Updating valuation for position [{}] with assetPrice: [{}], currencyPrice: [{}]",
        position.getPortfolioAsset().getName(),
        assetPrice,
        currencyPrice);
    BigDecimal settlementQuantity =
        position
            .getSettlementQuantity()
            .multiply(assetPrice)
            .divide(currencyPrice, bigDecimalScale, RoundingMode.DOWN);
    log.info(
        "Calculated settlementQuantity: [{}] = (settlementQuantity: {} * assetPrice: {}) / currencyPrice: {}",
        settlementQuantity,
        position.getSettlementQuantity(),
        assetPrice,
        currencyPrice);
    BigDecimal agreementQuantity =
        position
            .getAgreementQuantity()
            .multiply(assetPrice)
            .divide(currencyPrice, bigDecimalScale, RoundingMode.DOWN);
    log.info(
        "Calculated agreementQuantity: [{}] = (agreementQuantity: {} * assetPrice: {}) / currencyPrice: {}",
        agreementQuantity,
        position.getAgreementQuantity(),
        assetPrice,
        currencyPrice);
    valuation.setSettlementQuantity(settlementQuantity);
    valuation.setAgreementQuantity(agreementQuantity);
    valuation.setLastUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    return save(valuation);
  }
}
