package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_position_history_valuations")
public class PortfolioPositionHistoryValuation extends BasePortfolioPositionValuation {

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_position_history_id", nullable = false)
  private PortfolioPositionHistory portfolioPositionHistory;

  @Column(name = "created_at", nullable = false)
  private LocalDate createdAt;

  public PortfolioPositionHistoryValuation() {
    super();
  }

  public PortfolioPositionHistoryValuation(
      PortfolioPositionHistory positionHistory,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal agreementQuantity,
      BigDecimal settlementQuantity) {
    super();
    this.portfolioPositionHistory = positionHistory;
    this.setPortfolioCurrencyValuation(currencyValuation);
    this.setAgreementQuantity(agreementQuantity);
    this.setSettlementQuantity(settlementQuantity);
    this.createdAt = BuenosAiresTime.nowAsLocalDate();
  }

  @Override
  public BasePortfolioPosition getBasePortfolioPosition() {
    return getPortfolioPositionHistory();
  }

  public PortfolioPositionHistoryValuationKey getHistoryValuationKey() {
    return new PortfolioPositionHistoryValuationKey(
        getPortfolioPositionHistory().getId(), getPortfolioCurrencyValuation().getId());
  }

  public record PortfolioPositionHistoryValuationKey(
      UUID portfolioPositionHistoryId, UUID portfolioCurrencyValuationId) {}
}
