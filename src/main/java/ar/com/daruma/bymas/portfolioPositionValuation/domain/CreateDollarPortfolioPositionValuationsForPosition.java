package ar.com.daruma.bymas.portfolioPositionValuation.domain;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreateDollarPortfolioPositionValuationsForPosition {

  private static final Logger logger =
      LogManager.getLogger(CreateDollarPortfolioPositionValuationsForPosition.class);

  public List<PortfolioPositionValuation> create(
      PortfolioPosition position,
      List<PortfolioCurrencyQuotation> dollarQuotations,
      BigDecimal assetPrice,
      BigDecimal assetCurrencyQuotationPrice) {
    List<PortfolioPositionValuation> positionValuations = new java.util.ArrayList<>(List.of());
    logger.info("Creating dollar valuations for position {}", position.getId());
    for (PortfolioCurrencyQuotation currencyQuotation : dollarQuotations) {
      logger.info(
          "Creating dollar valuation for position [{}]. AssetPrice: [{}], AssetCurrencyQuotationPrice: [{}]",
          position.getPortfolioAsset().getName(),
          assetPrice,
          assetCurrencyQuotationPrice);
      BigDecimal currencyPriceInPesos;
      if (position.sharesCurrencyWithQuotation(currencyQuotation)) {
        currencyPriceInPesos = assetCurrencyQuotationPrice;
      } else {
        currencyPriceInPesos = currencyQuotation.getQuotation().getPrice();
      }
      PortfolioPositionValuation valuation =
          new PortfolioPositionValuation(
              position,
              currencyQuotation.getValuation(),
              assetPrice.multiply(assetCurrencyQuotationPrice),
              currencyPriceInPesos);
      positionValuations.add(valuation);
    }
    logger.info(
        "Successfully created {} dollar valuations for position {}",
        dollarQuotations.size(),
        position.getId());
    return positionValuations;
  }
}
