package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public abstract class BasePortfolioPositionValuation {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_currency_valuation_id", nullable = false)
  private PortfolioCurrencyValuation portfolioCurrencyValuation;

  @Column(name = "agreement_quantity", nullable = false)
  private BigDecimal agreementQuantity;

  @Column(name = "settlement_quantity", nullable = false)
  private BigDecimal settlementQuantity;

  public abstract BasePortfolioPosition getBasePortfolioPosition();

  public boolean isPesoValuation() {
    return this.getPortfolioCurrencyValuation().getCode().equals("ARS");
  }
}
