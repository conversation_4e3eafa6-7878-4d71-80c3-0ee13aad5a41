package ar.com.daruma.bymas.portfolioPositionValuation.application.find;

import static ar.com.daruma.bymas.portfolioCurrencyValuation.domain.utils.PortfolioCurrencyValuationUtils.*;

import ar.com.daruma.bymas.portfolioCurrencyValuation.application.findsert.FindsertPortfolioCurrencyValuationByNameAndCode;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestDollarPortfolioSellQuotations;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindPortfolioPositionValuationsForPositions {

  private final FindLatestDollarPortfolioSellQuotations findLatestDollarPortfolioSellQuotations;
  private final FindsertPortfolioCurrencyValuationByNameAndCode
      findsertPortfolioCurrencyValuationByNameAndCode;
  private final FindPortfolioPositionValuationsForPosition
      findPortfolioPositionValuationsForPosition;

  private static final Logger logger =
      LogManager.getLogger(FindPortfolioPositionValuationsForPositions.class);

  public List<PortfolioPositionValuation> find(List<PortfolioPosition> positions) {
    logger.info("Processing {} portfolio positions for valuations", positions.size());
    List<PortfolioCurrencyQuotation> dollarQuotations =
        findLatestDollarPortfolioSellQuotations.find();
    PortfolioCurrencyValuation pesoBnaCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(pesosBnaName, pesosBnaCode);
    PortfolioCurrencyValuation pesoImplicitCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(
            pesosImplicitName, pesosImplicitCode);
    logger.info(
        "Found {} dollar quotations and retrieved peso currency valuations",
        dollarQuotations.size());
    List<PortfolioPositionValuation> allValuations = new ArrayList<>();
    for (PortfolioPosition position : positions) {
      List<PortfolioPositionValuation> positionValuations =
          findPortfolioPositionValuationsForPosition.find(
              position, dollarQuotations, pesoBnaCurrencyValuation, pesoImplicitCurrencyValuation);
      allValuations.addAll(positionValuations);
    }
    logger.info(
        "Successfully processed {} total portfolio position valuations for {} positions",
        allValuations.size(),
        positions.size());
    return allValuations;
  }
}
