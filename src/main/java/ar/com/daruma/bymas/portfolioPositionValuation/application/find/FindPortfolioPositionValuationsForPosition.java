package ar.com.daruma.bymas.portfolioPositionValuation.application.find;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarOfficialBnaAbbreviation;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType.BUY;

import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByAbbreviation;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.CreateDollarPortfolioPositionValuationsForPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.CreatePesoPortfolioPositionValuationsForPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestPortfolioQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.errors.NoPortfolioQuotationFoundForAsset;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindPortfolioPositionValuationsForPosition {

  private final FindLatestPortfolioQuotation findLatestPortfolioQuotation;

  private final FindPortfolioAssetByAbbreviation findPortfolioAssetByAbbreviation;

  private final CreateDollarPortfolioPositionValuationsForPosition
      createDollarPortfolioPositionValuationsForPosition;

  private final CreatePesoPortfolioPositionValuationsForPosition
      createPesoPortfolioPositionValuationsForPosition;

  private static final Logger logger =
      LogManager.getLogger(FindPortfolioPositionValuationsForPosition.class);

  public List<PortfolioPositionValuation> find(
      PortfolioPosition position,
      List<PortfolioCurrencyQuotation> dollarQuotations,
      PortfolioCurrencyValuation pesoBnaCurrencyValuation,
      PortfolioCurrencyValuation pesoImplicitCurrencyValuation) {
    logger.info("Processing portfolio position {} valuations", position.getId());
    PortfolioAsset asset = position.getPortfolioAsset();
    BigDecimal assetPrice = getAssetPrice(asset);
    PortfolioAsset portfolioCurrencyAsset =
        findPortfolioAssetByAbbreviation.find(asset.getCurrency());
    AssetCurrencyPriceResult currencyPriceResult =
        getAssetCurrencyQuotationPrice(portfolioCurrencyAsset);
    BigDecimal assetCurrencyQuotationPrice = currencyPriceResult.price();
    Optional<BigDecimal> officialAssetPrice = currencyPriceResult.officialAssetPrice();
    List<PortfolioPositionValuation> positionValuations =
        createPositionValuations(
            position,
            dollarQuotations,
            pesoBnaCurrencyValuation,
            pesoImplicitCurrencyValuation,
            assetPrice,
            assetCurrencyQuotationPrice,
            officialAssetPrice);
    logger.info(
        "Successfully created {} valuations for position {}",
        positionValuations.size(),
        position.getId());
    return positionValuations;
  }

  private BigDecimal getAssetPrice(PortfolioAsset asset) {
    if (asset.isCurrencyAsset()) {
      return BigDecimal.ONE;
    }
    return findAssetQuotationPrice(asset);
  }

  private BigDecimal findAssetQuotationPrice(PortfolioAsset asset) {
    PortfolioQuotation assetQuotation =
        findLatestPortfolioQuotation
            .findByAssetAndQuotationType(asset, BUY)
            .orElseGet(
                () ->
                    new PortfolioQuotation(
                        asset,
                        BigDecimal.ZERO,
                        MARKET_DATA,
                        BUY,
                        BuenosAiresTime.nowAsLocalDateTime()));
    return assetQuotation.getPrice();
  }

  private AssetCurrencyPriceResult getAssetCurrencyQuotationPrice(
      PortfolioAsset portfolioCurrencyAsset) {
    if (portfolioCurrencyAsset.isPesoCurrency()) {
      return new AssetCurrencyPriceResult(BigDecimal.ONE, Optional.empty());
    }
    return handleNonPesoCurrency(portfolioCurrencyAsset);
  }

  private AssetCurrencyPriceResult handleNonPesoCurrency(PortfolioAsset portfolioCurrencyAsset) {
    Optional<BigDecimal> officialAssetPrice = Optional.empty();

    if (portfolioCurrencyAsset.isDollarCurrency()) {
      officialAssetPrice = getOfficialUSDPrice();
    }

    BigDecimal assetCurrencyQuotationPrice = findCurrencyQuotationPrice(portfolioCurrencyAsset);
    return new AssetCurrencyPriceResult(assetCurrencyQuotationPrice, officialAssetPrice);
  }

  private Optional<BigDecimal> getOfficialUSDPrice() {
    PortfolioAsset officialUSDAsset =
        findPortfolioAssetByAbbreviation.find(dolarOfficialBnaAbbreviation);
    BigDecimal officialPrice =
        findLatestPortfolioQuotation
            .findByAssetAndQuotationType(officialUSDAsset, QuotationType.SELL)
            .orElseThrow(
                () ->
                    new NoPortfolioQuotationFoundForAsset(
                        officialUSDAsset.getName(),
                        officialUSDAsset.getAbbreviation(),
                        officialUSDAsset.getCurrency()))
            .getPrice();
    return Optional.of(officialPrice);
  }

  private BigDecimal findCurrencyQuotationPrice(PortfolioAsset portfolioCurrencyAsset) {
    return findLatestPortfolioQuotation
        .findByAssetAndQuotationType(portfolioCurrencyAsset, QuotationType.SELL)
        .orElseThrow(
            () ->
                new NoPortfolioQuotationFoundForAsset(
                    portfolioCurrencyAsset.getName(),
                    portfolioCurrencyAsset.getAbbreviation(),
                    portfolioCurrencyAsset.getCurrency()))
        .getPrice();
  }

  private List<PortfolioPositionValuation> createPositionValuations(
      PortfolioPosition position,
      List<PortfolioCurrencyQuotation> dollarQuotations,
      PortfolioCurrencyValuation pesoBnaCurrencyValuation,
      PortfolioCurrencyValuation pesoImplicitCurrencyValuation,
      BigDecimal assetPrice,
      BigDecimal assetCurrencyQuotationPrice,
      Optional<BigDecimal> officialAssetPrice) {
    List<PortfolioPositionValuation> positionValuations =
        new ArrayList<>(
            createDollarPortfolioPositionValuationsForPosition.create(
                position, dollarQuotations, assetPrice, assetCurrencyQuotationPrice));
    positionValuations.addAll(
        createPesoPortfolioPositionValuationsForPosition.create(
            position,
            pesoBnaCurrencyValuation,
            pesoImplicitCurrencyValuation,
            assetPrice,
            officialAssetPrice,
            assetCurrencyQuotationPrice));
    return positionValuations;
  }

  public record AssetCurrencyPriceResult(
      BigDecimal price, Optional<BigDecimal> officialAssetPrice) {}
}
