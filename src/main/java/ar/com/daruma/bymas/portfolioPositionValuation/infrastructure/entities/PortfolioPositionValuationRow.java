package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_position_valuations")
public class PortfolioPositionValuationRow extends BasePortfolioPositionValuation {

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_position_id", nullable = false)
  private PortfolioPosition portfolioPosition;

  @Column(name = "created_at", nullable = false)
  private LocalDateTime createdAt;

  @Column(name = "last_updated_at", nullable = false)
  private LocalDateTime lastUpdatedAt;

  public PortfolioPositionValuationRow() {
    super();
  }

  public PortfolioPositionValuationRow(
      PortfolioPosition position,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal assetPriceInPesos,
      BigDecimal currencyPriceInPesos) {
    super();
    this.setPortfolioPosition(position);
    this.setPortfolioCurrencyValuation(currencyValuation);

    BigDecimal settlementQuantity =
        position
            .getSettlementQuantity()
            .multiply(assetPriceInPesos)
            .divide(currencyPriceInPesos, bigDecimalScale, RoundingMode.HALF_DOWN);
    BigDecimal agreementQuantity =
        position
            .getAgreementQuantity()
            .multiply(assetPriceInPesos)
            .divide(currencyPriceInPesos, bigDecimalScale, RoundingMode.HALF_DOWN);
    this.setSettlementQuantity(settlementQuantity);
    this.setAgreementQuantity(agreementQuantity);

    this.createdAt = BuenosAiresTime.nowAsLocalDateTime();
    this.lastUpdatedAt = BuenosAiresTime.nowAsLocalDateTime();
  }

  @Override
  public BasePortfolioPosition getBasePortfolioPosition() {
    return getPortfolioPosition();
  }
}
