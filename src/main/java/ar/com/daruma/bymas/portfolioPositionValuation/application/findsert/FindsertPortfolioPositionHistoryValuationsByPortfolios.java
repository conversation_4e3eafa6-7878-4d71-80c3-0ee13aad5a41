package ar.com.daruma.bymas.portfolioPositionValuation.application.findsert;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.PortfolioPositionValuationService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindsertPortfolioPositionHistoryValuationsByPortfolios {

  private final PortfolioPositionValuationService portfolioPositionValuationService;

  public void findsert(
      List<PortfolioPositionHistory> histories,
      Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryK<PERSON>,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId) {
    log.info("Finding or inserting portfolio position history valuations from portfolios");
    portfolioPositionValuationService.findsertPortfolioPositionHistoryValuationsByPortfolios(
        histories, portfolioGroupsByHistoryKey, portfolioAssetsByCurrencyValuationId);
    log.info("Found or inserted portfolio position history valuations from portfolios");
  }
}
