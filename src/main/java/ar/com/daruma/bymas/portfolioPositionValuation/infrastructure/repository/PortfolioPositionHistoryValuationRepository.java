package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.repository;

import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioPositionHistoryValuationRepository
    extends JpaRepository<PortfolioPositionHistoryValuation, UUID> {
  List<PortfolioPositionHistoryValuation> findByPortfolioPositionHistoryId(UUID positionHistoryId);

  List<PortfolioPositionHistoryValuation> findByPortfolioCurrencyValuationId(
      UUID currencyValuationId);

  Optional<PortfolioPositionHistoryValuation>
      findByPortfolioPositionHistoryIdAndPortfolioCurrencyValuationId(
          UUID portfolioPositionHistory_id, UUID portfolioCurrencyValuation_id);

  List<PortfolioPositionHistoryValuation> findByPortfolioPositionHistoryIdIn(
      List<UUID> positionHistoryIds);

  @Query(
      value =
          "SELECT distinct pphv FROM PortfolioPositionHistoryValuation pphv WHERE "
              + "CONCAT(pphv.portfolioPositionHistory.id, '|', pphv.portfolioCurrencyValuation.id) IN :searchKeys")
  List<PortfolioPositionHistoryValuation> findByMultipleCriteria(List<String> searchKeys);

  Set<PortfolioPositionHistoryValuation>
      findDistinctByPortfolioPositionHistoryIdInAndPortfolioCurrencyValuationIdIn(
          Set<UUID> positionHistoryIds, Set<UUID> currencyValuationIds);
}
