package ar.com.daruma.bymas.portfolioPositionValuation.application.find;

import ar.com.daruma.bymas.portfolioPositionValuation.domain.PortfolioPositionValuationService;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;
import java.util.List;
import java.util.UUID;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindPortfolioPositionHistoryValuations {

  @Autowired private PortfolioPositionValuationService portfolioPositionValuationService;

  private static final Logger logger =
      LogManager.getLogger(FindPortfolioPositionHistoryValuations.class);

  public List<PortfolioPositionHistoryValuation> byPositionHistoryId(UUID positionHistoryId) {
    logger.info(
        "Finding portfolio position history valuations with positionHistoryId: {}",
        positionHistoryId);
    List<PortfolioPositionHistoryValuation> valuations =
        portfolioPositionValuationService.findHistoryByPortfolioPositionId(positionHistoryId);
    logger.info(
        "{} valuations found for Portfolio position history {}",
        valuations.size(),
        positionHistoryId);
    return valuations;
  }

  public List<PortfolioPositionHistoryValuation> byPositionHistoryIds(
      List<UUID> positionHistoryIds) {
    logger.info(
        "Finding portfolio position history valuations for positionHistoryIds: {}",
        positionHistoryIds);
    List<PortfolioPositionHistoryValuation> valuations =
        portfolioPositionValuationService.findHistoryByPortfolioPositionIds(positionHistoryIds);
    logger.info(
        "{} valuations found for Portfolio position histories {}",
        valuations.size(),
        positionHistoryIds);
    return valuations;
  }
}
