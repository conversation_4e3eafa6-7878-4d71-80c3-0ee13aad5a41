package ar.com.daruma.bymas.portfolioPositionValuation.application.find;

import static ar.com.daruma.bymas.portfolioCurrencyValuation.domain.utils.PortfolioCurrencyValuationUtils.*;

import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByAbbreviation;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.application.findsert.FindsertPortfolioCurrencyValuationByNameAndCode;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.utils.AssetCurrencyPriceResult;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.utils.FindPortfolioPositionValuationsUtils;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestDollarPortfolioSellQuotations;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestPortfolioQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioPositionValuations {

  private final FindLatestDollarPortfolioSellQuotations findLatestDollarPortfolioSellQuotations;
  private final FindsertPortfolioCurrencyValuationByNameAndCode
      findsertPortfolioCurrencyValuationByNameAndCode;
  private final FindPortfolioPositionValuationsForPosition
      findPortfolioPositionValuationsForPosition;
  private final FindLatestPortfolioQuotation findLatestPortfolioQuotation;
  private final FindPortfolioAssetByAbbreviation findPortfolioAssetByAbbreviation;
  private final FindPortfolioPositionValuationsUtils findPortfolioPositionValuationsUtils;

  public List<PortfolioPositionValuation> findForPositions(List<PortfolioPosition> positions) {
    log.info("Processing {} portfolio positions for valuations", positions.size());
    List<PortfolioCurrencyQuotation> dollarQuotations =
        findLatestDollarPortfolioSellQuotations.find();
    PortfolioCurrencyValuation pesoBnaCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(pesosBnaName, pesosBnaCode);
    PortfolioCurrencyValuation pesoImplicitCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(
            pesosImplicitName, pesosImplicitCode);
    log.info(
        "Found {} dollar quotations and retrieved peso currency valuations",
        dollarQuotations.size());
    List<PortfolioPositionValuation> allValuations = new ArrayList<>();
    List<PortfolioQuotation> latestQuotations =
        findLatestPortfolioQuotation.findByAssetsAndQuotationType(
            positions.stream().map(PortfolioPosition::getPortfolioAsset).toList(),
            QuotationType.BUY);
    List<String> portfolioAssetCurrencyNames =
        positions.stream()
            .map(PortfolioPosition::getPortfolioAsset)
            .map(PortfolioAsset::getCurrency)
            .distinct()
            .toList();
    List<PortfolioAsset> portfolioCurrencyAssets =
        findPortfolioAssetByAbbreviation.findMultiple(portfolioAssetCurrencyNames);
    List<PortfolioQuotation> currencyQuotations =
        findLatestPortfolioQuotation.findByAssetsAndQuotationType(
            portfolioCurrencyAssets, QuotationType.BUY);
    Map<UUID, PortfolioQuotation> quotationsByAssetId =
        latestQuotations.stream()
            .collect(
                Collectors.toMap(PortfolioQuotation::getPortfolioAssetId, quotation -> quotation));
    Map<UUID, PortfolioQuotation> currencyQuotationsByAssetId =
        currencyQuotations.stream()
            .collect(
                Collectors.toMap(PortfolioQuotation::getPortfolioAssetId, quotation -> quotation));
    Optional<BigDecimal> officialUSDPrice =
        findPortfolioPositionValuationsUtils.getOfficialUSDPrice();
    List<AssetCurrencyPriceResult> currencyPriceResults =
        portfolioCurrencyAssets.stream()
            .map(
                currencyAsset ->
                    findPortfolioPositionValuationsUtils.getAssetCurrencyQuotationPrice(
                        currencyAsset, currencyQuotationsByAssetId, officialUSDPrice))
            .toList();
    for (PortfolioPosition position : positions) {
      List<PortfolioPositionValuation> positionValuations =
          findPortfolioPositionValuationsForPosition.find(
              position, dollarQuotations, pesoBnaCurrencyValuation, pesoImplicitCurrencyValuation);
      allValuations.addAll(positionValuations);
    }
    log.info(
        "Successfully processed {} total portfolio position valuations for {} positions",
        allValuations.size(),
        positions.size());
    return allValuations;
  }
}
