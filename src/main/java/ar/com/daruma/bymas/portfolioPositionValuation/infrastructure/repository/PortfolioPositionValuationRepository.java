package ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.repository;

import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionValuationRow;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioPositionValuationRepository
    extends JpaRepository<PortfolioPositionValuationRow, UUID> {
  List<PortfolioPositionValuationRow> findByPortfolioPositionId(UUID positionId);

  List<PortfolioPositionValuationRow> findByPortfolioCurrencyValuationId(UUID currencyValuationId);

  Optional<PortfolioPositionValuationRow> findByPortfolioPositionIdAndPortfolioCurrencyValuationId(
      UUID portfolioPositionId, UUID currencyValuationId);
}
