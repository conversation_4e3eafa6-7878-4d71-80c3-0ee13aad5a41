package ar.com.daruma.bymas.portfolioPositionValuation.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionHistoryValuation;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionValuationRow;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

@Service
public interface PortfolioPositionValuationService {

  PortfolioPositionValuationRow save(PortfolioPositionValuationRow positionValuation);

  PortfolioPositionHistoryValuation save(
      PortfolioPositionHistoryValuation positionHistoryValuation);

  Optional<PortfolioPositionValuationRow> findById(UUID id);

  Optional<PortfolioPositionHistoryValuation> findHistoryById(UUID id);

  List<PortfolioPositionValuationRow> findByPortfolioPositionId(UUID positionId);

  List<PortfolioPositionHistoryValuation> findHistoryByPortfolioPositionId(UUID positionId);

  List<PortfolioPositionValuationRow> findByPortfolioCurrencyValuationId(UUID currencyValuationId);

  List<PortfolioPositionHistoryValuation> findHistoryByPortfolioCurrencyValuationId(
      UUID currencyValuationId);

  void findsertPortfolioPositionHistoryValuationsByPortfolios(
      List<PortfolioPositionHistory> histories,
      Map<PortfolioPositionHistory.HistoryKey, List<List<Portfolio>>> portfolioGroupsByHistoryKey,
      Map<Integer, PortfolioCurrencyValuation> portfolioAssetsByCurrencyValuationId);

  PortfolioPositionValuationRow upsertByPositionIdAndCurrencyValuationId(
      PortfolioPosition position,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal assetPriceInPesos,
      BigDecimal currencyPriceInPesos);

  List<PortfolioPositionHistoryValuation> findHistoryByPortfolioPositionIds(
      List<UUID> positionHistoryIds);
}
