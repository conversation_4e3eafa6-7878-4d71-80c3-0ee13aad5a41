package ar.com.daruma.bymas.portfolioPositionValuation.application.update;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.application.sync.SyncPortfolioHistoryPositionsFromAllariaIntegrations;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.PortfolioPositionValuationService;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionValuationRow;
import java.math.BigDecimal;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpsertPortfolioPositionValuationByCurrencyValuationAndPosition {

  @Autowired private PortfolioPositionValuationService portfolioPositionValuationService;

  private static final Logger logger =
      LogManager.getLogger(SyncPortfolioHistoryPositionsFromAllariaIntegrations.class);

  public PortfolioPositionValuationRow upsert(
      PortfolioPosition position,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal assetPriceInPesos,
      BigDecimal currencyPriceInPesos) {
    logger.info(
        "Upserting PortfolioPosition Valuation with position: {} and currencyValuation: {}",
        position.getId(),
        currencyValuation.getId());
    PortfolioPositionValuationRow valuation =
        portfolioPositionValuationService.upsertByPositionIdAndCurrencyValuationId(
            position, currencyValuation, assetPriceInPesos, currencyPriceInPesos);
    logger.info(
        "Portfolio position history valuation with id {} found or inserted", valuation.getId());
    return valuation;
  }
}
