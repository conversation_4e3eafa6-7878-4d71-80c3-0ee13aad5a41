package ar.com.daruma.bymas.portfolioPositionValuation.domain;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.domain.entities.PortfolioPositionValuation;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CreatePesoPortfolioPositionValuationsForPosition {

  private static final Logger logger =
      LogManager.getLogger(CreatePesoPortfolioPositionValuationsForPosition.class);

  public List<PortfolioPositionValuation> create(
      PortfolioPosition position,
      PortfolioCurrencyValuation pesoBnaCurrencyValuation,
      PortfolioCurrencyValuation pesoImplicitCurrencyValuation,
      BigDecimal assetPrice,
      Optional<BigDecimal> officialDollarQuotationPrice,
      BigDecimal assetCurrencyQuotationPrice) {
    List<PortfolioPositionValuation> positionValuations = new java.util.ArrayList<>(List.of());
    logger.info("Creating peso valuations for position {}", position.getId());
    PortfolioPositionValuation bnaValuation =
        new PortfolioPositionValuation(
            position,
            pesoBnaCurrencyValuation,
            assetPrice.multiply(officialDollarQuotationPrice.orElse(assetCurrencyQuotationPrice)),
            BigDecimal.ONE);
    positionValuations.add(bnaValuation);
    PortfolioPositionValuation implicitValuation =
        new PortfolioPositionValuation(
            position,
            pesoImplicitCurrencyValuation,
            assetPrice.multiply(assetCurrencyQuotationPrice),
            BigDecimal.ONE);
    positionValuations.add(implicitValuation);
    logger.info("Successfully created peso valuations for position {}", position.getId());
    return positionValuations;
  }
}
