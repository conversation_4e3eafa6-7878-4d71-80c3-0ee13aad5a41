package ar.com.daruma.bymas.portfolioPositionValuation.domain.utils;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarOfficialBnaAbbreviation;

import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByAbbreviation;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestPortfolioQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.errors.NoPortfolioQuotationFoundForAsset;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioPositionValuationsUtils {

  private final FindLatestPortfolioQuotation findLatestPortfolioQuotation;
  private final FindPortfolioAssetByAbbreviation findPortfolioAssetByAbbreviation;

  public AssetCurrencyPriceResult getAssetCurrencyQuotationPrice(
      PortfolioAsset portfolioCurrencyAsset,
      Map<UUID, PortfolioQuotation> currencyQuotationsByAssetId,
      Optional<BigDecimal> officialUSDPrice) {
    if (portfolioCurrencyAsset.isPesoCurrency()) {
      return new AssetCurrencyPriceResult(BigDecimal.ONE, Optional.empty());
    }
    return handleNonPesoCurrency(
        portfolioCurrencyAsset, currencyQuotationsByAssetId, officialUSDPrice);
  }

  public AssetCurrencyPriceResult handleNonPesoCurrency(
      PortfolioAsset portfolioCurrencyAsset,
      Map<UUID, PortfolioQuotation> currencyQuotationsByAssetId,
      Optional<BigDecimal> officialUSDPrice) {
    Optional<BigDecimal> officialAssetPrice = Optional.empty();

    if (portfolioCurrencyAsset.isDollarCurrency()) {
      officialAssetPrice = officialUSDPrice;
    }

    BigDecimal assetCurrencyQuotationPrice =
        findCurrencyQuotationPrice(portfolioCurrencyAsset, currencyQuotationsByAssetId);
    return new AssetCurrencyPriceResult(assetCurrencyQuotationPrice, officialAssetPrice);
  }

  public Optional<BigDecimal> getOfficialUSDPrice() {
    PortfolioAsset officialUSDAsset =
        findPortfolioAssetByAbbreviation.find(dolarOfficialBnaAbbreviation);
    BigDecimal officialPrice =
        findLatestPortfolioQuotation
            .findByAssetAndQuotationType(officialUSDAsset, QuotationType.SELL)
            .orElseThrow(
                () ->
                    new NoPortfolioQuotationFoundForAsset(
                        officialUSDAsset.getName(),
                        officialUSDAsset.getAbbreviation(),
                        officialUSDAsset.getCurrency()))
            .getPrice();
    return Optional.of(officialPrice);
  }

  public BigDecimal findCurrencyQuotationPrice(
      PortfolioAsset portfolioCurrencyAsset,
      Map<UUID, PortfolioQuotation> currencyQuotationsByAssetId) {
    return Optional.ofNullable(
            currencyQuotationsByAssetId.getOrDefault(portfolioCurrencyAsset.getId(), null))
        .orElseThrow(
            () ->
                new NoPortfolioQuotationFoundForAsset(
                    portfolioCurrencyAsset.getName(),
                    portfolioCurrencyAsset.getAbbreviation(),
                    portfolioCurrencyAsset.getCurrency()))
        .getPrice();
  }
}
