package ar.com.daruma.bymas.portfolioPositionValuation.application.find;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FindBasePortfolioPositionValuationsForBasePositions {

  private final FindPortfolioPositionHistoryValuations findPortfolioPositionHistoryValuations;
  private final FindPortfolioPositionValuationsForPositions
      findPortfolioPositionValuationsForPositions;
  private static final Logger logger =
      LogManager.getLogger(FindBasePortfolioPositionValuationsForBasePositions.class);

  public List<BasePortfolioPositionValuation> find(List<BasePortfolioPosition> positions) {
    boolean isHistory = positions.stream().anyMatch(BasePortfolioPosition::isHistory);
    String historyLog = isHistory ? "history" : "current";
    logger.info("Finding {} portfolio position {} Valuations", positions.size(), historyLog);
    List<BasePortfolioPositionValuation> valuations = new java.util.ArrayList<>(List.of());
    if (isHistory) {
      valuations.addAll(
          findPortfolioPositionHistoryValuations.byPositionHistoryIds(
              positions.stream().map(BasePortfolioPosition::getId).toList()));
    } else {
      valuations.addAll(
          findPortfolioPositionValuationsForPositions.find(
              positions.stream().map(position -> (PortfolioPosition) position).toList()));
    }
    return valuations;
  }
}
