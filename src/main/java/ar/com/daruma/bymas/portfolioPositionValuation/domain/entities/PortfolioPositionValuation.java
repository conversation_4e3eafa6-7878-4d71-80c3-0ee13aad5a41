package ar.com.daruma.bymas.portfolioPositionValuation.domain.entities;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.BasePortfolioPosition;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.BasePortfolioPositionValuation;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PortfolioPositionValuation extends BasePortfolioPositionValuation {

  private PortfolioPosition portfolioPosition;

  public PortfolioPositionValuation(
      PortfolioPosition position,
      PortfolioCurrencyValuation currencyValuation,
      BigDecimal assetPriceInPesos,
      BigDecimal currencyPriceInPesos) {
    super();
    this.setPortfolioPosition(position);
    this.setPortfolioCurrencyValuation(currencyValuation);
    BigDecimal settlementQuantity =
        position
            .getSettlementQuantity()
            .multiply(assetPriceInPesos)
            .divide(currencyPriceInPesos, bigDecimalScale, RoundingMode.HALF_DOWN);
    BigDecimal agreementQuantity =
        position
            .getAgreementQuantity()
            .multiply(assetPriceInPesos)
            .divide(currencyPriceInPesos, bigDecimalScale, RoundingMode.HALF_DOWN);
    this.setSettlementQuantity(settlementQuantity);
    this.setAgreementQuantity(agreementQuantity);
  }

  @Override
  public BasePortfolioPosition getBasePortfolioPosition() {
    return getPortfolioPosition();
  }
}
