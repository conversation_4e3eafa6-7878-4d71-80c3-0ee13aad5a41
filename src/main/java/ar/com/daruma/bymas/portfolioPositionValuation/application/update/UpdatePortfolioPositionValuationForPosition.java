package ar.com.daruma.bymas.portfolioPositionValuation.application.update;

import static ar.com.daruma.bymas.portfolioCurrencyValuation.domain.utils.PortfolioCurrencyValuationUtils.*;
import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarOfficialBnaAbbreviation;
import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.pesoName;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationType.BUY;

import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByAbbreviation;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.application.findsert.FindsertPortfolioCurrencyValuationByNameAndCode;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.portfolioPositionValuation.infrastructure.entities.PortfolioPositionValuationRow;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestDollarPortfolioSellQuotations;
import ar.com.daruma.bymas.portfolioQuotation.application.find.FindLatestPortfolioQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.PortfolioCurrencyQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.errors.NoPortfolioQuotationFoundForAsset;
import ar.com.daruma.bymas.portfolioQuotation.infrastructure.entities.PortfolioQuotation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UpdatePortfolioPositionValuationForPosition {

  @Autowired
  private UpsertPortfolioPositionValuationByCurrencyValuationAndPosition
      upsertPortfolioPositionValuationByCurrencyValuationAndPosition;

  @Autowired
  private FindLatestDollarPortfolioSellQuotations findLatestDollarPortfolioSellQuotations;

  @Autowired private FindLatestPortfolioQuotation findLatestPortfolioQuotation;

  @Autowired private FindPortfolioAssetByAbbreviation findPortfolioAssetByAbbreviation;

  @Autowired
  private FindsertPortfolioCurrencyValuationByNameAndCode
      findsertPortfolioCurrencyValuationByNameAndCode;

  private static final Logger logger =
      LogManager.getLogger(UpdatePortfolioPositionValuationForPosition.class);

  public List<PortfolioPositionValuationRow> update(PortfolioPosition position) {

    logger.info("Updating portfolio position {} Valuations", position.getId());

    PortfolioAsset asset = position.getPortfolioAsset();
    boolean assetNameIsPeso = position.getPortfolioAsset().getName().equals(pesoName);
    BigDecimal assetPrice = BigDecimal.ONE;
    Optional<BigDecimal> officialUSDAssetPrice = Optional.empty();
    if (!assetNameIsPeso) {
      assetPrice =
          findLatestPortfolioQuotation
              .findByAssetAndQuotationType(asset, BUY)
              .map(PortfolioQuotation::getPrice)
              .orElse(BigDecimal.ZERO);
    }

    PortfolioAsset portfolioCurrencyAsset =
        findPortfolioAssetByAbbreviation.find(position.getPortfolioAsset().getCurrency());
    BigDecimal assetCurrencyQuotationPrice;
    if (portfolioCurrencyAsset.isPesoCurrency()) {
      assetCurrencyQuotationPrice = BigDecimal.ONE;
    } else {
      if (portfolioCurrencyAsset.isDollarCurrency()) {
        PortfolioAsset officialUSDAsset =
            findPortfolioAssetByAbbreviation.find(dolarOfficialBnaAbbreviation);
        officialUSDAssetPrice =
            Optional.of(
                findLatestPortfolioQuotation
                    .findByAssetAndQuotationType(officialUSDAsset, BUY)
                    .orElseThrow(
                        () ->
                            new NoPortfolioQuotationFoundForAsset(
                                officialUSDAsset.getName(),
                                officialUSDAsset.getAbbreviation(),
                                officialUSDAsset.getCurrency()))
                    .getPrice());
      }
      assetCurrencyQuotationPrice =
          findLatestPortfolioQuotation
              .findByAssetAndQuotationType(portfolioCurrencyAsset, BUY)
              .orElseThrow(
                  () ->
                      new NoPortfolioQuotationFoundForAsset(
                          portfolioCurrencyAsset.getName(),
                          portfolioCurrencyAsset.getAbbreviation(),
                          portfolioCurrencyAsset.getCurrency()))
              .getPrice();
    }
    List<PortfolioPositionValuationRow> updatedValuations = new ArrayList<>();
    updateDolarValuations(position, assetPrice, assetCurrencyQuotationPrice, updatedValuations);
    updatePesoValuation(
        position,
        assetPrice,
        officialUSDAssetPrice,
        assetCurrencyQuotationPrice,
        updatedValuations);
    logger.info(
        "Successfully updated {} portfolio position valuations for position {}",
        updatedValuations.size(),
        position.getId());
    return updatedValuations;
  }

  public void updateDolarValuations(
      PortfolioPosition position,
      BigDecimal assetPrice,
      BigDecimal assetCurrencyQuotationPrice,
      List<PortfolioPositionValuationRow> updatedValuations) {
    logger.info("Updating dollar valuations for position {}", position.getId());
    List<PortfolioCurrencyQuotation> currencyQuotations =
        findLatestDollarPortfolioSellQuotations.find();
    updatedValuations.addAll(
        currencyQuotations.stream()
            .map(
                currencyQuotation -> {
                  logger.info(
                      "Updating dolar valuation for position [{}]. AssetPrice: [{}], AssetCurrencyQuotationPrice: [{}], assetPriceInPesos: [{}]",
                      position.getPortfolioAsset().getName(),
                      assetPrice,
                      assetCurrencyQuotationPrice,
                      assetPrice.multiply(assetCurrencyQuotationPrice));
                  if (position.sharesCurrencyWithQuotation(currencyQuotation)) {
                    return upsertPortfolioPositionValuationByCurrencyValuationAndPosition.upsert(
                        position,
                        currencyQuotation.getValuation(),
                        assetPrice.multiply(assetCurrencyQuotationPrice),
                        assetCurrencyQuotationPrice);
                  }
                  return upsertPortfolioPositionValuationByCurrencyValuationAndPosition.upsert(
                      position,
                      currencyQuotation.getValuation(),
                      assetPrice.multiply(assetCurrencyQuotationPrice),
                      currencyQuotation.getQuotation().getPrice());
                })
            .filter(Objects::nonNull)
            .toList());
    logger.info(
        "Successfully updated {} dollar portfolio position valuations for position {}",
        updatedValuations.size(),
        position.getId());
  }

  public void updatePesoValuation(
      PortfolioPosition position,
      BigDecimal assetPrice,
      Optional<BigDecimal> officialDollarQuotationPrice,
      BigDecimal assetCurrencyQuotationPrice,
      List<PortfolioPositionValuationRow> updatedValuations) {
    logger.info("Updating peso valuations for position {}", position.getId());
    PortfolioCurrencyValuation pesoBnaCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(pesosBnaName, pesosBnaCode);
    PortfolioCurrencyValuation pesoImplicitCurrencyValuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(
            pesosImplicitName, pesosImplicitCode);
    updatedValuations.add(
        upsertPortfolioPositionValuationByCurrencyValuationAndPosition.upsert(
            position,
            pesoBnaCurrencyValuation,
            assetPrice.multiply(officialDollarQuotationPrice.orElse(assetCurrencyQuotationPrice)),
            BigDecimal.ONE));
    updatedValuations.add(
        upsertPortfolioPositionValuationByCurrencyValuationAndPosition.upsert(
            position,
            pesoImplicitCurrencyValuation,
            assetPrice.multiply(assetCurrencyQuotationPrice),
            BigDecimal.ONE));
    logger.info(
        "Successfully updated peso portfolio position valuation for position {}", position.getId());
  }
}
