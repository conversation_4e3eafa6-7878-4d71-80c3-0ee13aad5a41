package ar.com.daruma.bymas.portfolioReturn.infrastructure.repository;

import ar.com.daruma.bymas.portfolioReturn.infrastructure.entities.PortfolioReturn;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioReturnRepository extends JpaRepository<PortfolioReturn, UUID> {
  List<PortfolioReturn> findByPortfolioAccountId(UUID accountId);

  List<PortfolioReturn> findByPortfolioAssetId(UUID assetId);

  List<PortfolioReturn> findByDateBetween(LocalDate startDate, LocalDate endDate);
}
