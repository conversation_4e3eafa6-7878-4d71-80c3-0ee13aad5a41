package ar.com.daruma.bymas.portfolioReturn.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class Returns<T> {

  private String id;
  private String asset;
  private double quantity;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate date;

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
  private LocalDateTime lastUpdatedAt;

  private T metadata;

  public Returns() {}

  public Returns(
      String id,
      String asset,
      double quantity,
      LocalDate date,
      LocalDateTime lastUpdatedAt,
      T metadata) {
    this.id = id;
    this.asset = asset;
    this.quantity = quantity;
    this.date = date;
    this.lastUpdatedAt = lastUpdatedAt;
    this.metadata = metadata;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getAsset() {
    return asset;
  }

  public void setAsset(String asset) {
    this.asset = asset;
  }

  public double getQuantity() {
    return quantity;
  }

  public void setQuantity(double quantity) {
    this.quantity = quantity;
  }

  public LocalDate getDate() {
    return date;
  }

  public void setDate(LocalDate date) {
    this.date = date;
  }

  public LocalDateTime getLastUpdatedAt() {
    return lastUpdatedAt;
  }

  public void setLastUpdatedAt(LocalDateTime lastUpdatedAt) {
    this.lastUpdatedAt = lastUpdatedAt;
  }

  public T getMetadata() {
    return metadata;
  }

  public void setMetadata(T metadata) {
    this.metadata = metadata;
  }
}
