package ar.com.daruma.bymas.portfolioReturn.domain;

import ar.com.daruma.bymas.portfolioReturn.infrastructure.entities.PortfolioReturn;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.stereotype.Service;

@Service
public interface PortfolioReturnService {

  PortfolioReturn save(PortfolioReturn portfolioReturn);

  List<PortfolioReturn> findByPortfolioAccountId(UUID accountId);

  List<PortfolioReturn> findByPortfolioAssetId(UUID assetId);

  List<PortfolioReturn> findByDateBetween(LocalDate startDate, LocalDate endDate);
}
