package ar.com.daruma.bymas.portfolioReturn.infrastructure.adapter;

import ar.com.daruma.bymas.portfolioReturn.domain.PortfolioReturnService;
import ar.com.daruma.bymas.portfolioReturn.infrastructure.entities.PortfolioReturn;
import ar.com.daruma.bymas.portfolioReturn.infrastructure.repository.PortfolioReturnRepository;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioReturnServiceImpl implements PortfolioReturnService {
  @Autowired private PortfolioReturnRepository repository;

  @Override
  public PortfolioReturn save(PortfolioReturn portfolioReturn) {
    return repository.save(portfolioReturn);
  }

  @Override
  public List<PortfolioReturn> findByPortfolioAccountId(UUID accountId) {
    return repository.findByPortfolioAccountId(accountId);
  }

  @Override
  public List<PortfolioReturn> findByPortfolioAssetId(UUID assetId) {
    return repository.findByPortfolioAssetId(assetId);
  }

  @Override
  public List<PortfolioReturn> findByDateBetween(LocalDate startDate, LocalDate endDate) {
    return repository.findByDateBetween(startDate, endDate);
  }
}
