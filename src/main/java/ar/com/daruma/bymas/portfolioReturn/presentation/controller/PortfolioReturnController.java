package ar.com.daruma.bymas.portfolioReturn.presentation.controller;

import ar.com.daruma.bymas.portfolioReturn.domain.Returns;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/returns")
@Validated
public class PortfolioReturnController<T> {

  @BymasAuthorization(role = "WATCHER")
  @GetMapping("")
  public @ResponseBody ResponseEntity<Returns<T>> getReturn(
      @RequestParam(name = "account-id") @Valid @Positive(message = "Account id must be positive")
          Long accountId,
      @RequestParam(name = "asset") @NotNull String asset,
      @RequestParam(name = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {

    Returns<T> response = fetchReturn(accountId, asset, date);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  private Returns<T> fetchReturn(Long accountId, String asset, LocalDate date) {

    return new Returns<T>();
  }
}
