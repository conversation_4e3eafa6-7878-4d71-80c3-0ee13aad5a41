package ar.com.daruma.bymas.portfolioReturn.infrastructure.entities;

import ar.com.daruma.bymas.configuration.databases.bigDecimalNodeDeserialization.JsonNodeUserType;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "portfolio_returns")
public class PortfolioReturn {
  @Id private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_account_id", nullable = false)
  private PortfolioAccount portfolioAccount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_asset_id", nullable = false)
  private PortfolioAsset portfolioAsset;

  @Column(name = "initial_valuation", nullable = false)
  private BigDecimal initialValuation;

  @Column(name = "final_valuation", nullable = false)
  private BigDecimal finalValuation;

  private String description;

  private String tags;

  @Column(name = "total_valuation", nullable = false)
  private BigDecimal totalValuation;

  @Type(JsonNodeUserType.class)
  @Column(name = "metadata", columnDefinition = "jsonb")
  private JsonNode metadata;

  @Column(nullable = false)
  private LocalDate date;

  @Column(name = "last_updated_at", nullable = false)
  private LocalDate lastUpdatedAt;

  @Column(name = "created_at", nullable = false)
  private LocalDate createdAt;

  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public PortfolioAccount getPortfolioAccount() {
    return portfolioAccount;
  }

  public void setPortfolioAccount(PortfolioAccount portfolioAccount) {
    this.portfolioAccount = portfolioAccount;
  }

  public PortfolioAsset getPortfolioAsset() {
    return portfolioAsset;
  }

  public void setPortfolioAsset(PortfolioAsset portfolioAsset) {
    this.portfolioAsset = portfolioAsset;
  }

  public BigDecimal getInitialValuation() {
    return initialValuation;
  }

  public void setInitialValuation(BigDecimal initialValuation) {
    this.initialValuation = initialValuation;
  }

  public BigDecimal getFinalValuation() {
    return finalValuation;
  }

  public void setFinalValuation(BigDecimal finalValuation) {
    this.finalValuation = finalValuation;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getTags() {
    return tags;
  }

  public void setTags(String tags) {
    this.tags = tags;
  }

  public BigDecimal getTotalValuation() {
    return totalValuation;
  }

  public void setTotalValuation(BigDecimal totalValuation) {
    this.totalValuation = totalValuation;
  }

  public JsonNode getMetadata() {
    return metadata;
  }

  public void setMetadata(JsonNode metadata) {
    this.metadata = metadata;
  }

  public LocalDate getDate() {
    return date;
  }

  public void setDate(LocalDate date) {
    this.date = date;
  }

  public LocalDate getLastUpdatedAt() {
    return lastUpdatedAt;
  }

  public void setLastUpdatedAt(LocalDate lastUpdatedAt) {
    this.lastUpdatedAt = lastUpdatedAt;
  }

  public LocalDate getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDate createdAt) {
    this.createdAt = createdAt;
  }
}
