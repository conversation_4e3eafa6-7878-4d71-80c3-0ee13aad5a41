package ar.com.daruma.bymas.portfolioCompany.infrastructure.entities;

import jakarta.persistence.*;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_companies")
public class PortfolioCompany {

  @Id private UUID id;

  @Column(nullable = false)
  private String name;

  public PortfolioCompany() {}

  public PortfolioCompany(UUID id, String name) {
    this.id = id;
    this.name = name;
  }

  public boolean isAllariaMasCompany() {
    return this.name.equals("Allaria +");
  }

  public boolean isAllariaCompany() {
    return this.name.equals("Allaria");
  }
}
