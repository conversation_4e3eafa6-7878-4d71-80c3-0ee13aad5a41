package ar.com.daruma.bymas.portfolioCompany.infrastructure.adapter;

import ar.com.daruma.bymas.portfolioCompany.domain.PortfolioCompanyService;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import ar.com.daruma.bymas.portfolioCompany.infrastructure.repository.PortfolioCompanyRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioCompanyServiceImpl implements PortfolioCompanyService {
  @Autowired private PortfolioCompanyRepository repository;

  @Override
  public PortfolioCompany save(PortfolioCompany company) {
    return repository.save(company);
  }

  @Override
  public Optional<PortfolioCompany> findById(UUID id) {
    return repository.findById(id);
  }

  @Override
  public List<PortfolioCompany> findByName(String name) {
    return repository.findByName(name);
  }

  @Override
  public List<PortfolioCompany> findAll() {
    return repository.findAll();
  }

  @Override
  public Optional<PortfolioCompany> findFirstByName(String name) {
    return repository.findFirstByName(name);
  }
}
