package ar.com.daruma.bymas.portfolioCompany.infrastructure.repository;

import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioCompanyRepository extends JpaRepository<PortfolioCompany, UUID> {
  List<PortfolioCompany> findByName(String name);

  Optional<PortfolioCompany> findFirstByName(String name);
}
