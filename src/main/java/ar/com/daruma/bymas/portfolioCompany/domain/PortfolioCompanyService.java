package ar.com.daruma.bymas.portfolioCompany.domain;

import ar.com.daruma.bymas.portfolioCompany.infrastructure.entities.PortfolioCompany;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Service;

@Service
public interface PortfolioCompanyService {
  PortfolioCompany save(PortfolioCompany company);

  Optional<PortfolioCompany> findById(UUID id);

  List<PortfolioCompany> findByName(String name);

  List<PortfolioCompany> findAll();

  Optional<PortfolioCompany> findFirstByName(String name);
}
