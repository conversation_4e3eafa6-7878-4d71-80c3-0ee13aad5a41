package ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.repository;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.entitites.PortfolioAccountRelationshipType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioAccountRelationshipsRepository
    extends JpaRepository<PortfolioAccountRelationships, UUID> {

  @EntityGraph(attributePaths = {"portfolioAccount", "portfolioAccount.company"})
  List<PortfolioAccountRelationships> findByAccountId(Integer accountNumber);

  Optional<PortfolioAccountRelationships> findByAccountIdAndPortfolioAccountAndType(
      Integer accountId, PortfolioAccount portfolioAccount, PortfolioAccountRelationshipType type);

  Optional<PortfolioAccountRelationships> findByPortfolioAccountAndAccountId(
      PortfolioAccount allariaMasAccount, Integer accountId);
}
