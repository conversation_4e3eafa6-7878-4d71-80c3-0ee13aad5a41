package ar.com.daruma.bymas.portfolioAccountRelationships.application.upsert;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindOrInsertPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.domain.PortfolioAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.application.upsert.UpsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UpsertPortfolioAccountRelationshipsByAllariaLinks {

  private final PortfolioAccountRelationshipsService portfolioAccountRelationshipsService;
  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;

  private final UpsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink
      upsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink;

  private final FindOrInsertPortfolioAccountByAccountId findOrInsertPortfolioAccountByAccountId;

  @Transactional(transactionManager = "byMasTransactionManager")
  public void upsert(AllariaLinks allariaLinks) {
    log.info(
        "Starting upsert of portfolio account relationship with user {} for allaria links: accountId={}, allariaAccountId={}",
        allariaLinks.getUserId(),
        allariaLinks.getAccountId(),
        allariaLinks.getAllariaAccountId());
    PortfolioAccount allariaAccount =
        findOrInsertPortfolioAccountByAccountId.findOrInsert(allariaLinks.getAllariaAccountId());
    log.info("Found allaria account with id: {}", allariaAccount.getId());
    PortfolioAccountRelationships relationship =
        portfolioAccountRelationshipsService.upsertPortfolioAccountRelationshipByAllariaLinks(
            allariaLinks, allariaAccount);
    upsertPortfolioUserAccountRelationshipByRelationshipAndAllariaLink.execute(
        relationship, allariaLinks);
    log.info(
        "Successfully upserted portfolio user account relationship with id: {} for user: {}",
        relationship.getId().toString(),
        allariaLinks.getUserId());
  }
}
