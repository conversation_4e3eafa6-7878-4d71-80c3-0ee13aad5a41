package ar.com.daruma.bymas.portfolioAccountRelationships.application.find;

import ar.com.daruma.bymas.portfolioAccountRelationships.domain.PortfolioAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class FindPortfolioAccountRelationshipsByAccountId {

  @Autowired private PortfolioAccountRelationshipsService portfolioAccountRelationshipsService;

  private static final CustomLogger logger =
      CustomLogger.getLogger(FindPortfolioAccountRelationshipsByAccountId.class);

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<PortfolioAccountRelationships> find(Integer accountId) {
    logger.info("Finding portfolio account relationships by account id: " + accountId);
    List<PortfolioAccountRelationships> relationships =
        portfolioAccountRelationshipsService.findByAccountId(accountId);
    logger.info("Found {} portfolio account relationships", relationships.size());
    return relationships;
  }
}
