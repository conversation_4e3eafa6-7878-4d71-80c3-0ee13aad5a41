package ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.entitites.PortfolioAccountRelationshipType;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.infrastructure.entities.PortfolioUserAccountRelationship;
import ar.com.daruma.bymas.utils.infrastructure.AuditableEntity;
import ar.com.daruma.citadel.model.global.ActiveState;
import jakarta.persistence.*;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "portfolio_account_relationships")
public class PortfolioAccountRelationships extends AuditableEntity {
  @Id private UUID id;

  @Column(name = "account_id", nullable = false)
  private int accountId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "portfolio_account_id", nullable = false)
  private PortfolioAccount portfolioAccount;

  @Enumerated(EnumType.STRING)
  @Column(name = "state", nullable = false)
  private ActiveState state;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private PortfolioAccountRelationshipType type;

  @OneToMany(mappedBy = "relationship")
  private Set<PortfolioUserAccountRelationship> portfolioUserAccountRelationships =
      new LinkedHashSet<>();

  public PortfolioAccountRelationships() {}

  public PortfolioAccountRelationships(
      PortfolioAccount allariaMasAccount,
      Integer allariaAccountId,
      ActiveState state,
      PortfolioAccountRelationshipType portfolioAccountRelationshipType) {
    this.id = UUID.randomUUID();
    this.accountId = allariaAccountId;
    this.portfolioAccount = allariaMasAccount;
    this.state = state;
    this.type = portfolioAccountRelationshipType;
  }

  public boolean isMirrored() {
    return this.type.isMirrored();
  }

  public UUID getPortfolioAccountId() {
    return this.portfolioAccount.getId();
  }
}
