package ar.com.daruma.bymas.portfolioAccountRelationships.domain;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.entities.AllariaMirroredAccounts;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import java.util.List;

public interface PortfolioAccountRelationshipsService {

  List<PortfolioAccountRelationships> findByAccountId(Integer accountId);

  List<PortfolioAccountRelationships> upsertPortfolioAccountRelationshipByAllariaMirroredAccounts(
      AllariaMirroredAccounts allariaMirroredAccounts,
      PortfolioAccount allariaAccount,
      PortfolioAccount allariaMasAccount);

  PortfolioAccountRelationships upsertPortfolioAccountRelationshipByAllariaLinks(
      AllariaLinks allariaLinks, PortfolioAccount allariaAccount);
}
