package ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.adapter;

import static ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.entitites.PortfolioAccountRelationshipType.ALLARIA_LINK;
import static ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.entitites.PortfolioAccountRelationshipType.MIRRORED;

import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.entities.AllariaMirroredAccounts;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.domain.PortfolioAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.repository.PortfolioAccountRelationshipsRepository;
import ar.com.daruma.bymas.portfolioUserAccountRelationships.domain.entitites.PortfolioAccountRelationshipType;
import ar.com.daruma.citadel.model.global.ActiveState;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioAccountRelationshipsServiceImpl
    implements PortfolioAccountRelationshipsService {

  @Autowired private PortfolioAccountRelationshipsRepository repository;

  @Override
  public List<PortfolioAccountRelationships> findByAccountId(Integer accountId) {
    return repository.findByAccountId(accountId);
  }

  private PortfolioAccountRelationships upsertPortfolioAccountRelationship(
      PortfolioAccount portfolioAccount,
      Integer accountId,
      PortfolioAccountRelationshipType type,
      ActiveState state) {
    Optional<PortfolioAccountRelationships> maybeRelationship =
        repository.findByPortfolioAccountAndAccountId(portfolioAccount, accountId);
    maybeRelationship.ifPresent(
        relationship -> {
          relationship.setType(type);
          relationship.setState(state);
        });
    return repository.save(
        maybeRelationship.orElseGet(
            () -> new PortfolioAccountRelationships(portfolioAccount, accountId, state, type)));
  }

  @Override
  public List<PortfolioAccountRelationships>
      upsertPortfolioAccountRelationshipByAllariaMirroredAccounts(
          AllariaMirroredAccounts allariaMirroredAccounts,
          PortfolioAccount allariaAccount,
          PortfolioAccount allariaMasAccount) {
    ActiveState state = allariaMirroredAccounts.getActiveState();
    PortfolioAccountRelationships allariaToAllariaMasRelationship =
        upsertPortfolioAccountRelationship(
            allariaMasAccount, allariaMirroredAccounts.getAllariaAccountId(), MIRRORED, state);
    PortfolioAccountRelationships allariaMasToAllariaRelationship =
        upsertPortfolioAccountRelationship(
            allariaAccount, allariaMirroredAccounts.getAccountId(), MIRRORED, state);
    return List.of(allariaToAllariaMasRelationship, allariaMasToAllariaRelationship);
  }

  @Override
  public PortfolioAccountRelationships upsertPortfolioAccountRelationshipByAllariaLinks(
      AllariaLinks allariaLinks, PortfolioAccount allariaAccount) {
    ActiveState state = allariaLinks.getActiveState();
    return upsertPortfolioAccountRelationship(
        allariaAccount, allariaLinks.getAccountId(), ALLARIA_LINK, state);
  }
}
