package ar.com.daruma.bymas.portfolioAccountRelationships.application.upsert;

import ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.entities.AllariaMirroredAccounts;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindPortfolioAccountByAccountId;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAccountRelationships.domain.PortfolioAccountRelationshipsService;
import ar.com.daruma.bymas.portfolioAccountRelationships.infrastructure.entities.PortfolioAccountRelationships;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UpsertPortfolioAccountRelationshipsByAllariaMirroredAccounts {

  private final PortfolioAccountRelationshipsService portfolioAccountRelationshipsService;
  private final FindPortfolioAccountByAccountId findPortfolioAccountByAccountId;

  @Transactional(transactionManager = "byMasTransactionManager")
  public List<PortfolioAccountRelationships> upsert(
      AllariaMirroredAccounts allariaMirroredAccounts) {
    log.info(
        "Starting upsert of portfolio account relationships for mirrored accounts: accountId={}, allariaAccountId={}",
        allariaMirroredAccounts.getAccountId(),
        allariaMirroredAccounts.getAllariaAccountId());

    PortfolioAccount allariaMasAccount =
        findPortfolioAccountByAccountId.findAllariaMasAccount(
            allariaMirroredAccounts.getAccountId());
    log.info("Found allariaMas account with id: {}", allariaMasAccount.getId());

    PortfolioAccount allariaAccount =
        findPortfolioAccountByAccountId.findAllariaAccount(
            allariaMirroredAccounts.getAllariaAccountId());
    log.info("Found allaria account with id: {}", allariaAccount.getId());

    List<PortfolioAccountRelationships> relationships =
        portfolioAccountRelationshipsService
            .upsertPortfolioAccountRelationshipByAllariaMirroredAccounts(
                allariaMirroredAccounts, allariaAccount, allariaMasAccount);

    log.info("Successfully upserted {} portfolio account relationships", relationships.size());
    return relationships;
  }
}
