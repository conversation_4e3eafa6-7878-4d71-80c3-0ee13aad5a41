package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

public enum InvoiceOperationState {
  COMPLETED("COMPLETED"),
  FAILED("FAILED"),
  ABORTED("ABORTED"),
  PENDING_APPROVAL("PENDING_APPROVAL"),
  APPROVED("APPROVED"),
  PARTIALLY_APPROVED("PARTIALLY_APPROVED"),
  PENDING_FUNDS("PENDING_FUNDS"),
  EXPIRED("EXPIRED"),
  PENDING_CONFIRM("PENDING_CONFIRM");

  private final String description;

  InvoiceOperationState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }
}
