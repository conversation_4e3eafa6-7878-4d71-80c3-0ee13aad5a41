package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.PAYMENT_OPERATION;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.DEBIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.PAGO_SERVICIO;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities.SignatureDataWithType;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentOperationDataResponse implements ExternalMovement {

  private Long id;
  private String paymentCode;
  private Integer accountId;
  private String invoiceType;
  private String externalCode;
  private @Nullable String operationCode;
  private PaymentOperationState state;
  private BigDecimal taxAmount;
  private BigDecimal debitedAmount;
  private BigDecimal paidAmount;
  private @Nullable LocalDate expirationDate;
  private LocalDateTime createdAt;
  private LocalDateTime modifiedAt;
  private SignatureDataWithType maybeSignatureDataWithType;
  private MovixRecord movixRecord;

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(this.accountId);
  }

  @Override
  public String getOperationId() {
    return id + "-payment_operation";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(PAYMENT_OPERATION, state.getDescription());
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.createdAt.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.createdAt.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.debitedAmount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.paidAmount;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return this.taxAmount;
  }

  @Override
  public BigDecimal getQuantity() {
    return this.debitedAmount;
  }

  @Override
  public String getDescription() {
    return "Payment operation id:" + id;
  }

  @Override
  public OperationType getOperationType() {
    return DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return PAGO_SERVICIO;
  }

  @Override
  public String getCurrency() {
    return "Pesos";
  }

  @Override
  public MovementType getMovementType() {
    return PAYMENT_OPERATION;
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }
}
