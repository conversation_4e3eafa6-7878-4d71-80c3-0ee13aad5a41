package ar.com.daruma.bymas.movixEvent.domain.deserializer;

import ar.com.daruma.bymas.movixEvent.domain.cards.entities.MovixCardMovement;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;

public class MovixCardMovementDeserializer extends JsonDeserializer<MovixCardMovement> {

  @Override
  public MovixCardMovement deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException, JacksonException {
    JsonNode node = jsonParser.getCodec().readTree(jsonParser);

    int accountId = node.get("accountId").asInt();
    int userId = node.get("userId").asInt();
    int cardId = node.get("cardId").asInt();
    int cardMovementId = node.get("cardMovementId").asInt();
    // TODO: add necessary fields

    return new MovixCardMovement(accountId, userId, cardId, cardMovementId);
  }
}
