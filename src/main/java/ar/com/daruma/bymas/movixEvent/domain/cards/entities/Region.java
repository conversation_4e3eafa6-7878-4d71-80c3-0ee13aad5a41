package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum Region {
  BUENOS_AIRES("Buenos Aires"),
  CATAMARCA("Catamarca"),
  CHACO("Chaco"),
  CHUBUT("Chubut"),
  CABA("Ciudad Autónoma de Buenos Aires"),
  CORRIENTES("Corrientes"),
  CORDOBA("Córdoba"),
  ENTRE_RIOS("Entre Ríos"),
  FORMOSA("Formosa"),
  JUJUY("Jujuy"),
  LA_PAMPA("La Pampa"),
  LA_RIOJA("La Rioja"),
  MENDOZA("Mendoza"),
  MISIONES("Misiones"),
  NEUQUEN("Neuquén"),
  RIO_NEGRO("Río Negro"),
  SALTA("Salta"),
  SAN_JUAN("San Juan"),
  SAN_LUIS("San Luis"),
  SANTA_CRUZ("Santa Cruz"),
  SANTA_FE("Santa Fe"),
  SANTIAGO_DEL_ESTERO("Santiago del Estero"),
  TIERRA_DEL_FUEGO("Tierra del Fuego"),
  TUCUMAN("Tucumán");

  private final String description;

  Region(String description) {
    this.description = description;
  }

  @JsonValue
  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static Region fromDisplayName(String displayName) {
    for (Region region : Region.values()) {
      if (region.description.equals(displayName)) {
        return region;
      }
    }
    throw new IllegalArgumentException("No enum constant with display name: " + displayName);
  }
}
