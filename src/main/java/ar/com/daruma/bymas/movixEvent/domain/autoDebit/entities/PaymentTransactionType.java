package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum PaymentTransactionType {
  CANCELLATION,
  PAYMENT,
  REFUND;

  public static PaymentTransactionType fromString(String value) {
    for (PaymentTransactionType type : PaymentTransactionType.values()) {
      if (type.name().equalsIgnoreCase(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid PaymentTransactionType: " + value);
  }
}
