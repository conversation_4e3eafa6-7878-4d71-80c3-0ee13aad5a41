package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.CARD;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardMovementResponse implements ExternalMovement {
  private Integer id;
  private Integer accountId;
  private Integer cardId;
  private String transactionId;
  private @Nullable String originalTransactionId;

  private CardMovementState state;

  private MovementOrigin origin;

  private OperationType movementType;

  private CardTransactionType transactionType;

  private @Nullable ChargebackResponse maybeChargeback;

  private MerchantDetails merchant;

  private AmountInformationResponse amountInformation;
  private UserCardMovement user;
  private @Nullable Boolean hasExpenses;
  private LocalDateTime processDateTime;
  private LocalDateTime creationDateTime;
  private LocalDateTime updateDateTime;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-card_movement";
  }

  @Override
  public String getDescription() {
    return "card movement id:" + id;
  }

  public BigDecimal getAmount() {
    return this.amountInformation.getLocal().getAmount();
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.amountInformation.getLocal().getTotalAmount();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return this.amountInformation.getLocal().getTaxAmount();
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.amountInformation.getLocal().getAmount();
  }

  @Override
  public BigDecimal getQuantity() {
    return this.amountInformation.getLocal().getAmount();
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(CARD, this.state.toString());
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.processDateTime.toLocalDate();
  }

  @Override
  public OperationType getOperationType() {
    return this.movementType;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return MarketOperationType.CARD_MOVEMENT;
  }

  @Override
  public String getCurrency() {
    return this.amountInformation.getOriginal().getCurrency();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.creationDateTime.toLocalDate();
  }

  @Override
  public Long getId() {
    return this.id.longValue();
  }

  @Override
  public MovementType getMovementType() {
    return CARD;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Nullable
  public ChargebackResponse getMaybeChargeback() {
    return maybeChargeback;
  }

  @Nullable
  public String getOriginalTransactionId() {
    return originalTransactionId;
  }

  public void setOriginalTransactionId(@Nullable String originalTransactionId) {
    this.originalTransactionId = originalTransactionId;
  }

  public void setMaybeChargeback(@Nullable ChargebackResponse maybeChargeback) {
    this.maybeChargeback = maybeChargeback;
  }

  @Nullable
  public Boolean getHasExpenses() {
    return hasExpenses;
  }

  public void setHasExpenses(@Nullable Boolean hasExpenses) {
    this.hasExpenses = hasExpenses;
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
