package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceProvider {
  private int id;
  private String prismaCompanyId;
  private String categoryId;
  private int priority;
  private String name;
  private String category;
  private String pictureUrl;
  private String title;
  private int minLength;
  private int maxLength;
  private String additionalData;
  private PaymentType paymentType;
  private AmountType amountType;
  private ProviderState state;
}
