package ar.com.daruma.bymas.movixEvent.domain.investmentfundsIntentions.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.*;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FundTransactionIntentionResponse implements ExternalMovement {
  private Long id;
  private Long fundId;
  private String fundName;
  private Long accountId;
  private @Nullable String accountName;
  private FundTransactionIntentionState state;
  private OrderType orderType;
  private BigDecimal amount;
  private String currency;
  private LocalDateTime creationDateTime;
  private Boolean redeemAll;
  private BigDecimal shares;
  private @Nullable LocalDateTime settlementDate;
  private BigDecimal settlementValue;
  private Boolean isEstimated;
  private @Nullable RequestUser requestUser;
  private MovixRecord movixRecord;

  @Nullable
  public LocalDate getSettlementDate() {
    return this.settlementDate.toLocalDate();
  }

  public void setSettlementDate(@Nullable LocalDateTime settlementDate) {
    this.settlementDate = settlementDate;
  }

  @Nullable
  public RequestUser getRequestUser() {
    return this.requestUser;
  }

  public void setRequestUser(@Nullable RequestUser requestUser) {
    this.requestUser = requestUser;
  }

  @Override
  public String getDescription() {
    return "Fund transaction intention id: " + id;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public String getOperationId() {
    return id.toString() + "-fund_transaction_intention";
  }

  @Override
  public MovementState getMovementState() {
    if (orderType == OrderType.SUBSCRIPTION) {
      return StateMapper.getMovementState(FCI_SUBSCRIPTION, state.getDescription());
    } else {
      return StateMapper.getMovementState(FCI_REDEMPTION, state.getDescription());
    }
  }

  @Override
  public OperationType getOperationType() {
    if (this.orderType == OrderType.REDEMPTION) {
      return OperationType.DEBIT;
    } else return OperationType.CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    if (this.orderType == OrderType.SUBSCRIPTION) {
      return MarketOperationType.SUSCRIPCION;
    } else return MarketOperationType.RESCATE;
  }

  @Override
  public BigDecimal getNetAmount() {
    return amount;
  }

  @Override
  public MovementType getMovementType() {
    return FCI;
  }

  @Override
  public LocalDate getAgreementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId.intValue());
  }
}
