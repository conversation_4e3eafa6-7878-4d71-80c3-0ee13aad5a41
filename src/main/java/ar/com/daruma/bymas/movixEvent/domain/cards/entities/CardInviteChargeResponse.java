package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.CARD_INVITE;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.CARD_MOVEMENT;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardInviteChargeResponse implements ExternalMovement {
  private Long id;
  private Integer accountId;
  private String accountName;
  private CardInviteState state;
  private PepType pep;
  private CardholderResponse cardholder;
  private @Nullable UserResponse requestedByUser;
  private @Nullable UserResponse revokedByUser;
  private SignatureDataResponse signatureRequest;
  private @Nullable Integer previousInviteId;
  private Boolean chargesWasProcessed;
  private @Nullable CardInviteResponseCharges embossingCharges;
  private @Nullable CardInviteResponseCharges embossingConvertedCharges;
  private @Nullable CardInviteResponseCharges shipmentCharges;
  private LocalDateTime creationDateTime;
  private LocalDateTime updateDateTime;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-card_invite";
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(this.accountId);
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(CARD_INVITE, state.getDescription());
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.creationDateTime.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    BigDecimal embossingTaxes =
        Optional.ofNullable(this.embossingConvertedCharges)
            .map(CardInviteResponseCharges::totalTaxesAmount)
            .orElse(BigDecimal.ZERO);

    BigDecimal shipmentTaxes =
        Optional.ofNullable(this.shipmentCharges)
            .map(CardInviteResponseCharges::totalTaxesAmount)
            .orElse(BigDecimal.ZERO);

    return embossingTaxes.add(shipmentTaxes);
  }

  @Override
  public BigDecimal getGrossAmount() {
    return getNetAmount().add(getTaxAmount());
  }

  @Override
  public BigDecimal getNetAmount() {
    BigDecimal embossingCharges =
        Optional.ofNullable(this.embossingConvertedCharges)
            .map(CardInviteResponseCharges::getCharges)
            .orElse(BigDecimal.ZERO);

    BigDecimal shipmentCharges =
        Optional.ofNullable(this.shipmentCharges)
            .map(CardInviteResponseCharges::getCharges)
            .orElse(BigDecimal.ZERO);

    return embossingCharges.add(shipmentCharges);
  }

  @Override
  public BigDecimal getQuantity() {
    return getGrossAmount();
  }

  @Override
  public String getDescription() {
    return "card invite id:" + id;
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return CARD_MOVEMENT;
  }

  @Override
  public String getCurrency() {
    return "Pesos";
  }

  @Override
  public MovementType getMovementType() {
    return CARD_INVITE;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }
}
