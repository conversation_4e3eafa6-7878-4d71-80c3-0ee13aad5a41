package ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioOperations.entities;

import static ar.com.daruma.bymas.utils.numbers.BigDecimalUtils.bigDecimalScale;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import lombok.*;
import org.springframework.lang.Nullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MepPurchaseDetailsResponse {
  private Integer id;
  private Integer operationId;
  private BigDecimal estimatedPrice;
  private BigDecimal amount;
  private String termAndConditionsVersion;
  private @Nullable BigDecimal finalPrice;
  private @Nullable BigDecimal usedAmount;
  private @Nullable BigDecimal notUsedAmount;
  private @Nullable BigDecimal purchasedUsdAmount;
  private @Nullable BigDecimal purchasedBonds;

  public Optional<BigDecimal> getFinalPrice() {
    return Optional.ofNullable(finalPrice);
  }

  public BigDecimal getUsedAmount() {
    return Optional.ofNullable(usedAmount).orElseGet(() -> amount);
  }

  public Optional<BigDecimal> getNotUsedAmount() {
    return Optional.ofNullable(notUsedAmount);
  }

  public Optional<BigDecimal> getPurchasedUsdAmount() {
    return Optional.ofNullable(purchasedUsdAmount);
  }

  public BigDecimal getDollarAmount() {
    return Optional.ofNullable(purchasedUsdAmount)
        .orElseGet(
            () -> getUsedAmount().divide(estimatedPrice, bigDecimalScale, RoundingMode.HALF_DOWN));
  }

  public Optional<BigDecimal> getPurchasedBonds() {
    return Optional.ofNullable(purchasedBonds);
  }
}
