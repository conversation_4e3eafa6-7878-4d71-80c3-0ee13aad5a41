package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.WRAPPER_OBJECT)
@JsonSubTypes({
  @JsonSubTypes.Type(value = Vep.class, name = "Vep"),
  @JsonSubTypes.Type(value = ServicePayment.class, name = "ServicePayment")
})
@Data
@NoArgsConstructor
public abstract class Invoice {}
