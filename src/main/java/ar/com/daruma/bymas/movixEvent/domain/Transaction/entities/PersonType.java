package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

public enum PersonType {
  PHYSICAL("Physical"),
  JURIDICAL("Juridical");

  private final String description;

  PersonType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public static PersonType fromString(String name) {
    return PersonType.valueOf(name.toUpperCase());
  }
}
