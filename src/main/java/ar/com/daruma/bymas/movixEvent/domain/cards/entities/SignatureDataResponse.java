package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import ar.com.daruma.citadel.model.global.ActiveState;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureRequestState;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignatureDataResponse {
  private Integer signatureRequestId;
  private Integer solicitorId;
  private SignatureRequestState signedState;
  private ActiveState activeState;
  private Set<SignerResponse> signers;
  private Set<SignerResponse> potentialSigners;
  private @Nullable SolidarySignatureRuleResponse maybeAppliedSolidaryRule;
  private @Nullable JointSignatureRuleResponse maybeAppliedJointRule;
  private @Nullable SignatureDataResponse previousSignatureRequest;
}
