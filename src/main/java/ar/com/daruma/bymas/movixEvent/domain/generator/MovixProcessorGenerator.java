package ar.com.daruma.bymas.movixEvent.domain.generator;

import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.Transaction.MovixBulkTransactionProcessor;
import ar.com.daruma.bymas.movixEvent.domain.Transaction.MovixTransactionProcessor;
import ar.com.daruma.bymas.movixEvent.domain.accountShares.MovixAccountSharesProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaInboundTransaction.MovixInboundTransactionProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.MovixAllariaLinksProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaMasAccount.MovixAllariaMasAccountProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.MovixAllariaMirroredAccountsProcessor;
import ar.com.daruma.bymas.movixEvent.domain.autoDebit.MovixAutoDebitProcessor;
import ar.com.daruma.bymas.movixEvent.domain.cards.MovixCardInviteProcessor;
import ar.com.daruma.bymas.movixEvent.domain.cards.MovixCardsProcessor;
import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount.MovixDolarMepPortfolioAccountProcessor;
import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioOperations.MovixDolarMepPortfolioOperationProcessor;
import ar.com.daruma.bymas.movixEvent.domain.echeq.MovixEcheqProcessor;
import ar.com.daruma.bymas.movixEvent.domain.fundTransfer.MovixFundTransferProcessor;
import ar.com.daruma.bymas.movixEvent.domain.fundsSolicitation.MovixFundsSolicitacionProcessor;
import ar.com.daruma.bymas.movixEvent.domain.investmentfundsIntentions.MovixInvestmentFoundIntentionProcessor;
import ar.com.daruma.bymas.movixEvent.domain.paymentLink.MovixPaymentLinkProcessor;
import ar.com.daruma.bymas.movixEvent.domain.paymentsHub.MovixInvoiceOperationProcessor;
import ar.com.daruma.bymas.movixEvent.domain.paymentsHub.MovixPaymentOperationProcessor;
import ar.com.daruma.bymas.movixEvent.domain.returns.MovixReturnsProcessor;
import ar.com.daruma.bymas.movixEvent.domain.servicePayment.MovixProgrammedPaymentProcessor;
import ar.com.daruma.bymas.movixEvent.domain.servicePayment.MovixServicePaymentProcessor;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MovixProcessorGenerator {

  private static final CustomLogger logger = CustomLogger.getLogger(MovixProcessorGenerator.class);
  private static final Map<String, MovixProcessor<?>> processorMap = new HashMap<>();

  @Autowired
  public MovixProcessorGenerator(
      MovixAutoDebitProcessor autoDebitProcessor,
      MovixDolarMepPortfolioOperationProcessor dolarMepProcessor,
      MovixDolarMepPortfolioAccountProcessor movixDolarMepPortfolioAccountProcessor,
      MovixAllariaMasAccountProcessor movixAllariaMasAccountProcessor,
      MovixAccountSharesProcessor movixAccountSharesProcessor,
      MovixTransactionProcessor transactionProcessor,
      MovixReturnsProcessor returnsProcessor,
      MovixEcheqProcessor echeqProcessor,
      MovixPaymentLinkProcessor movixPaymentLinkProcessor,
      MovixInvestmentFoundIntentionProcessor movixInvestmentFoundIntentionProcessor,
      MovixServicePaymentProcessor movixServicePaymentProcessor,
      MovixAllariaMirroredAccountsProcessor movixAllariaMirroredAccountsProcessor,
      MovixAllariaLinksProcessor movixAllariaLinksProcessor,
      MovixFundTransferProcessor movixFundTransferProcessor,
      MovixCardsProcessor movixCardsProcessor,
      MovixInvoiceOperationProcessor movixInvoiceOperationProcessor,
      MovixPaymentOperationProcessor movixPaymentOperationProcessor,
      MovixCardInviteProcessor movixCardInviteProcessor,
      MovixProgrammedPaymentProcessor movixProgrammedPaymentProcessor,
      MovixBulkTransactionProcessor movixBulkTransactionProcessor,
      MovixInboundTransactionProcessor movixInboundTransactionProcessor,
      MovixFundsSolicitacionProcessor movixFundsSolicitacionProcessor) {

    processorMap.put("auto_debit_subscription_payments", autoDebitProcessor);
    processorMap.put("portfolio_operations", dolarMepProcessor);
    processorMap.put("accounts", movixAllariaMasAccountProcessor);
    processorMap.put("account_shares", movixAccountSharesProcessor);
    processorMap.put("transactions", transactionProcessor);
    processorMap.put("returns", returnsProcessor);
    processorMap.put("allaria_echeqs", echeqProcessor);
    processorMap.put("investment_fund_intentions", movixInvestmentFoundIntentionProcessor);
    processorMap.put("portfolio_accounts", movixDolarMepPortfolioAccountProcessor);
    processorMap.put("payment", movixPaymentLinkProcessor);
    processorMap.put("service_payment", movixServicePaymentProcessor);
    processorMap.put("allaria_mirrored_accounts", movixAllariaMirroredAccountsProcessor);
    processorMap.put("allaria_links", movixAllariaLinksProcessor);
    processorMap.put("allaria_funds_transfer", movixFundTransferProcessor);
    processorMap.put("card_movements", movixCardsProcessor);
    processorMap.put("invoice_operation", movixInvoiceOperationProcessor);
    processorMap.put("payment_operation", movixPaymentOperationProcessor);
    processorMap.put("card_invites", movixCardInviteProcessor);
    processorMap.put("programmed_payment", movixProgrammedPaymentProcessor);
    processorMap.put("bulk_transactions", movixBulkTransactionProcessor);
    processorMap.put("allaria_inbound_transaction", movixInboundTransactionProcessor);
    processorMap.put("allaria_redemptions", movixFundsSolicitacionProcessor);
  }

  public MovixProcessor<?> getProcessorFromTable(String tableName) {
    logger.info("Generating movix webhook processor from table {}", tableName);
    Objects.requireNonNull(tableName, "Processor table name cannot be null");
    if (tableName.trim().isEmpty()) {
      throw new IllegalArgumentException("Processor table name cannot be empty");
    }

    MovixProcessor<?> processorSupplier = processorMap.get(tableName);
    if (processorSupplier == null) {
      throw new IllegalArgumentException("No processor found for table name: " + tableName);
    }
    return processorSupplier;
  }
}
