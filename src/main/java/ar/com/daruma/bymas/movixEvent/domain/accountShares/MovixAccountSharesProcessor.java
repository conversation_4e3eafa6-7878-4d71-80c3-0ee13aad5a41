package ar.com.daruma.bymas.movixEvent.domain.accountShares;

import ar.com.daruma.bymas.allariaMas.mutualFund.application.find.FindMutualFundById;
import ar.com.daruma.bymas.allariaMas.mutualFund.infrastructure.entities.MutualFund;
import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.accountShares.entities.AccountShares;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.application.findsert.FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByAccountShares;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixAccountSharesProcessor extends MovixProcessor<AccountShares> {

  @Autowired
  private FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Autowired private FindMutualFundById findMutualFundById;

  @Autowired
  private FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency
      findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency;

  @Autowired private UpdatePortfolioPositionByAccountShares updatePortfolioPositionByAccountShares;

  public MovixAccountSharesProcessor() {
    super("account_shares", AccountShares.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    AccountShares response = getDataFromJsonNode(movixRecord);
    process(response);
  }

  private void process(AccountShares accountShares) {
    PortfolioAccount portfolioAccount = findsertPortfolioAccountByAccountId(accountShares);
    MutualFund mutualFund = findMutualFundById.find(accountShares.getMutualFundId());
    PortfolioAsset asset =
        findsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency.findsert(
            mutualFund.getDescription(),
            mutualFund.getCategory(),
            mutualFund.getSubCategory(),
            mutualFund.getAbbreviation(),
            mutualFund.getCurrency(),
            Optional.empty());
    updatePortfolioPositionByAccountShares.update(accountShares, portfolioAccount, asset);
  }

  private PortfolioAccount findsertPortfolioAccountByAccountId(AccountShares accountShares) {
    return findsertPortfolioAccountByAccountNumber.findsert(accountShares.getAccountId());
  }
}
