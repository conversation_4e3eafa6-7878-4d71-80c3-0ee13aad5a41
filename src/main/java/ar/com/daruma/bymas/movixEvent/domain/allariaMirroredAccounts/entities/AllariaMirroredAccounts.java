package ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.entities;

import static ar.com.daruma.citadel.model.global.ActiveState.ACTIVE;
import static ar.com.daruma.citadel.model.global.ActiveState.INACTIVE;

import ar.com.daruma.citadel.model.global.ActiveState;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllariaMirroredAccounts {

  @JsonProperty("id")
  private Integer id;

  @JsonProperty("account_id")
  private Integer accountId;

  @JsonProperty("allaria_account_id")
  @Getter(AccessLevel.NONE)
  private String allariaAccountId;

  @JsonProperty("state")
  private String state;

  @JsonProperty("created_at")
  private String createdAt;

  @JsonProperty("created_by")
  private Integer createdBy;

  @JsonProperty("updated_at")
  private String updatedAt;

  @JsonProperty("updated_by")
  private Integer updatedBy;

  @JsonProperty("investment_account_id")
  private String investmentAccountId;

  @JsonProperty("allaria_account_code")
  private String allariaAccountCode;

  @JsonProperty("in_whitelist")
  private Boolean inWhitelist;

  @JsonProperty("requested_by")
  private Integer requestedBy;

  public ActiveState getActiveState() {
    if (state.equals("ACTIVE")) {
      return ACTIVE;
    }
    return INACTIVE;
  }

  public Integer getAllariaAccountId() {
    return Integer.parseInt(allariaAccountId);
  }
}
