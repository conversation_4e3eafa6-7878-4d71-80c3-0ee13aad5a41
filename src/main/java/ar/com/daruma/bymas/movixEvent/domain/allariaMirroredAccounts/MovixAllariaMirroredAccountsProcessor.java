package ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts;

import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaMirroredAccounts.entities.AllariaMirroredAccounts;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAccountRelationships.application.upsert.UpsertPortfolioAccountRelationshipsByAllariaMirroredAccounts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixAllariaMirroredAccountsProcessor extends MovixProcessor<AllariaMirroredAccounts> {

  @Autowired
  private UpsertPortfolioAccountRelationshipsByAllariaMirroredAccounts
      upsertPortfolioAccountRelationshipsByAllariaMirroredAccounts;

  public MovixAllariaMirroredAccountsProcessor() {
    super("allaria_mirrored_accounts", AllariaMirroredAccounts.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    AllariaMirroredAccounts response = getDataFromJsonNode(movixRecord);
    upsertPortfolioAccountRelationshipsByAllariaMirroredAccounts.upsert(response);
  }
}
