package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum MaritalStatus {
  SINGLE("SOLTERO"),
  MARRIED("CASADO"),
  DIVORCED("DIVORCIADO"),
  WIDOWER("VIUDO");

  private final String description;

  MaritalStatus(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static MaritalStatus fromDisplayName(String displayName) {
    for (MaritalStatus ms : MaritalStatus.values()) {
      if (ms.description.equals(displayName)) {
        return ms;
      }
    }
    throw new IllegalArgumentException("No enum constant with display name: " + displayName);
  }
}
