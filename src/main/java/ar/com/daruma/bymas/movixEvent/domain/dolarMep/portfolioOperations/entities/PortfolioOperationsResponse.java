package ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioOperations.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.DOLLAR_MEP;
import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarMepName;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;
import org.springframework.lang.Nullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortfolioOperationsResponse implements ExternalMovement {

  @Getter(AccessLevel.NONE)
  private Integer id;

  private String externalId;
  private Integer accountId;
  private Integer portfolioAccountId;
  private Integer sourceInstrumentId;
  private BigDecimal quantity;
  private String type;

  private String state;

  private LocalDateTime createdAt;
  private LocalDateTime processedAt;
  private LocalDateTime finishedAt;
  private @Nullable MepPurchaseDetailsResponse mepPurchaseBody;
  private @Nullable MepTransferDetailsResponse mepTransferBody;
  private @Nullable MepSaleDetailsResponse mepSellBody;

  private MovixRecord movixRecord;

  private WorkingDayService workingDayService;

  public Optional<LocalDateTime> getProcessedAt() {
    return Optional.ofNullable(processedAt);
  }

  public Optional<LocalDateTime> getFinishedAt() {
    return Optional.ofNullable(finishedAt);
  }

  public Optional<MepPurchaseDetailsResponse> getMepPurchaseBody() {
    return Optional.ofNullable(mepPurchaseBody);
  }

  public Optional<MepTransferDetailsResponse> getMepTransferBody() {
    return Optional.ofNullable(mepTransferBody);
  }

  public Optional<MepSaleDetailsResponse> getMepSellBody() {
    return Optional.ofNullable(mepSellBody);
  }

  @Override
  public MovementState getMovementState() {
    return switch (state) {
      case "PENDING" -> MovementState.PENDING;
      case "PROCESSING" -> MovementState.PROCESSING;
      case "COMPLETED" -> MovementState.LIQUIDATED;
      case "CANCELED" -> MovementState.CANCELLED;
      case "FAILED" -> MovementState.FAILED;
      default -> throw new IllegalArgumentException("Unknown state: " + state);
    };
  }

  @Override
  public LocalDate getSettlementAt() {
    return getSettlementAt(workingDayService);
  }

  public LocalDate getSettlementAt(WorkingDayService workingDayService) {
    return getFinishedAt()
        .map(LocalDateTime::toLocalDate)
        .orElseGet(
            () ->
                workingDayService.nextAvailableDay(
                    getProcessedAt().orElse(getCreatedAt()).toLocalDate(), 1));
  }

  @Override
  public LocalDate getAgreementAt() {
    return getCreatedAt().toLocalDate();
  }

  @Override
  public String getOperationId() {
    return id.toString() + "-portfolio_operations";
  }

  @Override
  public String getDescription() {
    return "Dollar mep portfolio operation of type " + type + " with id: " + id;
  }

  @Override
  public OperationType getOperationType() {
    return switch (type) {
      case "PURCHASE_MEP" -> OperationType.CREDIT;
      default -> OperationType.DEBIT;
    };
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return switch (type) {
      case "PURCHASE_MEP" -> MarketOperationType.COMPRA_MEP;
      case "TRANSFER_MEP" -> MarketOperationType.TRANSFER_MEP;
      case "SELL_MEP" -> MarketOperationType.VENTA_MEP;
      default -> throw new IllegalArgumentException("Unknown type: " + type);
    };
  }

  @Override
  public String getCurrency() {
    return dolarMepName;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return getDollarAmount();
  }

  @Override
  public BigDecimal getNetAmount() {
    return getDollarAmount();
  }

  @Override
  public BigDecimal getQuantity() {
    return getDollarAmount();
  }

  public BigDecimal getDollarAmount() {
    return getMepPurchaseBody().map(MepPurchaseDetailsResponse::getDollarAmount).orElse(quantity);
  }

  @Override
  public MovementType getMovementType() {
    return DOLLAR_MEP;
  }

  @Override
  public Long getId() {
    return id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
