package ar.com.daruma.bymas.movixEvent.domain.investmentfundsIntentions.entities;

public enum FundTransactionIntentionState {
  ADDED("ADDED"),
  LIQUIDATED("LIQUIDATED"),
  PARTIALLY_LIQUIDATED("PARTIALLY_LIQUIDATED"),
  PENDING_SETTLEMENT("PENDING_SETTLEMENT"),
  PENDING_NOTIFY("PENDING_NOTIFY"),
  COMPLETED("COMPLETED"),
  CANCELLED("CANCELLED"),
  FAILED("FAILED");
  private final String description;

  FundTransactionIntentionState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }
}
