package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.TRANSFER;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.DEBIT;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BulkTransactionResponse implements ExternalMovement {
  private Long id;
  private int accountId;
  private List<String> transactions;
  private TransactionState state;
  private @Nullable SignatureDataRequest signatureRequest;
  private @Nullable String signatureType;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-bulk_transaction";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(TRANSFER, this.state.toString());
  }

  @Override
  public LocalDate getAgreementAt() {
    return LocalDate.now();
  }

  @Override
  public LocalDate getSettlementAt() {
    return LocalDate.now();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getNetAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getQuantity() {
    return BigDecimal.ZERO;
  }

  @Override
  public String getDescription() {
    return "Bulk Transaction id: " + id;
  }

  @Override
  public MovementType getMovementType() {
    return TRANSFER;
  }

  @Override
  public OperationType getOperationType() {
    return DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return MarketOperationType.TRANSACTION_OUTBOUND;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public Long getId() {
    return id;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.of(accountId);
  }
}
