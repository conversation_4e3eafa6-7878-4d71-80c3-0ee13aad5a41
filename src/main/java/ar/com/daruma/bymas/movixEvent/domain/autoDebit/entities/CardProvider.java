package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum CardProvider {
  MASTERCARD,
  VISA,
  DINERS,
  MAESTRO,
  AMERICAN_EXPRESS;

  public static CardProvider fromString(String value) {
    for (CardProvider p : CardProvider.values()) {
      if (p.name().equalsIgnoreCase(value)) {
        return p;
      }
    }
    throw new IllegalArgumentException("Invalid Provider: " + value);
  }
}
