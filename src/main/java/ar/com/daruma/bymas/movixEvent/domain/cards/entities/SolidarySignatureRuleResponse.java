package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import ar.com.daruma.citadel.model.global.ActiveState;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SolidarySignatureRuleResponse {
  private Integer id;
  private Integer signatureProfileId;
  private Integer userId;
  private String currency;
  private @Nullable BigDecimal amountLimit;
  private ActiveState state;
}
