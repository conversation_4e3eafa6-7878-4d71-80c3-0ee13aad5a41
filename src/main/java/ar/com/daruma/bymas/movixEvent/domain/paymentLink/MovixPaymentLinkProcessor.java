package ar.com.daruma.bymas.movixEvent.domain.paymentLink;

import ar.com.daruma.bymas.movixEvent.domain.GenericMovixMovementProcessor;
import ar.com.daruma.bymas.movixEvent.domain.paymentLink.entities.PaymentLinkResponse;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixPaymentLinkProcessor extends GenericMovixMovementProcessor<PaymentLinkResponse> {

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  public MovixPaymentLinkProcessor() {
    super("payment", PaymentLinkResponse.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    PaymentLinkResponse response = getDataFromJsonNode(movixRecord);
    response.setMovixRecord(movixRecord);
    if (movixRecord.isInsert()) {
      insert(response);
    } else {
      PortfolioMovement movement = findsertMovement(response.getOperationId(), response);
      update(response, movement);
    }
  }

  @Override
  protected PortfolioAsset assetToAffect() {
    return findPortfolioAssetByName.find(PortfolioQuotationCurrencyUtils.pesoName);
  }
}
