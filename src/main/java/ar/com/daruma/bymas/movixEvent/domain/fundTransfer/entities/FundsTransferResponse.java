package ar.com.daruma.bymas.movixEvent.domain.fundTransfer.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.FUNDS_TRANSFER;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.TRANSPASOS_DE_FONDOS;

import ar.com.daruma.bymas.movixEvent.domain.echeq.entities.SignatureData;
import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureType;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FundsTransferResponse implements ExternalMovement {
  private Integer id;
  private int accountId;
  private int allariaLinkId;
  private BigDecimal amount;
  private FundsTransferState state;
  private @Nullable Integer orderId;
  private int signatureRequestId;
  private @Nullable LocalDateTime approvedDateTime;
  private @Nullable SignatureData signatureData;
  private @Nullable SignatureType signatureType;
  private LocalDateTime createdAt;
  private int createdBy;
  private MovixRecord movixRecord;

  @Nullable
  public Integer getOrderId() {
    return orderId;
  }

  public void setOrderId(@Nullable Integer orderId) {
    this.orderId = orderId;
  }

  @Nullable
  public LocalDateTime getApprovedDateTime() {
    return approvedDateTime;
  }

  public void setApprovedDateTime(@Nullable LocalDateTime approvedDateTime) {
    this.approvedDateTime = approvedDateTime;
  }

  @Nullable
  public SignatureData getSignatureData() {
    return signatureData;
  }

  public void setSignatureData(@Nullable SignatureData signatureData) {
    this.signatureData = signatureData;
  }

  @Nullable
  public SignatureType getSignatureType() {
    return signatureType;
  }

  public void setSignatureType(@Nullable SignatureType signatureType) {
    this.signatureType = signatureType;
  }

  @Override
  public String getOperationId() {
    return id + "-funds_transfer";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(FUNDS_TRANSFER, this.state.getDescription());
  }

  @Override
  public LocalDate getAgreementAt() {
    return createdAt.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return createdAt.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public String getDescription() {
    return "funds_transfer id: " + id;
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return TRANSPASOS_DE_FONDOS;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return FUNDS_TRANSFER;
  }

  @Override
  public Long getId() {
    return this.id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
