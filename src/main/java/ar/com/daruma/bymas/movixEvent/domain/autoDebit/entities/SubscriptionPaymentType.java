package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum SubscriptionPaymentType {
  PROPORTIONAL,
  TOTAL;

  public static SubscriptionPaymentType fromString(String value) {
    for (SubscriptionPaymentType type : SubscriptionPaymentType.values()) {
      if (type.name().equalsIgnoreCase(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid SubscriptionPaymentType: " + value);
  }
}
