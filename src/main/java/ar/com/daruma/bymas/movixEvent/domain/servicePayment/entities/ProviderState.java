package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

public enum ProviderState {
  ACTIVE("ACTIVE"),
  INACTIVE("INACTIVE");

  private final String description;

  ProviderState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public static ProviderState fromDescription(String description) {
    for (ProviderState state : values()) {
      if (state.description.equalsIgnoreCase(description)) {
        return state;
      }
    }
    throw new IllegalArgumentException("Unknown ProviderState: " + description);
  }
}
