package ar.com.daruma.bymas.movixEvent.domain.allariaMasAccount.entities;

import ar.com.daruma.bymas.allariaMas.accounts.domain.AllariaMasAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Account implements AllariaMasAccount {

  private Integer id;
  private String name;
  private String personType;
  private String currency;
  private BigDecimal availableCash;
  private BigDecimal lockedCash;
  private BigDecimal borrowedCash;
  private BigDecimal internalCash;
  private BigDecimal pendingFundsCash;
  private BigDecimal pendingSubscriptionCash;
  private BigDecimal allariaCash;
  private BigDecimal mirroredAdvancedCash;
  private BigDecimal lockedAdvancedCash;
  private BigDecimal shares;
  private BigDecimal internalMirroredAdvancedCash;

  @Schema(hidden = true)
  private UpdateAudit updateAudit;

  @Schema(hidden = true)
  private BigDecimal balance(BigDecimal lastSettlementValue) {
    BigDecimal amountInShares = shares.multiply(lastSettlementValue);
    BigDecimal positiveBalance =
        availableCash.add(internalCash).add(amountInShares).add(allariaCash);
    BigDecimal negativeBalance =
        borrowedCash
            .add(pendingFundsCash)
            .add(mirroredAdvancedCash)
            .add(internalMirroredAdvancedCash);
    return positiveBalance.subtract(negativeBalance).setScale(2, RoundingMode.HALF_UP);
  }

  @Schema(hidden = true)
  @Override
  public BigDecimal getAgreementQuantity(BigDecimal lastSettlementValue) {
    return balance(lastSettlementValue);
  }

  @Schema(hidden = true)
  public Boolean isPhysical() {
    return personType.equals("PHYSICAL");
  }

  @Schema(hidden = true)
  @Override
  public BigDecimal getSettlementQuantity(BigDecimal lastSettlementValue) {
    return balance(lastSettlementValue);
  }

  @Override
  public BigDecimal getAvailableQuantity() {
    return availableCash;
  }

  @Override
  public BigDecimal getLockedQuantity() {
    return lockedCash;
  }
}
