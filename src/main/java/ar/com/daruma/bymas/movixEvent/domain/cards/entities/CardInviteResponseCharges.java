package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardInviteResponseCharges {
  private BigDecimal charges;
  private String currency;
  private List<TaxResponse> taxes;
  private LocalDateTime creationDateTime;

  public BigDecimal totalTaxesAmount() {
    if (taxes == null || taxes.isEmpty()) {
      return BigDecimal.ZERO;
    }

    return taxes.stream()
        .filter(tax -> tax != null && tax.getAmount() != null)
        .map(TaxResponse::getAmount)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }
}
