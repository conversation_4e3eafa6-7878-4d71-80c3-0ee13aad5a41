package ar.com.daruma.bymas.movixEvent.domain;

import ar.com.daruma.bymas.movixEvent.domain.exceptions.MovixBadRequestException;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.utils.objectMapper.ObjectMapperUtils;

public abstract class MovixProcessor<T> {

  public abstract void execute(MovixRecord movixRecord);

  private final Class<T> genericType;
  protected final String processorName;

  public MovixProcessor(String processorName, Class<T> genericType) {
    this.processorName = processorName;
    this.genericType = genericType;
  }

  protected T getDataFromJsonNode(MovixRecord movixRecord) {
    try {
      return ObjectMapperUtils.INSTANCE.readValue(movixRecord.getData().toString(), genericType);
    } catch (Exception e) {
      throw new MovixBadRequestException(
          "Failed to parse %s JSON because of error: %s".formatted(processorName, e.toString()));
    }
  }
}
