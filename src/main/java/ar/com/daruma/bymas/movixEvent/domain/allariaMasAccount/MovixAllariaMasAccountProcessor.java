package ar.com.daruma.bymas.movixEvent.domain.allariaMasAccount;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaMasCompany;

import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaMasAccount.entities.Account;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePesoPortfolioPositionByAllariaMasAccount;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixAllariaMasAccountProcessor extends MovixProcessor<Account> {

  @Autowired
  private FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Autowired
  private UpdatePesoPortfolioPositionByAllariaMasAccount
      updatePesoPortfolioPositionByAllariaMasAccount;

  public MovixAllariaMasAccountProcessor() {
    super("accounts", Account.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    Account response = getDataFromJsonNode(movixRecord);
    upsert(response);
  }

  private void upsert(Account account) {
    PortfolioAccount portfolioAccount = findPortfolioAccountByAccount(account);
    updatePesoPortfolioPositionByAllariaMasAccount.update(
        account, portfolioAccount, Optional.empty());
  }

  private PortfolioAccount findPortfolioAccountByAccount(Account account) {
    return findsertPortfolioAccountByAccountNumber.findsert(
        account.getId(), account.getName(), account.isPhysical(), allariaMasCompany);
  }
}
