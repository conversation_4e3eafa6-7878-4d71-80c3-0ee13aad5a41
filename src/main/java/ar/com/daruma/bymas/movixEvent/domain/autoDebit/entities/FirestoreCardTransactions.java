package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FirestoreCardTransactions {
  private Integer id;
  private Integer paymentId;
  private Integer paymentMethodId;
  @Nullable private Integer paywayId;
  @Nullable private Integer paymentLinkId;
  private Integer accountId;
  private Integer subscriptionId;
  @Nullable private FirestoreCardData card;
  private ProviderType providerType;
  private PaymentTransactionType paymentTransactionType;
  private PaywayPaymentTransactionState state;
  @Nullable private String errorCode;
  @Nullable private String errorDetails;
  private LocalDateTime transactionDate;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
}
