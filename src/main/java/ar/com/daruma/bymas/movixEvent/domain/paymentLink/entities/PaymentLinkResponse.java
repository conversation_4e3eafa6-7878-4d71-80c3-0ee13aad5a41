package ar.com.daruma.bymas.movixEvent.domain.paymentLink.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.PAYMENT_LINK;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentLinkResponse implements ExternalMovement {
  private Long id;
  private Integer accountId;
  private PaymentLinkState state;
  private LinkData linkData;
  private LiquidationData liquidationData;
  private @Nullable RefundData maybePaymentRefund;
  private PaymentMethodData cardData;
  private GatewayData gatewayData;
  private CustomerData customerData;
  private int installments;
  private LocalDateTime creationDateTime;
  private String originPaymentLink;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-payment_link";
  }

  @Override
  public String getDescription() {
    return "Payment link id:" + id;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return liquidationData.getGrossAmount();
  }

  @Override
  public BigDecimal getTaxAmount() {
    return Optional.ofNullable(liquidationData.getFinalTaxesAmount()).orElse(BigDecimal.ZERO);
  }

  @Override
  public BigDecimal getFeeAmount() {
    return liquidationData.getTotalFeeWithVat();
  }

  @Override
  public BigDecimal getNetAmount() {
    return Optional.ofNullable(liquidationData.getFinalNetAmount()).orElse(BigDecimal.ZERO);
  }

  @Override
  public BigDecimal getQuantity() {
    return liquidationData.getGrossAmount();
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return MovementType.PAYMENT_LINK;
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(PAYMENT_LINK, this.state.toString());
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return MarketOperationType.PAYMENT_LINK;
  }

  @Override
  public LocalDate getAgreementAt() {
    return getCreationDateTime().toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return liquidationData.getEstimatedLiquidationDateTime().toLocalDate();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
