package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import ar.com.daruma.citadelsdk.model.signme.response.JointSignatureRule;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureType;
import ar.com.daruma.citadelsdk.model.signme.response.SolidarySignatureRule;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignatureDataWithType {
  private int signatureRequestId;
  private int solicitorId;
  private SignatureState state;
  private Set<User> signers;
  private Set<User> potentialSigners;
  private @Nullable SignatureType signatureType;
  private LocalDateTime createdAt;
  private @Nullable SolidarySignatureRule maybeAppliedSolidaryRule;
  private @Nullable JointSignatureRule maybeAppliedJointRule;

  @Nullable
  public SignatureType getSignatureType() {
    return signatureType;
  }

  public void setSignatureType(@Nullable SignatureType signatureType) {
    this.signatureType = signatureType;
  }
}
