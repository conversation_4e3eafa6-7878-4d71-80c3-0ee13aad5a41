package ar.com.daruma.bymas.movixEvent.domain.returns.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.RETURN;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.CREDIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.RETURNS;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnsResponse implements ExternalMovement {
  @Getter(AccessLevel.NONE)
  private Integer id;

  private int accountId;
  private LocalDate date;
  private BigDecimal amount;
  private String currency;
  private Long settlementId;
  private LocalDateTime creationDateTime;
  private int createdBy;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-returns";
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public String getDescription() {
    return "Returns from MM id: " + id;
  }

  @Override
  public MovementState getMovementState() {
    return MovementState.COMPLETED;
  }

  @Override
  public LocalDate getAgreementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return date;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public OperationType getOperationType() {
    return CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return RETURNS;
  }

  @Override
  public MovementType getMovementType() {
    return RETURN;
  }

  @Override
  public Long getId() {
    return id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
