package ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities;

import ar.com.daruma.citadel.model.global.ActiveState;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllariaLinks {

  @JsonProperty("id")
  private String id;

  @JsonProperty("account_id")
  private Integer accountId;

  @JsonProperty("allaria_account_id")
  private Integer allariaAccountId;

  @JsonProperty("user_id")
  private Integer userId;

  @JsonProperty("state")
  private String state;

  @JsonProperty("allaria_account_owners_emails")
  private String allariaAccountOwnersEmails;

  @JsonProperty("user_expiration_date_time")
  private LocalDateTime userExpirationDateTime;

  @JsonProperty("creation_date_time")
  private LocalDateTime creationDateTime;

  @JsonProperty("created_by")
  private Integer createdBy;

  @JsonProperty("last_modification_date_time")
  private LocalDateTime lastModificationDateTime;

  @JsonProperty("last_modified_by")
  private Integer lastModifiedBy;

  public ActiveState getActiveState() {
    if (state.equals("APPROVED")) {
      return ActiveState.ACTIVE;
    }
    return ActiveState.INACTIVE;
  }
}
