package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

public enum TransactionConcept {
  ALQUILER("Alquiler"),
  CUOTA("Cuota"),
  EXPENSAS("Expensas"),
  FACTURA("Factura"),
  PRESTAMO("Préstamo"),
  SEGURO("Seguro"),
  HONORARIOS("Honorarios"),
  HABERES("Haberes"),
  VARIOS("Varios");

  private final String description;

  TransactionConcept(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }
}
