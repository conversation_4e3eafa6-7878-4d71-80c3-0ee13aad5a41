package ar.com.daruma.bymas.movixEvent.domain.allariaInboundTransaction.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.TRANSFER;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.CREDIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.TRANSACTION_INBOUND;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AllariaInboundTransactionResponse implements ExternalMovement {
  private Long id;
  private Integer accountId;
  private String state;
  private Integer transactionId;
  private @Nullable String collectReceiptNumber;
  private String description;
  private BigDecimal amount;
  private String originCxu;
  private @Nullable String originCuit;
  private List<AllariaAccountsPerCuit> allariaAccountsPerCuit;
  private @Nullable String destinationAllariaAccount;
  private LocalDateTime transactionDateTime;
  private LocalDateTime createAudit;
  private LocalDateTime updateAudit;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-allaria_inbound_transactions";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(TRANSFER, this.state);
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.transactionDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.transactionDateTime.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.amount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return this.amount;
  }

  @Override
  public OperationType getOperationType() {
    return CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return TRANSACTION_INBOUND;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return MovementType.TRANSFER;
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }
}
