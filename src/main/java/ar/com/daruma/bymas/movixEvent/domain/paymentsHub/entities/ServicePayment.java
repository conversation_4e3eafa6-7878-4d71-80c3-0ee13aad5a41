package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServicePayment extends Invoice {

  private String paymentCode;
  private String taxName;
  private String taxCollector;
  private BigDecimal firstExpirationAmount;
  private LocalDateTime firstExpirationDate;
  private @Nullable BigDecimal secondExpirationAmount;
  private @Nullable LocalDateTime secondExpirationDate;
}
