package ar.com.daruma.bymas.movixEvent.domain.allariaLinks;

import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.allariaLinks.entities.AllariaLinks;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAccountRelationships.application.upsert.UpsertPortfolioAccountRelationshipsByAllariaLinks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixAllariaLinksProcessor extends MovixProcessor<AllariaLinks> {

  @Autowired
  private UpsertPortfolioAccountRelationshipsByAllariaLinks
      upsertPortfolioAccountRelationshipsByAllariaLinks;

  public MovixAllariaLinksProcessor() {
    super("allaria_links", AllariaLinks.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    AllariaLinks response = getDataFromJsonNode(movixRecord);
    upsertPortfolioAccountRelationshipsByAllariaLinks.upsert(response);
  }
}
