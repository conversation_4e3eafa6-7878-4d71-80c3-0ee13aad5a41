package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum PaymentOrigin {
  AUTOMATIC_DEBIT,
  PAYMENT_LINK;

  public static PaymentOrigin fromString(String value) {
    for (PaymentOrigin origin : PaymentOrigin.values()) {
      if (origin.name().equalsIgnoreCase(value)) {
        return origin;
      }
    }
    throw new IllegalArgumentException("Invalid PaymentOrigin: " + value);
  }
}
