package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

import ar.com.daruma.citadelsdk.model.signme.response.RuleSegment;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignerResponseForPayment {
  private int id;
  private String email;
  private String name;
  private String lastName;
  private List<RuleSegment> segments;
}
