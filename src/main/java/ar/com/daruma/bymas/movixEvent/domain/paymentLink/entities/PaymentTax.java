package ar.com.daruma.bymas.movixEvent.domain.paymentLink.entities;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentTax {
  private int id;
  private int paymentId;
  private String taxType;
  private @Nullable String regimeType;
  private String stage;
  private BigDecimal aliquot;
  private BigDecimal amount;
  private @Nullable String explanation;
}
