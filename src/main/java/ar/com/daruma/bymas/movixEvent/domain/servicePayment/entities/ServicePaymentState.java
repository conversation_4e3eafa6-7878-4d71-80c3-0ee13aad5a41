package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import java.util.Optional;

public enum ServicePaymentState {
  PARTIALLY_APPROVED("PARTIALLY_APPROVED"),
  PARTIALLY_APPROVED_BY_BULK("PARTIALLY_APPROVED_BY_BULK"),
  PENDING_APPROVAL("PENDING_APPROVAL"),
  PENDING_APPROVAL_BY_BULK("PENDING_APPROVAL_BY_BULK"),
  CANCELED("CANCELED"),
  CANCELLED_BY_BULK("CANCELLED_BY_BULK"),
  FAILED("FAILED"),
  FAILED_BY_BULK("FAILED_BY_BULK"),
  APPROVED_NO_FUNDS("APPROVED_NO_FUNDS"),
  APPROVED_LOCKED("APPROVED_LOCKED"),
  SERVICE_PROVIDER_SUCCESS("SERVICE_PROVIDER_SUCCESS"),
  SERVICE_PROVIDER_SUCCESS_BY_BULK("SERVICE_PROVIDER_SUCCESS_BY_BULK"),
  SERVICE_PROVIDER_UNKNOWN("SERVICE_PROVIDER_UNKNOWN"),
  SERVICE_PROVIDER_UNKNOWN_BY_BULK("SERVICE_PROVIDER_UNKNOWN_BY_BULK"),
  COMPLETED("COMPLETED"),
  COMPLETED_BY_BULK("COMPLETED_BY_BULK"),
  APPROVED_BY_BULK("APPROVED_BY_BULK");

  private final String description;

  ServicePaymentState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public static Optional<ServicePaymentState> fromDescription(String description) {
    for (ServicePaymentState state : values()) {
      if (state.description.equalsIgnoreCase(description)) {
        return Optional.of(state);
      }
    }
    return Optional.empty();
  }
}
