package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.PROGRAMMED_PAYMENT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.PAGO_SERVICIO;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureType;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgrammedPaymentResponse implements ExternalMovement {
  private Long id;
  private int accountId;
  private int serviceProviderId;
  private String clientId;
  private @Nullable String invoiceId;
  private IntentionState state;
  private BigDecimal amount;
  private BigDecimal taxAmount;
  private BigDecimal totalAmount;
  private LocalDateTime dueDate;
  private LocalDateTime createdAt;
  private @Nullable SignatureDataWithType signatureData;
  private @Nullable SignatureType signatureType;
  private @Nullable String description;
  private ServiceProvider serviceProvider;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-programmed_payment";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(PROGRAMMED_PAYMENT, this.state.getDescription());
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.createdAt.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.createdAt.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.taxAmount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return this.amount;
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return PAGO_SERVICIO;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return MovementType.PROGRAMMED_PAYMENT;
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.of(accountId);
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }
}
