package ar.com.daruma.bymas.movixEvent.presentation.bodies;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;

@Data
public class MovixRecord {
  @Valid private MovixMetadata metadata;
  @Valid private JsonNode data;

  @Schema(hidden = true)
  public boolean isInsert() {
    return metadata.isInsert();
  }
}
