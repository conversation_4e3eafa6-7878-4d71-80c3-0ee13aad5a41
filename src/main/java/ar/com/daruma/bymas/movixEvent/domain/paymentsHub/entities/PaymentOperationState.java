package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

public enum PaymentOperationState {
  COMPLETED("COMPLETED"),
  FAILED("FAILED"),
  ABORTED("ABORTED"),
  PENDING_APPROVAL("PENDING_APPROVAL"),
  APPROVED("APPROVED"),
  PARTIALLY_APPROVED("PARTIALLY_APPROVED"),
  PENDING_FUNDS("PENDING_FUNDS"),
  EXPIRED("EXPIRED");
  private final String description;

  PaymentOperationState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }
}
