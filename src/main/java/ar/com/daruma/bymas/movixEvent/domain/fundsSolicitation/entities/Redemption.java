package ar.com.daruma.bymas.movixEvent.domain.fundsSolicitation.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Redemption {
  private Long id;
  private String allariaLinkId;
  private BigDecimal amount;
  private Boolean exempt;
  private LocalDateTime creationDateTime;
  private LocalDateTime lastModificationDateTime;
  private String lastModifiedBy;
  private String createdBy;
  private @Nullable String rejectionReason;
  private String requestedBy;
  private String state;
}
