package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import lombok.*;
import org.springframework.lang.Nullable;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class PaymentLiquidationData {
  private BigDecimal darumaFeeAmount;
  private BigDecimal darumaFeeVatAmount;
  private BigDecimal prismaFeeAmount;
  private BigDecimal prismaFeeVatAmount;
  private BigDecimal totalFeeVatAmount;
  private LocalDate estimatedLiquidationAt;

  @Nullable
  @Getter(AccessLevel.NONE)
  private BigDecimal estimatedNetAmount;

  @Nullable
  @Getter(AccessLevel.NONE)
  private BigDecimal finalNetAmount;

  @Nullable
  @Getter(AccessLevel.NONE)
  private BigDecimal estimatedTaxesAmount;

  @Nullable
  @Getter(AccessLevel.NONE)
  private BigDecimal finalTaxesAmount;

  @Nullable
  @Getter(AccessLevel.NONE)
  private LocalDate finalLiquidationAt;

  public Optional<BigDecimal> getEstimatedNetAmount() {
    return Optional.ofNullable(estimatedNetAmount);
  }

  public Optional<BigDecimal> getFinalNetAmount() {
    return Optional.ofNullable(finalNetAmount);
  }

  public Optional<BigDecimal> getEstimatedTaxesAmount() {
    return Optional.ofNullable(estimatedTaxesAmount);
  }

  public Optional<BigDecimal> getFinalTaxesAmount() {
    return Optional.ofNullable(finalTaxesAmount);
  }

  public Optional<LocalDate> getFinalLiquidationAt() {
    return Optional.ofNullable(finalLiquidationAt);
  }
}
