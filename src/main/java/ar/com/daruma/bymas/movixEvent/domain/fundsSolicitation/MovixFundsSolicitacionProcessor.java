package ar.com.daruma.bymas.movixEvent.domain.fundsSolicitation;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.pesoName;

import ar.com.daruma.bymas.movixEvent.domain.GenericMovixMovementProcessor;
import ar.com.daruma.bymas.movixEvent.domain.fundsSolicitation.entities.FundsSolicitationResponse;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixFundsSolicitacionProcessor
    extends GenericMovixMovementProcessor<FundsSolicitationResponse> {

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  public MovixFundsSolicitacionProcessor() {
    super("allaria_redemptions", FundsSolicitationResponse.class);
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    FundsSolicitationResponse response = getDataFromJsonNode(movixRecord);
    response.setMovixRecord(movixRecord);
    if (movixRecord.isInsert()) {
      insert(response);
    } else {
      PortfolioMovement movement = findsertMovement(response.getOperationId(), response);
      update(response, movement);
    }
  }

  @Override
  protected PortfolioAsset assetToAffect() {
    return findPortfolioAssetByName.find(pesoName);
  }
}
