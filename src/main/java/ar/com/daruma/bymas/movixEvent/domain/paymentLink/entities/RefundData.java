package ar.com.daruma.bymas.movixEvent.domain.paymentLink.entities;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefundData {
  private LocalDateTime refundDateTime;
  private String refundType;
  private double grossAmount;
  private double totalFeeWithVat;
  private @Nullable double totalFeeWithVatAndTaxes;
  private @Nullable double finalNetAmount;
  private @Nullable double finalTaxesAmount;
}
