package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Vep extends Invoice {

  private String vepCode;
  private @Nullable String cuitGenerator;
  private String cuitTaxPayer;
  private String taxCollector;
  private String paymentType;
  private @Nullable String concept;
  private @Nullable String subConcept;
  private String paymentGateway;
  private LocalDateTime generationDate;
  private LocalDateTime expiration;
  private LocalDateTime uploadDate;
  private BigDecimal amount;
  private List<VepService> vepServices;
}
