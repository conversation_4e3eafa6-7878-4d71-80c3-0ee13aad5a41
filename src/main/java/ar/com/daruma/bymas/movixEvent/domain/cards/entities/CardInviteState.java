package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum CardInviteState {
  PENDING("PENDING"),
  PARTIALLY_APPROVED("PARTIALLY_APPROVED"),
  APPROVED("APPROVED"),
  REJECTED("REJECTED"),
  EXPIRED("EXPIRED"),
  PENDING_VALIDATION("PENDING_VALIDATION"),
  VALIDATION_FAILED("VALIDATION_FAILED"),
  REJECTED_VALIDATION("REJECTED_VALIDATION");
  private final String description;

  CardInviteState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static CardInviteState fromDisplayName(String displayName) {
    for (CardInviteState ci : CardInviteState.values()) {
      if (ci.description.equals(displayName)) {
        return ci;
      }
    }
    throw new IllegalArgumentException("No enum constant with display name: " + displayName);
  }
}
