package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum IntentionState {
  PARTIALLY_APPROVED("PARTIALLY_APPROVED"),
  PENDING_APPROVAL("PENDING_APPROVAL"),
  APPROVED("APPROVED"),
  CA<PERSON><PERSON>LED("CANCELLED"),
  FAILED("FAILED"),
  APPROVED_NO_FUNDS("APPROVED_NO_FUNDS"),
  COMPLETED("COMPLETED");

  private final String description;

  IntentionState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  @JsonCreator
  public static IntentionState fromDisplayName(String displayName) {
    for (IntentionState is : IntentionState.values()) {
      if (is.description.equals(displayName)) {
        return is;
      }
    }
    throw new IllegalArgumentException("No enum constant with display name: " + displayName);
  }
}
