package ar.com.daruma.bymas.movixEvent.domain.echeq.entities;

import ar.com.daruma.citadel.model.global.ActiveState;
import ar.com.daruma.citadelsdk.model.signme.response.JointSignatureRule;
import ar.com.daruma.citadelsdk.model.signme.response.Signer;
import ar.com.daruma.citadelsdk.model.signme.response.SolidarySignatureRule;
import java.time.LocalDateTime;
import java.util.List;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignatureData {

  private int signatureRequestId;
  private int solicitorId;
  private ActiveState state;
  private List<Signer> signers;
  private List<Signer> potentialSigners;
  private LocalDateTime createdAt;
  private SolidarySignatureRule maybeAppliedSolidaryRule;
  private JointSignatureRule maybeAppliedJointRule;
}
