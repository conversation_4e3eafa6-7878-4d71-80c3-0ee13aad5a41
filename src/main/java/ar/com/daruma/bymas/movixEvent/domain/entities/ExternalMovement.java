package ar.com.daruma.bymas.movixEvent.domain.entities;

import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementPositionEffect.AFFECTS_BALANCE;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementVisibility.IS_SHOWN;

import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.*;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

public interface ExternalMovement {

  String getOperationId();

  MovementState getMovementState();

  LocalDate getAgreementAt();

  LocalDate getSettlementAt();

  BigDecimal getFeeAmount();

  BigDecimal getTaxAmount();

  BigDecimal getGrossAmount();

  BigDecimal getNetAmount();

  BigDecimal getQuantity();

  String getDescription();

  OperationType getOperationType();

  MarketOperationType getMarketOperationType();

  String getCurrency();

  MovementType getMovementType();

  Long getId();

  JsonNode getDataAsJson();

  Optional<Integer> getAccountId();

  default MovementProcessState getMovementProcessState(WorkingDayService workingDayService) {
    return MovementProcessState.fromLocalDate(getSettlementAt(), workingDayService);
  }

  default MovementPositionEffect getPositionEffect() {
    return AFFECTS_BALANCE;
  }

  default MovementVisibility getMovementVisibility() {
    return IS_SHOWN;
  }
}
