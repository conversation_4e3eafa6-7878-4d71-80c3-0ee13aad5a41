package ar.com.daruma.bymas.movixEvent.domain;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.domain.error.AccountNotDefinedException;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.application.create.CreatePortfolioMovementFromExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.application.find.FindMovementByOperationId;
import ar.com.daruma.bymas.portfolioMovement.application.update.UpdatePortfolioMovementByExternalMovement;
import ar.com.daruma.bymas.portfolioMovement.application.validations.ValidateNoPortfolioMovementExistsForOperationId;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByAllariaMasMovement;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

public abstract class GenericMovixMovementProcessor<T> extends MovixProcessor<T> {

  private static final CustomLogger logger = CustomLogger.getLogger(MovixProcessor.class);
  @Autowired private FindMovementByOperationId findMovementByOperationId;

  @Autowired
  private FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  @Autowired
  private CreatePortfolioMovementFromExternalMovement createPortfolioMovementFromExternalMovement;

  @Autowired
  private UpdatePortfolioPositionByAllariaMasMovement updatePortfolioPositionByAllariaMasMovement;

  @Autowired
  private UpdatePortfolioMovementByExternalMovement updatePortfolioMovementByExternalMovement;

  @Autowired
  private ValidateNoPortfolioMovementExistsForOperationId
      validateNoPortfolioMovementExistsForOperationId;

  public GenericMovixMovementProcessor(String processorName, Class<T> genericType) {
    super(processorName, genericType);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  protected PortfolioMovement findsertMovement(String operationId, ExternalMovement response) {
    logger.info("Validating portfolio movement exists for operation ID: {}", operationId);

    PortfolioMovement portfolioMovement =
        findMovementByOperationId
            .findMaybeMovement(operationId)
            .orElseGet(
                () -> {
                  logger.info(
                      "No existing portfolio movement found for operation ID: {}. A new one will be created.",
                      operationId);
                  return insert(response);
                });

    logger.info(
        "Portfolio movement found for operation ID: {} with id: {}",
        operationId,
        portfolioMovement.getId());

    return portfolioMovement;
  }

  protected PortfolioMovement insert(ExternalMovement response) {
    logger.info(
        "Inserting event for process [{}] with id [{}] for accountId [{}], operation-type [{}] and state [{}]",
        processorName,
        response.getOperationId(),
        response.getAccountId(),
        response.getOperationType(),
        response.getMovementState());
    validateIfMovementAlreadyExists(response);
    PortfolioAsset asset = assetToAffect();
    PortfolioAccount account =
        findsertPortfolioAccountByAccountNumber.findsert(
            response
                .getAccountId()
                .orElseThrow(
                    () ->
                        new AccountNotDefinedException(processorName, response.getOperationId())));
    PortfolioMovement movement =
        createPortfolioMovementFromExternalMovement.create(response, account, asset);
    updatePortfolioPositionByAllariaMasMovement.update(movement, false);
    logger.info(
        "Created portfolio movement [{}] by process [{}] for accountId [{}], operation-type [{}] and state [{}]",
        movement.getOperationId(),
        processorName,
        response.getAccountId(),
        response.getOperationType(),
        response.getMovementState());
    return movement;
  }

  private void validateIfMovementAlreadyExists(ExternalMovement movement) {
    validateNoPortfolioMovementExistsForOperationId.validate(movement.getOperationId());
  }

  protected void update(ExternalMovement response, PortfolioMovement movement) {
    logger.info(
        "Updating portfolio movement [{}] by process [{}] for accountId [{}], operation-type [{}] and state [{}]",
        movement.getOperationId(),
        processorName,
        response.getAccountId(),
        response.getOperationType(),
        response.getMovementState());
    PortfolioAccount account =
        findsertPortfolioAccountByAccountNumber.findsert(
            response
                .getAccountId()
                .orElseThrow(
                    () ->
                        new AccountNotDefinedException(processorName, response.getOperationId())));
    updatePortfolioPositionByAllariaMasMovement.update(movement, true);
    PortfolioMovement updatedMovement =
        updatePortfolioMovementByExternalMovement.execute(movement, response, false, account);
    updatePortfolioPositionByAllariaMasMovement.update(updatedMovement, false);
    logger.info(
        "Successfully updated portfolio movement [{}] by process [{}] for accountId [{}], operation-type [{}] and state [{}]",
        updatedMovement.getOperationId(),
        processorName,
        response.getAccountId(),
        response.getOperationType(),
        response.getMovementState());
  }

  protected abstract PortfolioAsset assetToAffect();
}
