package ar.com.daruma.bymas.movixEvent.domain.autoDebit;

import ar.com.daruma.bymas.movixEvent.domain.GenericMovixMovementProcessor;
import ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities.CroupierPaymentResponse;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixAutoDebitProcessor
    extends GenericMovixMovementProcessor<CroupierPaymentResponse> {

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  public MovixAutoDebitProcessor() {
    super("auto_debit_subscription_payments", CroupierPaymentResponse.class);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    CroupierPaymentResponse response = getDataFromJsonNode(movixRecord);
    response.setMovixRecord(movixRecord);
    if (movixRecord.isInsert()) {
      insert(response);
    } else {
      PortfolioMovement movement = findsertMovement(response.getOperationId(), response);
      update(response, movement);
    }
  }

  @Override
  protected PortfolioAsset assetToAffect() {
    return findPortfolioAssetByName.find(PortfolioQuotationCurrencyUtils.pesoName);
  }
}
