package ar.com.daruma.bymas.movixEvent.presentation.bodies;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class MovixMetadata {
  @NotNull private String operation;
  @NotNull private String tableName;
  @NotNull private LocalDateTime timestamp;

  @Schema(hidden = true)
  public boolean isInsert() {
    return operation.equalsIgnoreCase("insert");
  }
}
