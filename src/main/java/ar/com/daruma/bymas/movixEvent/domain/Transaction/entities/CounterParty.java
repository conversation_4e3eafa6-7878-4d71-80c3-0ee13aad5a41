package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

import javax.annotation.Nullable;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CounterParty {
  private int accountId;
  private String cuit;
  private String name;
  private PersonType personType;
  private String accountNumberType;
  private String accountNumber;
  private String bank;
  private String nickname;
  private @Nullable String email;
  private @Nullable String phoneNumber;

  @Nullable
  public String getEmail() {
    return email;
  }

  public void setEmail(@Nullable String email) {
    this.email = email;
  }

  @Nullable
  public String getPhoneNumber() {
    return phoneNumber;
  }

  public void setPhoneNumber(@Nullable String phoneNumber) {
    this.phoneNumber = phoneNumber;
  }
}
