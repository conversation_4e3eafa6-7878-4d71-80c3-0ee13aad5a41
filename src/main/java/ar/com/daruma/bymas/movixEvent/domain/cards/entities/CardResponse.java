package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardResponse {
  private Integer id;
  private FormatType formatType;
  private CardServiceProvider serviceProvider;
  private CardBrand provider;
  private ProductType productType;
  private CardState state;
  private @Nullable DeactivationReason deactivationReason;
  private @Nullable LocalDateTime startingDate;
  private @Nullable LocalDateTime activationAt;
  private @Nullable String lastFour;
  private CardConfigurationResponse cardConfiguration;
  private @Nullable ShipmentResponse shipment;
  private @Nullable Integer previousCardId;
}
