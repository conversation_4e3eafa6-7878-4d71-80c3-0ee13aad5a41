package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentResponse {
  private Integer id;
  private @Nullable String externalTrackingId;
  private @Nullable String trackingUrl;
  private @Nullable CourierCompany courierCompany;
  private ShipmentState status;
  private AddressResponse address;
}
