package ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount.entities;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MepPortfolioAccountResponse {
  private Integer id;
  private String externalId;
  private Integer accountId;
  private Integer instrumentId;
  private BigDecimal quantity;
  private BigDecimal lockedQuantity;
  private BigDecimal pendingLiquidationQuantity;
  private String state;
  private Integer createdBy;
  private LocalDateTime createdAt;
  private Integer updatedBy;
  private LocalDateTime updatedAt;

  public BigDecimal getAgreementQuantity() {
    return quantity.add(pendingLiquidationQuantity).subtract(lockedQuantity);
  }

  public BigDecimal getSettlementQuantity() {
    return quantity;
  }

  public BigDecimal getAvailableQuantity() {
    return getSettlementQuantity().subtract(getLockedQuantity());
  }
}
