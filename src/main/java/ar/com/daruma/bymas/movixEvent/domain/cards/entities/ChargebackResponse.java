package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import com.google.firebase.database.annotations.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class ChargebackResponse {
  private Integer id;
  private String providerTransactionId;
  private UserDetails requestedBy;
  private BigDecimal disputeAmount;
  private DisputeType disputeType;
  private DisputeReason disputeReason;
  private ChargebackStatus status;
  private String currency;
  private @Nullable String description;
  private LocalDateTime creationDateTime;
  private LocalDateTime updateDateTime;

  public void setDescription(@Nullable String description) {
    this.description = description;
  }

  @Nullable
  public String getDescription() {
    return description;
  }
}
