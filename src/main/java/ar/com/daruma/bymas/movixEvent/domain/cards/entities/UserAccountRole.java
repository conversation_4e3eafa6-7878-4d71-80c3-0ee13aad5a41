package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

public enum UserAccountRole {
  APPROVER(-1),
  LIMITED(0),
  WATCHER(1),
  MEMBER(3),
  ADMIN(5),
  OWNER(6),

  ACCOUNT_REGULAR(2), // Para retrocompatibilidad
  ACCOUNT_ADMIN(4); // Para retrocompatibilidad,

  private final int value;

  UserAccountRole(int value) {
    this.value = value;
  }

  public int getValue() {
    return value;
  }
}
