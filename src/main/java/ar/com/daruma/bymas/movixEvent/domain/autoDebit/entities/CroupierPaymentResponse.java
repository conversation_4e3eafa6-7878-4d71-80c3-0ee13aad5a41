package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.AUTOMATIC_DEBIT;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.CREDIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.RECIBO_COBRO;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.domain.exceptions.MovixBadRequestException;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.*;
import org.springframework.lang.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public final class CroupierPaymentResponse implements ExternalMovement {

  @Getter(AccessLevel.NONE)
  private Integer id;

  private String externalId;
  private Integer accountId;
  private PaymentResponseSubscriptionInformation plan;
  private PaymentResponseSubscriberInformation subscriber;
  private String state;
  private PaymentMethodType paymentMethodType;
  private PaymentOrigin paymentOrigin;

  @Getter(AccessLevel.NONE)
  private @Nullable FirestoreCardData card;

  private SubscriptionPaymentType subscriptionPaymentType;
  private BigDecimal amount;

  private String currency;

  @Getter(AccessLevel.NONE)
  private @Nullable String description;

  private LocalDate date;
  private PaymentLiquidationData liquidationData;
  private List<FirestoreCardTransactions> transactions;
  private List<FirestorePaymentTax> taxes;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private MovixRecord movixRecord;

  @Override
  public MovementState getMovementState() {
    try {
      return MovementState.fromString(state);
    } catch (IllegalArgumentException e) {
      throw new MovixBadRequestException("Invalid state: " + state);
    }
  }

  @Override
  public LocalDate getSettlementAt() {
    return liquidationData
        .getFinalLiquidationAt()
        .orElse(liquidationData.getEstimatedLiquidationAt());
  }

  @Override
  public LocalDate getAgreementAt() {
    return createdAt.toLocalDate();
  }

  @Override
  public BigDecimal getNetAmount() {
    return liquidationData
        .getFinalNetAmount()
        .orElse(liquidationData.getEstimatedNetAmount().orElse(amount));
  }

  @Override
  public BigDecimal getTaxAmount() {
    return liquidationData
        .getFinalTaxesAmount()
        .orElse(liquidationData.getEstimatedTaxesAmount().orElse(BigDecimal.ZERO));
  }

  @Override
  public BigDecimal getFeeAmount() {
    return liquidationData.getTotalFeeVatAmount();
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public String getOperationId() {
    return id.toString() + "-auto_debit_subscription_payments";
  }

  @Override
  public String getDescription() {
    return description != null ? description : "Auto Debit Payment: " + id;
  }

  @Override
  public OperationType getOperationType() {
    return CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return RECIBO_COBRO;
  }

  @Override
  public MovementType getMovementType() {
    return AUTOMATIC_DEBIT;
  }

  @Override
  public Long getId() {
    return id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
