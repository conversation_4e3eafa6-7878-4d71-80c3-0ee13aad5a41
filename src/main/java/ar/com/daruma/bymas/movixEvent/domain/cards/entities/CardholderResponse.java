package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardholderResponse {
  private Long id;
  private String name;
  private String surname;
  private String birthdate;
  private Gender gender;
  private String nationality;
  private IdentificationResponse identification;
  private TaxIdentificationResponse taxIdentification;
  private String email;
  private @Nullable String phoneNumber;
  private PepType pep;
  private AddressResponse address;
  private List<CardResponse> cards;
  private @Nullable String activity;
  private @Nullable MaritalStatus maritalStatus;
}
