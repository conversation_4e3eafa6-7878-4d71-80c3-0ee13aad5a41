package ar.com.daruma.bymas.movixEvent.domain.echeq.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.ECHEQ;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureType;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.firebase.database.annotations.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EcheqResponse implements ExternalMovement {

  @Getter(AccessLevel.NONE)
  private Integer id;

  private Long accountId;
  private String currency;
  private UUID externalCode;
  @Nullable private Integer wireId;
  private BigDecimal amount;
  private EcheqState state;
  private int signatureId;
  private SignatureData signatureRequest;
  private SignatureType signatureType;
  private LocalDateTime createdAt;
  private Long orderId;
  private int createdBy;
  private LocalDateTime modifiedAt;
  private int modifiedBy;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return externalCode + "-echeq";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(ECHEQ, this.state.toString());
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public String getDescription() {
    return "Echeq id: " + id;
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public LocalDate getAgreementAt() {
    return createdAt.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return createdAt.toLocalDate();
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return MarketOperationType.ECHEQ;
  }

  @Override
  public MovementType getMovementType() {
    return MovementType.ECHEQ;
  }

  @Override
  public Long getId() {
    return id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId.intValue());
  }
}
