package ar.com.daruma.bymas.movixEvent.application.processor;

import ar.com.daruma.bymas.configuration.movix.MovixConfig;
import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.generator.MovixProcessorGenerator;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.utils.discord.DiscordSender;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MovixWebhookProcessor extends DiscordSender {
  private static final CustomLogger logger = CustomLogger.getLogger(MovixWebhookProcessor.class);
  @Autowired private MovixConfig movixConfig;
  @Autowired private MovixProcessorGenerator movixProcessorGenerator;

  public void process(MovixRecord body) {
    if (movixConfig.getEnabled()) processEvent(body);
    else logger.info("Movix-webhook is disabled");
  }

  private void processEvent(MovixRecord body) {
    processRecord(body);
    logger.info(
        "Finished processing record for operation: [{}] in table: [{}]",
        body.getMetadata().getOperation(),
        body.getMetadata().getTableName());
  }

  private void processRecord(MovixRecord movixRecord) {
    MovixProcessor<?> processor =
        movixProcessorGenerator.getProcessorFromTable(movixRecord.getMetadata().getTableName());
    processor.execute(movixRecord);
  }
}
