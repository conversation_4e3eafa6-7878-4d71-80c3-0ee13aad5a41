package ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioOperations;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarMepName;

import ar.com.daruma.bymas.movixEvent.domain.GenericMovixMovementProcessor;
import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioOperations.entities.PortfolioOperationsResponse;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.PortfolioMovement;
import ar.com.daruma.bymas.utils.time.application.WorkingDayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MovixDolarMepPortfolioOperationProcessor
    extends GenericMovixMovementProcessor<PortfolioOperationsResponse> {

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  @Autowired private WorkingDayService workingDayService;

  public MovixDolarMepPortfolioOperationProcessor() {
    super("portfolio_operations", PortfolioOperationsResponse.class);
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    PortfolioOperationsResponse response = getDataFromJsonNode(movixRecord);
    response.setMovixRecord(movixRecord);
    response.setWorkingDayService(workingDayService);
    if (movixRecord.isInsert()) {
      insert(response);
    } else {
      PortfolioMovement movement = findsertMovement(response.getOperationId(), response);
      update(response, movement);
    }
  }

  @Override
  protected PortfolioAsset assetToAffect() {
    return findPortfolioAssetByName.find(dolarMepName);
  }
}
