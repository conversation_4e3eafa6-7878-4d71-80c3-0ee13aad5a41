package ar.com.daruma.bymas.movixEvent.domain.paymentsHub.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.INVOICE_OPERATION;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.PAGO_SERVICIO;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities.SignatureDataWithType;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceOperationDataResponse implements ExternalMovement {
  private Long id;
  private Integer accountId;
  private String externalCode;
  private String paymentCode;
  private String invoiceType;
  private Invoice invoice;
  private @Nullable String operationCode;
  private InvoiceOperationState status;
  private BigDecimal taxAmount;
  private BigDecimal debitedAmount;
  private BigDecimal paidAmount;
  private @Nullable LocalDate expirationDate;
  private LocalDateTime creationDateTime;
  private LocalDateTime lastModified;
  private SignatureDataWithType signatureDataWithType;
  private MovixRecord movixRecord;

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(this.accountId);
  }

  @Override
  public String getOperationId() {
    return id + "-invoice_operation";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(INVOICE_OPERATION, status.getDescription());
  }

  public LocalDate getAgreementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return this.taxAmount;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.debitedAmount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.paidAmount;
  }

  @Override
  public BigDecimal getQuantity() {
    return this.debitedAmount;
  }

  @Override
  public String getDescription() {
    return "Invoice operation id: " + id;
  }

  @Override
  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return PAGO_SERVICIO;
  }

  @Override
  public String getCurrency() {
    return "Pesos";
  }

  @Override
  public MovementType getMovementType() {
    return INVOICE_OPERATION;
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }
}
