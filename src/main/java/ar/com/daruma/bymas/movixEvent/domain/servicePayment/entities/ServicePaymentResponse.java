package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.SERVICE_PAYMENT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.PAGO_SERVICIO;

import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServicePaymentResponse implements ExternalMovement {
  private Long id;
  private int accountId;
  private String providerClientId;
  private @Nullable String invoiceId;
  private @Nullable String additionalData;
  private ServicePaymentState state;
  private BigDecimal paidAmount;
  private @Nullable LocalDateTime approvedDatetime;
  private BigDecimal taxAmount;
  private BigDecimal debitedAmount;
  private LocalDateTime liquidationDatetime;
  private ServiceProvider provider;
  private @Nullable SignatureDataWithType signatureData;
  private @Nullable LocalDateTime dueDate;
  private @Nullable String transactionNumber;
  private @Nullable String controlNumber;
  private LocalDateTime creationDateTime;
  private @Nullable String description;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-service_payment";
  }

  @Override
  public BigDecimal getGrossAmount() {
    return getPaidAmount();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getNetAmount() {
    return getDebitedAmount();
  }

  @Override
  public BigDecimal getQuantity() {
    return getPaidAmount();
  }

  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return SERVICE_PAYMENT;
  }

  public MovementState getMovementState() {
    return StateMapper.getMovementState(SERVICE_PAYMENT, this.state.getDescription());
  }

  public OperationType getOperationType() {
    return OperationType.DEBIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return PAGO_SERVICIO;
  }

  public LocalDate getAgreementAt() {
    return creationDateTime.toLocalDate();
  }

  public LocalDate getSettlementAt() {
    return liquidationDatetime.toLocalDate();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.ofNullable(accountId);
  }
}
