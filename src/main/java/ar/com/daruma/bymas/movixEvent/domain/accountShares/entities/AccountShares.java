package ar.com.daruma.bymas.movixEvent.domain.accountShares.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountShares {

  private Integer id;
  private Integer accountId;
  private Integer mutualFundId;
  private String state;
  private BigDecimal shares;
  private BigDecimal amountToDebit;
  private BigDecimal amountToCredit;
  private LocalDateTime createdAt;
  private Integer createdBy;
  private LocalDateTime updatedAt;
  private Integer updatedBy;

  public BigDecimal getAgreementQuantity() {
    return shares.add(amountToCredit).subtract(amountToDebit);
  }

  public BigDecimal getSettlementQuantity() {
    return shares;
  }

  public BigDecimal getLockedQuantity() {
    return amountToDebit;
  }

  public BigDecimal getAvailableQuantity() {
    return shares.subtract(amountToDebit);
  }
}
