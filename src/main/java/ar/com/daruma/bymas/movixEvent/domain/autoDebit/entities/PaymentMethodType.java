package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum PaymentMethodType {
  CARD,
  QR,
  PAYMENTLINK;

  public static PaymentMethodType fromString(String value) {
    for (PaymentMethodType type : PaymentMethodType.values()) {
      if (type.name().equalsIgnoreCase(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid PaymentMethodType: " + value);
  }
}
