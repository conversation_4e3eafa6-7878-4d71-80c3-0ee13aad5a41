package ar.com.daruma.bymas.movixEvent.domain.fundTransfer.entities;

public enum FundsTransferState {
  PENDING_APPROVAL("PENDING_APPROVAL"),
  PARTIALLY_SIGNED("PARTIALLY_SIGNED"),
  TRANSFERRED_PENDING_ORDER_CREATION("TRANSFERRED_PENDING_ORDER_CREATION"),
  COMPLETED("COMPLETED"),
  CANCELED("CANCELED"),
  UNKNOWN("UNKNOWN"),
  FAILED("FAILED");

  private final String description;

  FundsTransferState(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }
}
