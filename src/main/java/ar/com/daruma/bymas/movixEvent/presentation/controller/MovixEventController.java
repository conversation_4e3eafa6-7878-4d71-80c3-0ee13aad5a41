package ar.com.daruma.bymas.movixEvent.presentation.controller;

import ar.com.daruma.bymas.movixEvent.application.processor.MovixWebhookProcessor;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@ResponseBody
@RestController
@RequestMapping("/by-mas/movix")
public class MovixEventController {

  @Autowired private MovixWebhookProcessor movixWebhookProcessor;

  @PostMapping("")
  @BymasAuthorization
  public ResponseEntity<Void> execute(@RequestBody MovixRecord body) {
    movixWebhookProcessor.process(body);
    return ResponseEntity.ok().build();
  }
}
