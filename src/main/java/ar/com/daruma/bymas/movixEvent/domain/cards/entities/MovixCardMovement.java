package ar.com.daruma.bymas.movixEvent.domain.cards.entities;

import ar.com.daruma.bymas.movixEvent.domain.deserializer.MovixCardMovementDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

@Data
@JsonDeserialize(using = MovixCardMovementDeserializer.class)
public class MovixCardMovement {
  private Integer cardId;
  private Integer accountId;
  private Integer userId;
  private Integer cardMovementId;

  // TODO: add necessary fields
  public MovixCardMovement(
      Integer accountId, Integer userId, Integer cardId, Integer cardMovementId) {
    this.accountId = accountId;
    this.userId = userId;
    this.cardId = cardId;
    this.cardMovementId = cardMovementId;
  }
}
