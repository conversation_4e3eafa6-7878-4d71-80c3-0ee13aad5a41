package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

public enum AmountType {
  EQUAL_INVOICE("EQUAL_INVOICE"),
  GREATER_EQUAL_INVOICE("GREATER_EQUAL_INVOICE"),
  GREATER_LESS_EQUAL_INVOICE("GREATER_LESS_EQUAL_INVOICE");

  private final String description;

  AmountType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public static AmountType fromDescription(String description) {
    for (AmountType type : values()) {
      if (type.description.equalsIgnoreCase(description)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Unknown AmountType: " + description);
  }
}
