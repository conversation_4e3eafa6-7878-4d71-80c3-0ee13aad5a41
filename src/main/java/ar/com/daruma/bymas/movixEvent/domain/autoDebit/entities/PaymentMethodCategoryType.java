package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum PaymentMethodCategoryType {
  CREDIT,
  DEBIT,
  PREPAID,
  FOREIGN;

  public static PaymentMethodCategoryType fromString(String value) {
    for (PaymentMethodCategoryType type : PaymentMethodCategoryType.values()) {
      if (type.name().equalsIgnoreCase(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid PaymentMethodCategoryType: " + value);
  }
}
