package ar.com.daruma.bymas.movixEvent.domain.autoDebit.entities;

public enum PaywayPaymentTransactionState {
  APPROVED,
  FAILED,
  PENDING;

  public static PaywayPaymentTransactionState fromString(String value) {
    for (PaywayPaymentTransactionState state : PaywayPaymentTransactionState.values()) {
      if (state.name().equalsIgnoreCase(value)) {
        return state;
      }
    }
    throw new IllegalArgumentException("Invalid PaywayPaymentTransactionState: " + value);
  }
}
