package ar.com.daruma.bymas.movixEvent.domain.servicePayment.entities;

import java.util.Optional;

public enum PaymentType {
  MANDATORY_INVOICE("MANDATORY_INVOICE"),
  NO_INVOICE("NO_INVOICE"),
  NO_INVOICE_ADDITIONAL_DATA("NO_INVOICE_ADDITIONAL_DATA"),
  MIXED("MIXED");

  private final String description;

  PaymentType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public static Optional<PaymentType> fromDescription(String description) {
    for (PaymentType type : values()) {
      if (type.description.equalsIgnoreCase(description)) {
        return Optional.of(type);
      }
    }
    return Optional.empty();
  }
}
