package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

import ar.com.daruma.citadel.model.global.ActiveState;
import ar.com.daruma.citadelsdk.model.signme.response.JointSignatureRule;
import ar.com.daruma.citadelsdk.model.signme.response.SolidarySignatureRule;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignatureDataRequest {
  private Integer signatureRequestId;
  private Integer solicitorId;
  private ActiveState state;
  private Set<SignerResponseForPayment> signers;
  private Set<SignerResponseForPayment> potentialSigners;
  private LocalDateTime createdAt;
  private @Nullable SolidarySignatureRule maybeAppliedSolidaryRule;
  private @Nullable JointSignatureRule maybeAppliedJointRule;
}
