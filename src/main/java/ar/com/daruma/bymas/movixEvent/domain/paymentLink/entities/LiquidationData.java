package ar.com.daruma.bymas.movixEvent.domain.paymentLink.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiquidationData {
  private BigDecimal grossAmount;
  private BigDecimal totalFeeWithVat;
  private @Nullable BigDecimal totalFeeWithVatAndTaxes;
  private @Nullable BigDecimal finalNetAmount;
  private @Nullable BigDecimal finalTaxesAmount;
  private LocalDateTime estimatedLiquidationDateTime;
  private @Nullable LocalDateTime finalLiquidationDateTime;
  private String liquidationState;
  private List<PaymentTax> taxes;
}
