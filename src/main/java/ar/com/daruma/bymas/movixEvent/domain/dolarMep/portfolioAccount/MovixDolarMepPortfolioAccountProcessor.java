package ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarMepName;

import ar.com.daruma.bymas.movixEvent.domain.MovixProcessor;
import ar.com.daruma.bymas.movixEvent.domain.dolarMep.portfolioAccount.entities.MepPortfolioAccountResponse;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioAccount.application.findsert.FindsertPortfolioAccountByAccountNumber;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.application.find.FindPortfolioAssetByName;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.application.findsert.FindsertPortfolioPositionByDateAndAssetAndAccount;
import ar.com.daruma.bymas.portfolioPosition.application.update.UpdatePortfolioPositionByDolarMepAccount;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPosition;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class MovixDolarMepPortfolioAccountProcessor
    extends MovixProcessor<MepPortfolioAccountResponse> {

  @Autowired
  private FindsertPortfolioPositionByDateAndAssetAndAccount
      findsertPortfolioPositionByDateAndAssetAndAccount;

  @Autowired private FindPortfolioAssetByName findPortfolioAssetByName;

  @Autowired
  private UpdatePortfolioPositionByDolarMepAccount updatePortfolioPositionByDolarMepAccount;

  @Autowired
  private FindsertPortfolioAccountByAccountNumber findsertPortfolioAccountByAccountNumber;

  private static final CustomLogger logger =
      CustomLogger.getLogger(MovixDolarMepPortfolioAccountProcessor.class);

  public MovixDolarMepPortfolioAccountProcessor() {
    super("dolar_mep_account", MepPortfolioAccountResponse.class);
  }

  @Override
  @Transactional(transactionManager = "byMasTransactionManager")
  public void execute(MovixRecord movixRecord) {
    MepPortfolioAccountResponse response = getDataFromJsonNode(movixRecord);
    upsert(response);
  }

  private void upsert(MepPortfolioAccountResponse response) {
    logger.info(
        "Upserting dolar mep portfolio account for accountId {}, with quantities: quantity {}, lockedQuantity {}, pendingLiquidationQuantity {}",
        response.getAccountId(),
        response.getQuantity(),
        response.getLockedQuantity(),
        response.getPendingLiquidationQuantity());
    LocalDate now = BuenosAiresTime.nowAsLocalDate();
    PortfolioAsset dolarMepAsset = findPortfolioAssetByName.find(dolarMepName);
    PortfolioAccount portfolioAccount =
        findsertPortfolioAccountByAccountNumber.findsert(response.getAccountId());
    PortfolioPosition position =
        findsertPortfolioPositionByDateAndAssetAndAccount.findsert(
            now, dolarMepAsset, portfolioAccount);
    PortfolioPosition updatedPosition =
        updatePortfolioPositionByDolarMepAccount.execute(position, response);
    logger.info(
        "Updated portfolio position {} for accountId {}, new agreement amount: {}, new settlement amount: {}",
        updatedPosition.getId(),
        response.getAccountId(),
        updatedPosition.getAgreementQuantity(),
        updatedPosition.getSettlementQuantity());
  }
}
