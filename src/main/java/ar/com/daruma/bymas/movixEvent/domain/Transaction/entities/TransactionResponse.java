package ar.com.daruma.bymas.movixEvent.domain.Transaction.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.TRANSFER;

import ar.com.daruma.bymas.movixEvent.domain.echeq.entities.SignatureData;
import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import ar.com.daruma.citadelsdk.model.signme.response.SignatureType;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.*;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransactionResponse implements ExternalMovement {
  @Getter(AccessLevel.NONE)
  private Integer id;

  @Getter(AccessLevel.NONE)
  private int accountId;

  private int destinationAccountId;
  private TransactionType type;
  private CounterParty counterParty;
  private BigDecimal amount;
  private BigDecimal taxAmount;
  private List<InboundTax> inboundTaxes;
  private @Nullable String currency;
  private String description;
  private TransactionConcept concept;
  private @Nullable String tags;
  private List<String> notificationEmails;
  private @Nullable LocalDate scheduledDate;
  private TransactionState state;
  private LocalDateTime creationDateTime;
  private LocalDateTime processedDateTime;
  private @Nullable SignatureData signatureRequest;
  private @Nullable SignatureType signatureType;
  private @Nullable TransactionOrigin origin;
  private @Nullable String externalId;
  private Boolean outMarketImmediate;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return id + "-transactions";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(TRANSFER, this.state.toString());
  }

  @Override
  public OperationType getOperationType() {
    if (this.type == TransactionType.OUTBOUND) {
      return OperationType.DEBIT;
    } else return OperationType.CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    if (this.origin == TransactionOrigin.TRANSFER) {
      return this.type == TransactionType.OUTBOUND
          ? MarketOperationType.TRANSACTION_OUTBOUND
          : MarketOperationType.TRANSACTION_INBOUND;
    } else {
      return MarketOperationType.REDEMPTION;
    }
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getNetAmount() {
    return amount.subtract(taxAmount);
  }

  @Override
  public BigDecimal getGrossAmount() {
    return amount;
  }

  @Override
  public LocalDate getAgreementAt() {
    return creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return getCreationDateTime().toLocalDate();
  }

  @Override
  public BigDecimal getQuantity() {
    return amount;
  }

  @Override
  public String getDescription() {
    return type + " Transaction id:" + id;
  }

  @Override
  public MovementType getMovementType() {
    return TRANSFER;
  }

  @Override
  public Long getId() {
    return id.longValue();
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    if (this.origin == TransactionOrigin.TRANSFER && this.type == TransactionType.INBOUND) {
      return Optional.of(destinationAccountId);
    } else return Optional.of(accountId);
  }
}
