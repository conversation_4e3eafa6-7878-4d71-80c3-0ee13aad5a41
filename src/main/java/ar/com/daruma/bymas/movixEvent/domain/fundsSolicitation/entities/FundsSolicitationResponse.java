package ar.com.daruma.bymas.movixEvent.domain.fundsSolicitation.entities;

import static ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType.RETURN;
import static ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType.CREDIT;
import static ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType.RETURNS;

import ar.com.daruma.bymas.movixEvent.domain.Transaction.entities.TransactionResponse;
import ar.com.daruma.bymas.movixEvent.domain.entities.ExternalMovement;
import ar.com.daruma.bymas.movixEvent.presentation.bodies.MovixRecord;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.MovementType;
import ar.com.daruma.bymas.portfolioMovement.domain.entities.OperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.adapter.StateMapper;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MarketOperationType;
import ar.com.daruma.bymas.portfolioMovement.infrastructure.entities.MovementState;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FundsSolicitationResponse implements ExternalMovement {
  private Long id;
  private Integer accountId;
  private Integer allariaLinkId;
  private FundsSolicitationAllariaAccount allariaAccount;
  private BigDecimal amount;
  private @Nullable Boolean exempt;
  private LocalDateTime creationDateTime;
  private LocalDateTime lastModificationDateTime;
  private Integer lastModifiedBy;
  private Integer createdBy;
  private @Nullable String rejectionReason;
  private String requestedBy;
  private String state;
  private @Nullable TransactionResponse maybeTransaction;
  private MovixRecord movixRecord;

  @Override
  public String getOperationId() {
    return this.id + "-funds_solicitation";
  }

  @Override
  public MovementState getMovementState() {
    return StateMapper.getMovementState(RETURN, this.state);
  }

  @Override
  public LocalDate getAgreementAt() {
    return this.creationDateTime.toLocalDate();
  }

  @Override
  public LocalDate getSettlementAt() {
    return this.creationDateTime.toLocalDate();
  }

  @Override
  public BigDecimal getFeeAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getTaxAmount() {
    return BigDecimal.ZERO;
  }

  @Override
  public BigDecimal getGrossAmount() {
    return this.amount;
  }

  @Override
  public BigDecimal getNetAmount() {
    return this.amount;
  }

  @Override
  public BigDecimal getQuantity() {
    return this.amount;
  }

  @Override
  public String getDescription() {
    return "Funds solicitation";
  }

  @Override
  public OperationType getOperationType() {
    return CREDIT;
  }

  @Override
  public MarketOperationType getMarketOperationType() {
    return RETURNS;
  }

  @Override
  public String getCurrency() {
    return "Peso";
  }

  @Override
  public MovementType getMovementType() {
    return RETURN;
  }

  @Override
  public Long getId() {
    return this.id;
  }

  @Override
  public JsonNode getDataAsJson() {
    return movixRecord.getData();
  }

  @Override
  public Optional<Integer> getAccountId() {
    return Optional.of(this.accountId);
  }
}
