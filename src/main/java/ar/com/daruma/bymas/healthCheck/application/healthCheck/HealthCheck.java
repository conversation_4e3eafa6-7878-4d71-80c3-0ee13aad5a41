package ar.com.daruma.bymas.healthCheck.application.healthCheck;

import ar.com.daruma.bymas.healthCheck.domain.HealthCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.stereotype.Service;

@Service
public class HealthCheck {

  @Autowired HealthCheckService healthCheckService;

  public Health health() {
    return healthCheckService.healthcheck();
  }
}
