package ar.com.daruma.bymas.healthCheck.presentation.controller;

import ar.com.daruma.bymas.healthCheck.application.healthCheck.HealthCheck;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@ResponseBody
@RestController
@RequestMapping("/")
public class HealthcheckController {

  @Autowired HealthCheck healthCheck;

  @GetMapping("/healthcheck")
  public Health health() {
    return healthCheck.health();
  }
}
