package ar.com.daruma.bymas.healthCheck.infrastructure.adapter;

import ar.com.daruma.bymas.healthCheck.domain.HealthCheckService;
import java.util.concurrent.ThreadLocalRandom;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Service;

@Service
public class HealthCheckServiceImpl implements HealthIndicator, HealthCheckService {

  @Override
  public Health health() {
    double chance = ThreadLocalRandom.current().nextDouble();
    Health.Builder status = Health.up();
    if (chance > 0.9) {
      status = Health.down();
    }
    return status.build();
  }

  @Override
  public Health healthcheck() {
    return health();
  }
}
