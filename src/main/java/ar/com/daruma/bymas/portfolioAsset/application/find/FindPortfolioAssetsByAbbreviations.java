package ar.com.daruma.bymas.portfolioAsset.application.find;

import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioAssetsByAbbreviations {

  private final PortfolioAssetService portfolioAssetService;

  public List<PortfolioAsset> find(List<String> abbreviations) {
    log.info("Finding portfolio assets by {} abbreviations", abbreviations.size());
    List<PortfolioAsset> assets = portfolioAssetService.findByAbbreviationIn(abbreviations);
    log.info("{} Portfolio assets found", assets.size());
    return assets;
  }
}
