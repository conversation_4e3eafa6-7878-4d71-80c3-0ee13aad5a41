package ar.com.daruma.bymas.portfolioAsset.application.find;

import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FindPortfolioAssetsByNames {

  private final PortfolioAssetService portfolioAssetService;

  public List<PortfolioAsset> find(List<String> names) {
    log.info("Finding portfolio assets by {} names", names.size());
    List<PortfolioAsset> assets = portfolioAssetService.findByNameIn(names);
    log.info("{} Portfolio assets found", assets.size());
    return assets;
  }
}
