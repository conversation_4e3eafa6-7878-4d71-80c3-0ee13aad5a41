package ar.com.daruma.bymas.portfolioAsset.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PortfolioAssetService {
  PortfolioAsset save(PortfolioAsset asset);

  Optional<PortfolioAsset> findById(UUID id);

  List<PortfolioAsset> findByCategory(String category);

  PortfolioAsset findsertByNameCategorySubcategoryAbbreviationAndCurrencyAndQuotationByMarket(
      String name,
      String category,
      String subCategory,
      String abbreviation,
      String currency,
      QuotationKind quotationByMarket,
      Optional<Integer> allariaAssetId);

  PortfolioAsset
      upsertByNameCategorySubcategoryAbbreviationAndCurrencyAndQuotationByMarketAndPreferredValuationAndSubCategoryDescriptionWithoutSaving(
          String name,
          String category,
          String subCategory,
          String abbreviation,
          String currency,
          QuotationKind quotationByMarket,
          String preferredValuationCurrency,
          Optional<String> subCategoryDescription,
          Integer allariaAssetId);

  List<PortfolioAsset> findBySubCategory(String subCategory);

  List<PortfolioAsset> findByCurrency(String currency);

  List<PortfolioAsset> findAll();

  Optional<PortfolioAsset> findMaybeByName(String name);

  Optional<PortfolioAsset> findMaybeByAbbreviation(String abbreviation);

  Boolean existsByName(String name);

  void saveAll(List<PortfolioAsset> assets);

  List<PortfolioAsset> upsertPortfolioAssetsByPortfolios(List<Portfolio> portfolios);

  List<PortfolioAsset> findByNameIn(List<String> names);

  List<PortfolioAsset> findByAbbreviationIn(List<String> abbreviations);
}
