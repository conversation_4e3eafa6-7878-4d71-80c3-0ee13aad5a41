package ar.com.daruma.bymas.portfolioAsset.application.upsert;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UpsertMultiplePortfolioAssetsByPortfolios {

  private final PortfolioAssetService portfolioAssetService;

  public List<PortfolioAsset> upsert(List<Portfolio> portfolios) {
    log.info("Upserting portfolio assets by {} allaria integration portfolios", portfolios.size());
    List<PortfolioAsset> assets =
        portfolioAssetService.upsertPortfolioAssetsByPortfolios(portfolios);
    log.info("Upserted {} portfolio assets by allaria integration portfolios", assets.size());
    return assets;
  }
}
