package ar.com.daruma.bymas.portfolioAsset.domain;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PortfolioCurrencyAsset {

  private PortfolioAsset asset;

  private PortfolioCurrencyValuation valuation;

  public PortfolioCurrencyAsset(PortfolioAsset asset, PortfolioCurrencyValuation valuation) {
    this.asset = asset;
    this.valuation = valuation;
  }
}
