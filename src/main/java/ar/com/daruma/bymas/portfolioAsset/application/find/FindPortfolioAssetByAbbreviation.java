package ar.com.daruma.bymas.portfolioAsset.application.find;

import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.domain.entities.errors.PortfolioAssetNotFoundException;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.citadel.utils.CustomLogger;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindPortfolioAssetByAbbreviation {

  @Autowired private PortfolioAssetService portfolioAssetService;

  private static final CustomLogger logger =
      CustomLogger.getLogger(FindPortfolioAssetByAbbreviation.class);

  public PortfolioAsset find(String abbreviation) {
    logger.info("Finding portfolio asset by abbreviation {}", abbreviation);
    PortfolioAsset asset =
        portfolioAssetService
            .findMaybeByAbbreviation(abbreviation)
            .orElseThrow(() -> new PortfolioAssetNotFoundException("abbreviation", abbreviation));
    logger.info("Portfolio asset found with id: {}", asset.getId());
    return asset;
  }

  public List<PortfolioAsset> findMultiple(List<String> abbreviations) {
    logger.info("Finding portfolio assets by abbreviations {}", abbreviations);
    List<PortfolioAsset> assets = portfolioAssetService.findByAbbreviationIn(abbreviations);
    logger.info("{} Portfolio assets found", assets.size());
    return assets;
  }
}
