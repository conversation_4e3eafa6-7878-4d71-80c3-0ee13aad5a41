package ar.com.daruma.bymas.portfolioAsset.application.find;

import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.domain.entities.errors.PortfolioAssetNotFoundException;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.citadel.utils.CustomLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FindPortfolioAssetByName {

  @Autowired private PortfolioAssetService portfolioAssetService;

  private static final CustomLogger logger = CustomLogger.getLogger(FindPortfolioAssetByName.class);

  public PortfolioAsset find(String name) {
    logger.info("Finding portfolio asset by name {}", name);
    PortfolioAsset asset =
        portfolioAssetService
            .findMaybeByName(name)
            .orElseThrow(() -> new PortfolioAssetNotFoundException("name", name));
    logger.info("Portfolio asset found with id: {}", asset.getId());
    return asset;
  }
}
