package ar.com.daruma.bymas.portfolioAsset.infrastructure.entities;

import ar.com.daruma.bymas.portfolioPosition.presentation.entities.responses.AssetInfo;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import ar.com.daruma.bymas.utils.infrastructure.AuditableEntity;
import jakarta.persistence.*;
import java.util.Optional;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "portfolio_assets")
public class PortfolioAsset extends AuditableEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @Column(nullable = false)
  private String name;

  @Column(nullable = false)
  private String category;

  @Column(name = "sub_category", nullable = false)
  private String subCategory;

  @Column(name = "sub_category_description")
  @Getter(AccessLevel.NONE)
  private String subCategoryDescription;

  @Column(nullable = false)
  private String currency;

  @Column(nullable = false)
  private String abbreviation;

  @Enumerated(EnumType.STRING)
  @Column(name = "quotation_by", nullable = false)
  private QuotationKind quotationByMarket;

  @Column(name = "preferred_valuation_currency", nullable = false)
  private String preferredValuationCurrency;

  @Column(name = "allaria_asset_id")
  private Integer allariaAssetId;

  public PortfolioAsset() {}

  public PortfolioAsset(
      String name,
      String category,
      String subCategory,
      String abbreviation,
      String currency,
      QuotationKind quotationByMarket,
      String preferredValuationCurrency,
      String subCategoryDescription,
      Optional<Integer> allariaAssetId) {
    this.name = name;
    this.category = category;
    this.subCategory = subCategory;
    this.subCategoryDescription = subCategoryDescription;
    this.currency = currency;
    this.abbreviation = abbreviation;
    this.quotationByMarket = quotationByMarket;
    this.preferredValuationCurrency = preferredValuationCurrency;
    this.allariaAssetId = allariaAssetId.orElse(null);
  }

  public boolean isPesoCurrency() {
    return this.currency.equals("ARS");
  }

  public AssetInfo toAssetInfo() {
    return new AssetInfo(
        this.name, this.abbreviation, this.category, this.subCategory, this.currency);
  }

  public QuotationKind getQuotationKind() {
    return quotationByMarket;
  }

  public String getSubCategoryDescription() {
    return Optional.ofNullable(subCategoryDescription).orElse("");
  }

  public boolean isDollarCurrency() {
    return this.currency.contains("USD");
  }

  public boolean isPesoAsset() {
    return this.name.equals("Peso");
  }

  public boolean isDollarAsset() {
    return this.name.equals("Dólar");
  }

  public boolean isDollarMepAsset() {
    return this.name.equals("Dólar MEP");
  }

  public boolean isCurrencyAsset() {
    return isDollarAsset() || isDollarMepAsset() || isPesoAsset();
  }

  public PortfolioAssetKey getPortfolioAssetKey() {
    return new PortfolioAssetKey(name, abbreviation, category, subCategory, currency);
  }

  public boolean isFund() {
    return this.subCategory.contains("FCI");
  }

  public record PortfolioAssetKey(
      String name, String abbreviation, String category, String subCategory, String currency) {}
}
