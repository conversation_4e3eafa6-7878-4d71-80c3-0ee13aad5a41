package ar.com.daruma.bymas.portfolioAsset.infrastructure.repository;

import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioAssetRepository extends JpaRepository<PortfolioAsset, UUID> {
  List<PortfolioAsset> findByCategory(String category);

  List<PortfolioAsset> findBySubCategory(String subCategory);

  List<PortfolioAsset> findByCurrency(String currency);

  Optional<PortfolioAsset> findByNameAndCategoryAndSubCategoryAndCurrency(
      String name, String category, String subCategory, String currency);

  Optional<PortfolioAsset> findByNameIgnoreCase(String name);

  Optional<PortfolioAsset> findByAbbreviation(String abbreviation);

  Optional<PortfolioAsset> findByNameAndCategoryAndCurrency(
      String name, String category, String currency);

  List<PortfolioAsset> findByNameIn(List<String> names);

  List<PortfolioAsset> findByAbbreviationIn(List<String> abbreviations);

  Boolean existsByName(String name);

  @Modifying
  @Query("UPDATE PortfolioAsset p SET p.allariaAssetId = :allariaAssetId WHERE p.id = :id")
  void updateAllariaAssetId(@Param("id") UUID id, @Param("allariaAssetId") Integer allariaAssetId);
}
