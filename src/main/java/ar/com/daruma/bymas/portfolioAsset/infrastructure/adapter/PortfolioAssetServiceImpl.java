package ar.com.daruma.bymas.portfolioAsset.infrastructure.adapter;

import static ar.com.daruma.bymas.portfolioQuotation.domain.PortfolioQuotationCurrencyUtils.dolarMepName;

import ar.com.daruma.bymas.allariaIntegrations.investmentMarketCurrency.infrastructure.entities.InvestmentMarketCurrency;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.repository.PortfolioAssetRepository;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import ar.com.daruma.bymas.utils.time.application.BuenosAiresTime;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortfolioAssetServiceImpl implements PortfolioAssetService {

  @Autowired private PortfolioAssetRepository repository;

  @Override
  public PortfolioAsset save(PortfolioAsset asset) {
    return repository.save(asset);
  }

  @Override
  public Optional<PortfolioAsset> findById(UUID id) {
    return repository.findById(id);
  }

  @Override
  public List<PortfolioAsset> findByCategory(String category) {
    return repository.findByCategory(category);
  }

  @Override
  public PortfolioAsset
      findsertByNameCategorySubcategoryAbbreviationAndCurrencyAndQuotationByMarket(
          String name,
          String category,
          String subCategory,
          String abbreviation,
          String currency,
          QuotationKind quotationByMarket,
          Optional<Integer> allariaAssetId) {
    Optional<PortfolioAsset> maybeAsset =
        repository.findByNameAndCategoryAndCurrency(name, category, currency);
    updateAssetIdIfApply(maybeAsset, allariaAssetId);
    return maybeAsset.orElseGet(
        () -> {
          PortfolioAsset asset =
              new PortfolioAsset(
                  name,
                  category,
                  subCategory,
                  abbreviation,
                  currency,
                  quotationByMarket,
                  currency,
                  null, // TODO check if we can get this from kinesis
                  allariaAssetId);
          return save(asset);
        });
  }

  private void updateAssetIdIfApply(
      Optional<PortfolioAsset> maybeAsset, Optional<Integer> allariaAssetId) {
    if (maybeAsset.isPresent() && allariaAssetId.isPresent()) {
      repository.updateAllariaAssetId(maybeAsset.get().getId(), allariaAssetId.get());
    }
  }

  @Override
  public PortfolioAsset
      upsertByNameCategorySubcategoryAbbreviationAndCurrencyAndQuotationByMarketAndPreferredValuationAndSubCategoryDescriptionWithoutSaving(
          String name,
          String category,
          String subCategory,
          String abbreviation,
          String currency,
          QuotationKind quotationByMarket,
          String preferredValuationCurrency,
          Optional<String> subCategoryDescription,
          Integer allariaAssetId) {
    Optional<PortfolioAsset> maybeAsset =
        repository.findByNameAndCategoryAndCurrency(name, category, currency);

    PortfolioAsset asset =
        maybeAsset.orElseGet(
            () ->
                new PortfolioAsset(
                    name,
                    category,
                    subCategory,
                    abbreviation,
                    currency,
                    quotationByMarket,
                    preferredValuationCurrency,
                    subCategoryDescription.orElse(null),
                    Optional.ofNullable(allariaAssetId)));
    asset.setQuotationByMarket(
        name.equals(dolarMepName) ? asset.getQuotationByMarket() : quotationByMarket);
    asset.setPreferredValuationCurrency(preferredValuationCurrency);
    asset.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    asset.setSubCategoryDescription(subCategoryDescription.orElse(null));
    asset.setAllariaAssetId(allariaAssetId);
    return asset;
  }

  @Override
  public List<PortfolioAsset> findBySubCategory(String subCategory) {
    return repository.findBySubCategory(subCategory);
  }

  @Override
  public List<PortfolioAsset> findByCurrency(String currency) {
    return repository.findByCurrency(currency);
  }

  @Override
  public List<PortfolioAsset> findAll() {
    return repository.findAll();
  }

  @Override
  public Optional<PortfolioAsset> findMaybeByName(String name) {
    return repository.findByNameIgnoreCase(name);
  }

  @Override
  public Optional<PortfolioAsset> findMaybeByAbbreviation(String abbreviation) {
    return repository.findByAbbreviation(abbreviation);
  }

  @Override
  public Boolean existsByName(String name) {
    return repository.existsByName(name);
  }

  @Override
  public void saveAll(List<PortfolioAsset> assets) {
    repository.saveAll(assets);
  }

  @Override
  public List<PortfolioAsset> upsertPortfolioAssetsByPortfolios(List<Portfolio> portfolios) {
    List<String> assetNames = portfolios.stream().map(Portfolio::assetName).distinct().toList();

    Map<PortfolioAsset.PortfolioAssetKey, List<Portfolio>> portfoliosByAssetKey =
        portfolios.stream().collect(Collectors.groupingBy(Portfolio::getPortfolioAssetKey));
    Set<PortfolioAsset.PortfolioAssetKey> keys = portfoliosByAssetKey.keySet();
    List<PortfolioAsset> existingAssets = repository.findByNameIn(assetNames).stream().toList();
    existingAssets =
        existingAssets.stream()
            .filter(asset -> keys.contains(asset.getPortfolioAssetKey()))
            .toList();
    Set<PortfolioAsset.PortfolioAssetKey> existingPortfolioAssetKeys =
        existingAssets.stream()
            .map(PortfolioAsset::getPortfolioAssetKey)
            .collect(Collectors.toSet());

    existingAssets.forEach(asset -> updateExistingAsset(asset, portfoliosByAssetKey));

    List<Portfolio> portfoliosWithoutExistingAssets =
        portfolios.stream()
            .filter(
                portfolio -> !existingPortfolioAssetKeys.contains(portfolio.getPortfolioAssetKey()))
            .toList();

    List<PortfolioAsset> newAssets =
        portfoliosWithoutExistingAssets.stream()
            .map(Portfolio::createNewPortfolioAsset)
            .distinct()
            .toList();

    List<PortfolioAsset> allAssets = new ArrayList<>();
    allAssets.addAll(newAssets);
    allAssets.addAll(existingAssets);

    return repository.saveAll(allAssets);
  }

  @Override
  public List<PortfolioAsset> findByNameIn(List<String> names) {
    return repository.findByNameIn(names);
  }

  @Override
  public List<PortfolioAsset> findByAbbreviationIn(List<String> abbreviations) {
    return repository.findByAbbreviationIn(abbreviations);
  }

  private void updateExistingAsset(
      PortfolioAsset asset,
      Map<PortfolioAsset.PortfolioAssetKey, List<Portfolio>> portfoliosByAssetKey) {
    portfoliosByAssetKey.getOrDefault(asset.getPortfolioAssetKey(), List.of()).stream()
        .findFirst()
        .ifPresent(portfolio -> updateAssetWithPortfolioData(asset, portfolio));
  }

  private void updateAssetWithPortfolioData(PortfolioAsset asset, Portfolio portfolio) {
    asset.setQuotationByMarket(
        asset.getName().equals(dolarMepName)
            ? asset.getQuotationByMarket()
            : portfolio.getQuotationKind());
    asset.setPreferredValuationCurrency(
        portfolio
            .getInvestmentMarketCurrency()
            .map(InvestmentMarketCurrency::getDescription)
            .orElse(portfolio.currency()));
    asset.setUpdatedAt(BuenosAiresTime.nowAsLocalDateTime());
    asset.setSubCategoryDescription(portfolio.getMaybeSubCategoryDescription().orElse(null));
    asset.setAllariaAssetId(portfolio.getInvestment().getId());
  }
}
