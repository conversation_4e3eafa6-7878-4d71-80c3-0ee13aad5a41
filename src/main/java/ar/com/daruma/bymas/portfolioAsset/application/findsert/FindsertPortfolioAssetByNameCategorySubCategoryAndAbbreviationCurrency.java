package ar.com.daruma.bymas.portfolioAsset.application.findsert;

import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;

import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioAssetService;
import ar.com.daruma.bymas.portfolioAsset.domain.PortfolioCurrencyAsset;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioCurrencyValuation.application.findsert.FindsertPortfolioCurrencyValuationByNameAndCode;
import ar.com.daruma.bymas.portfolioCurrencyValuation.infrastructure.entities.PortfolioCurrencyValuation;
import ar.com.daruma.bymas.portfolioQuotation.application.create.CreateAssetQuotation;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency {

  private final PortfolioAssetService portfolioAssetService;

  private final FindsertPortfolioCurrencyValuationByNameAndCode
      findsertPortfolioCurrencyValuationByNameAndCode;

  private final CreateAssetQuotation createAssetQuotation;

  private static final Logger logger =
      LogManager.getLogger(
          FindsertPortfolioAssetByNameCategorySubCategoryAndAbbreviationCurrency.class);

  // TODO : change this to be used in the sync from allaria integrations when allaria integrations
  // table changes
  public PortfolioAsset findsertWithQuotationByMarket(
      String name,
      String category,
      String subCategory,
      String abbreviation,
      String currency,
      QuotationKind quotationKind,
      Optional<Integer> allariaAssetId) {
    logger.info(
        "Finding or inserting PortfolioAsset with name {}, category {}, subCategory {}, abbreviation {}, currency {} and quotationKind {}",
        name,
        category,
        subCategory,
        abbreviation,
        currency,
        quotationKind.toString());
    Boolean existedBeforeFindsert = portfolioAssetService.existsByName(name);
    PortfolioAsset asset =
        portfolioAssetService
            .findsertByNameCategorySubcategoryAbbreviationAndCurrencyAndQuotationByMarket(
                name, category, subCategory, abbreviation, currency, quotationKind, allariaAssetId);
    if (!existedBeforeFindsert) {
      logger.info(
          "PortfolioAsset with name {} did not exist before, creating quotations for it", name);
      try {
        createAssetQuotation.create(asset);
        logger.info("Quotations for PortfolioAsset with id {} created", asset.getId());
      } catch (Exception e) {
        logger.warn(
            "Error creating quotations for PortfolioAsset with id {}: {}",
            asset.getId(),
            e.getMessage());
      }
    }
    logger.info("PortfolioAsset with id {} found or inserted", asset.getId());
    return asset;
  }

  public PortfolioAsset findsert(
      String name,
      String category,
      String subCategory,
      String abbreviation,
      String currency,
      Optional<Integer> allariaAssetId) {
    return findsertWithQuotationByMarket(
        name, category, subCategory, abbreviation, currency, MARKET_DATA, allariaAssetId);
  }

  @Transactional(transactionManager = "byMasTransactionManager")
  public PortfolioCurrencyAsset findsertAsCurrency(
      String name,
      String category,
      String subCategory,
      String abbreviation,
      String code,
      String currency) {
    PortfolioAsset asset =
        findsert(name, category, subCategory, abbreviation, currency, Optional.empty());
    PortfolioCurrencyValuation valuation =
        findsertPortfolioCurrencyValuationByNameAndCode.findsert(name, code);
    return new PortfolioCurrencyAsset(asset, valuation);
  }
}
