package ar.com.daruma.bymas;

import ar.com.daruma.bymas.runner.ByMasInitializer;
import ar.com.daruma.citadel.configuration.SharedConfigurationReference;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@SpringBootApplication(exclude = {UserDetailsServiceAutoConfiguration.class})
@EnableScheduling
@Import(SharedConfigurationReference.class)
public class ByMasApplication {
  public static void main(String[] args) {
    SpringApplication application = new SpringApplication(ByMasApplication.class);
    application.addInitializers(new ByMasInitializer());
    application.run(args);
  }
}
