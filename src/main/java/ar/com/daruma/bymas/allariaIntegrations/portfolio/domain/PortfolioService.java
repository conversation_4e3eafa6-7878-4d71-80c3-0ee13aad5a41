package ar.com.daruma.bymas.allariaIntegrations.portfolio.domain;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PortfolioService {
  Portfolio save(Portfolio portfolio);

  Optional<Portfolio> findById(Integer id);

  List<Portfolio> findAll();

  Page<Portfolio> findByDateAndAccount(
      LocalDateTime date, Optional<Integer> accountId, Pageable pageable);
}
