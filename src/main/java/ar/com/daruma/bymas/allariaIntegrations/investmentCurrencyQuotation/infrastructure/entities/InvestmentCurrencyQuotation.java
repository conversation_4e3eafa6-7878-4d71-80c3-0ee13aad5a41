package ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.entities;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "investment_currency_quotations")
public class InvestmentCurrencyQuotation {
  @Id private Integer id;

  @Column(nullable = false)
  private String description;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public InvestmentCurrencyQuotation() {}

  public InvestmentCurrencyQuotation(
      Integer id, String description, LocalDateTime creationDateTime, Integer createdBy) {
    this.id = id;
    this.description = description;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }

  // Getters and Setters
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public LocalDateTime getCreationDateTime() {
    return creationDateTime;
  }

  public void setCreationDateTime(LocalDateTime creationDateTime) {
    this.creationDateTime = creationDateTime;
  }

  public Integer getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(Integer createdBy) {
    this.createdBy = createdBy;
  }
}
