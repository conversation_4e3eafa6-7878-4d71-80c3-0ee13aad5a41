package ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.domain.PortfolioService;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.repository.PortfolioRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class PortfolioServiceImpl implements PortfolioService {
  @Autowired private PortfolioRepository repository;

  @Override
  public Portfolio save(Portfolio portfolio) {
    return repository.save(portfolio);
  }

  @Override
  public Optional<Portfolio> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<Portfolio> findAll() {
    return repository.findAll();
  }

  @Override
  public Page<Portfolio> findByDateAndAccount(
      LocalDateTime date, Optional<Integer> accountId, Pageable pageable) {
    return repository.findByDateAfterAndOptionalAccount(date, accountId.orElse(null), pageable);
  }
}
