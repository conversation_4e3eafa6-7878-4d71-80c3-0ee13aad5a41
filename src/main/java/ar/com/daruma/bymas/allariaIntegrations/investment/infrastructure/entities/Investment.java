package ar.com.daruma.bymas.allariaIntegrations.investment.infrastructure.entities;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "investments")
public class Investment {

  @Id private Integer id;

  @Column(nullable = false)
  private String description;

  @Column(name = "original_description", nullable = false)
  private String originalDescription;

  private String code;
  private String isin;
  private String cusip;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public Investment() {}

  public Investment(
      Integer id,
      String description,
      String originalDescription,
      String code,
      String isin,
      String cusip,
      LocalDateTime creationDateTime,
      Integer createdBy) {
    this.id = id;
    this.description = description;
    this.originalDescription = originalDescription;
    this.code = code;
    this.isin = isin;
    this.cusip = cusip;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }
}
