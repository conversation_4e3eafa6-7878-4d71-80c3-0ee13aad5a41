package ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.entities;

import static ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets.allariaCompany;

import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@EqualsAndHashCode
@Table(name = "allaria_accounts")
public class AllariaAccount {

  @Id
  @Column(name = "account_number")
  private Integer accountNumber;

  @Column(name = "denomination", nullable = false)
  private String denomination;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public AllariaAccount() {}

  public AllariaAccount(
      Integer accountNumber,
      String denomination,
      LocalDateTime creationDateTime,
      Integer createdBy) {
    this.accountNumber = accountNumber;
    this.denomination = denomination;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }

  public PortfolioAccount createNewPortfolioAccount(Boolean isPhysical) {
    return new PortfolioAccount(getAccountNumber(), getDenomination(), isPhysical, allariaCompany);
  }
}
