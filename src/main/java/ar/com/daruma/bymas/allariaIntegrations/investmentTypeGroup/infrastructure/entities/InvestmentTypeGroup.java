package ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.entities;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

@Setter
@Entity
@Getter
@Table(name = "investment_type_groups")
public class InvestmentTypeGroup {
  @Id private Integer id;

  @Getter(AccessLevel.NONE)
  @Column(nullable = false)
  private String description;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public InvestmentTypeGroup() {}

  public InvestmentTypeGroup(
      Integer id, String description, LocalDateTime creationDateTime, Integer createdBy) {
    this.id = id;
    this.description = description;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }

  public String getDescription() {
    if (description.contains("/")) {
      return description.split("/")[0].trim();
    }
    return description;
  }

  public Optional<String> getMaybeSubCategoryDescription() {
    if (description.contains(
        "/")) { // TODO : check to see if we can use a mapper here or something alike
      return Optional.of(description.split("/")[0].trim());
    }
    return Optional.empty();
  }
}
