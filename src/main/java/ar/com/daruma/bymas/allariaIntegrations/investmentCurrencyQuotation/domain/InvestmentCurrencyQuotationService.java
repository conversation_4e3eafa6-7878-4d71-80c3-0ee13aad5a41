package ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.domain;

import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.entities.InvestmentCurrencyQuotation;
import java.util.List;
import java.util.Optional;

public interface InvestmentCurrencyQuotationService {
  InvestmentCurrencyQuotation save(InvestmentCurrencyQuotation quotation);

  Optional<InvestmentCurrencyQuotation> findById(Integer id);

  List<InvestmentCurrencyQuotation> findAll();
}
