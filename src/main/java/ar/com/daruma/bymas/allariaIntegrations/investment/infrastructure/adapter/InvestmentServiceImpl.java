package ar.com.daruma.bymas.allariaIntegrations.investment.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investment.domain.InvestmentService;
import ar.com.daruma.bymas.allariaIntegrations.investment.infrastructure.entities.Investment;
import ar.com.daruma.bymas.allariaIntegrations.investment.infrastructure.repository.InvestmentRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentServiceImpl implements InvestmentService {
  @Autowired private InvestmentRepository repository;

  @Override
  public Investment save(Investment investment) {
    return repository.save(investment);
  }

  @Override
  public Optional<Investment> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<Investment> findAll() {
    return repository.findAll();
  }
}
