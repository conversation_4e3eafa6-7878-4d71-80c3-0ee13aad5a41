package ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.domain.InvestmentTypeGroupService;
import ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.entities.InvestmentTypeGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.repository.InvestmentTypeGroupRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentTypeGroupServiceImpl implements InvestmentTypeGroupService {
  @Autowired private InvestmentTypeGroupRepository repository;

  @Override
  public InvestmentTypeGroup save(InvestmentTypeGroup group) {
    return repository.save(group);
  }

  @Override
  public Optional<InvestmentTypeGroup> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<InvestmentTypeGroup> findAll() {
    return repository.findAll();
  }
}
