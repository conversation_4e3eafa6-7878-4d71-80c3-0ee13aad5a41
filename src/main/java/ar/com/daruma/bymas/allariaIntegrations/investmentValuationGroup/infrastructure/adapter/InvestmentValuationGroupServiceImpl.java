package ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.domain.InvestmentValuationGroupService;
import ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.infrastructure.entities.InvestmentValuationGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.infrastructure.repository.InvestmentValuationGroupRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentValuationGroupServiceImpl implements InvestmentValuationGroupService {
  @Autowired private InvestmentValuationGroupRepository repository;

  @Override
  public InvestmentValuationGroup save(InvestmentValuationGroup group) {
    return repository.save(group);
  }

  @Override
  public Optional<InvestmentValuationGroup> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<InvestmentValuationGroup> findAll() {
    return repository.findAll();
  }
}
