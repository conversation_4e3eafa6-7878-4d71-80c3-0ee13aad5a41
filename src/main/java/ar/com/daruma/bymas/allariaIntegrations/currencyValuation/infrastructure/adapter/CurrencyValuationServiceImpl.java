package ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.domain.CurrencyValuationService;
import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.repository.CurrencyValuationRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CurrencyValuationServiceImpl implements CurrencyValuationService {
  @Autowired private CurrencyValuationRepository repository;

  @Override
  public CurrencyValuation save(CurrencyValuation currencyValuation) {
    return repository.save(currencyValuation);
  }

  @Override
  public Optional<CurrencyValuation> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<CurrencyValuation> findAll() {
    return repository.findAll();
  }
}
