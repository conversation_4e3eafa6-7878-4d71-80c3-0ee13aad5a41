package ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@EqualsAndHashCode
@Table(name = "currency_valuations")
public class CurrencyValuation {
  @Id private Integer id;

  @Column(nullable = false)
  private String description;

  @Column(nullable = false)
  private String code;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public CurrencyValuation() {}

  public CurrencyValuation(
      Integer id,
      String description,
      String code,
      LocalDateTime creationDateTime,
      Integer createdBy) {
    this.id = id;
    this.description = description;
    this.code = code;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }
}
