package ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.repository;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import java.time.LocalDateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PortfolioRepository extends JpaRepository<Portfolio, Integer> {

  @Query(
      """
          SELECT p FROM Portfolio p
          WHERE p.date >= :date
          AND (:accountId IS NULL OR p.allariaAccount.accountNumber = :accountId)
          """)
  @EntityGraph(attributePaths = "criteria")
  Page<Portfolio> findByDateAfterAndOptionalAccount(
      @Param("date") LocalDateTime date, @Param("accountId") Integer accountId, Pageable page);
}
