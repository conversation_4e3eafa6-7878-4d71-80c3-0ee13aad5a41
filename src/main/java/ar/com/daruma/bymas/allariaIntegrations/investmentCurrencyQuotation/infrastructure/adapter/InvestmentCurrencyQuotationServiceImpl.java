package ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.domain.InvestmentCurrencyQuotationService;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.entities.InvestmentCurrencyQuotation;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.repository.InvestmentCurrencyQuotationRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentCurrencyQuotationServiceImpl implements InvestmentCurrencyQuotationService {
  @Autowired private InvestmentCurrencyQuotationRepository repository;

  @Override
  public InvestmentCurrencyQuotation save(InvestmentCurrencyQuotation quotation) {
    return repository.save(quotation);
  }

  @Override
  public Optional<InvestmentCurrencyQuotation> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<InvestmentCurrencyQuotation> findAll() {
    return repository.findAll();
  }
}
