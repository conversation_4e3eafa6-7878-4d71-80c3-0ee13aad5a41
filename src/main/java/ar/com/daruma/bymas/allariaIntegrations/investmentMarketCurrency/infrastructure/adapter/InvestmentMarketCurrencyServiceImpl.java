package ar.com.daruma.bymas.allariaIntegrations.investmentMarketCurrency.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentMarketCurrency.domain.InvestmentMarketCurrencyService;
import ar.com.daruma.bymas.allariaIntegrations.investmentMarketCurrency.infrastructure.repository.InvestmentMarketCurrencyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentMarketCurrencyServiceImpl implements InvestmentMarketCurrencyService {

  @Autowired private InvestmentMarketCurrencyRepository repository;
}
