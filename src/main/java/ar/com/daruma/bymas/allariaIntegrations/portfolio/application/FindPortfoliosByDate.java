package ar.com.daruma.bymas.allariaIntegrations.portfolio.application;

import ar.com.daruma.bymas.allariaIntegrations.portfolio.domain.PortfolioService;
import ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities.Portfolio;
import java.time.LocalDateTime;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class FindPortfoliosByDate {
  @Autowired private PortfolioService portfolioService;

  private static final Logger logger = LogManager.getLogger(FindPortfoliosByDate.class);

  public Page<Portfolio> find(LocalDateTime date, Optional<Integer> accountId, Pageable pageable) {
    logger.info("Finding portfolios before date {}", date);
    Page<Portfolio> portfolios = portfolioService.findByDateAndAccount(date, accountId, pageable);
    logger.info("Found {} portfolios before date {}", portfolios.getTotalElements(), date);
    return portfolios;
  }
}
