package ar.com.daruma.bymas.allariaIntegrations.portfolio.infrastructure.entities;

import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.ALLARIA_DATA;
import static ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind.MARKET_DATA;

import ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.entities.AllariaAccount;
import ar.com.daruma.bymas.allariaIntegrations.criteria.infrastructure.entities.Criteria;
import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import ar.com.daruma.bymas.allariaIntegrations.investment.infrastructure.entities.Investment;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.infrastructure.entities.InvestmentCurrencyGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyQuotation.infrastructure.entities.InvestmentCurrencyQuotation;
import ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.infrastructure.entities.InvestmentInstrumentGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentMarketCurrency.infrastructure.entities.InvestmentMarketCurrency;
import ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.entities.InvestmentTypeGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.infrastructure.entities.InvestmentValuationGroup;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.bymas.portfolioAsset.infrastructure.entities.PortfolioAsset;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioPositionHistory;
import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioQuantities;
import ar.com.daruma.bymas.portfolioQuotation.domain.entities.QuotationKind;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(
    name = "portfolios",
    indexes = {
      @Index(name = "portfolios_date_idx", columnList = "date"),
      @Index(
          name = "portfolios_investment_instrument_group_date_idx",
          columnList = "investment_instrument_group_date"),
      @Index(name = "portfolios_creation_date_time_idx", columnList = "creation_date_time")
    })
@EqualsAndHashCode
public class Portfolio {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(nullable = false)
  private LocalDateTime date;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "allaria_account_id", nullable = false)
  private AllariaAccount allariaAccount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "criteria_id", nullable = false)
  private Criteria criteria;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "currency_valuation_id", nullable = false)
  private CurrencyValuation currencyValuation;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_id", nullable = false)
  private Investment investment;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_currency_group_id", nullable = false)
  private InvestmentCurrencyGroup investmentCurrencyGroup;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_currency_quotation_id", nullable = false)
  private InvestmentCurrencyQuotation investmentCurrencyQuotation;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_type_group_id", nullable = false)
  private InvestmentTypeGroup investmentTypeGroup;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_valuation_group_id", nullable = false)
  private InvestmentValuationGroup investmentValuationGroup;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_instrument_group_id", nullable = false)
  private InvestmentInstrumentGroup investmentInstrumentGroup;

  @Column(name = "investment_instrument_group_date")
  private LocalDateTime investmentInstrumentGroupDate;

  @Column(precision = 19, scale = 2)
  private BigDecimal tc;

  @Column(name = "investment_quotation", precision = 19, scale = 2)
  private BigDecimal investmentQuotation;

  @Column(name = "quantity_available", nullable = false, precision = 19, scale = 2)
  private BigDecimal quantityAvailable;

  @Column(name = "quantity_no_available", nullable = false, precision = 19, scale = 2)
  private BigDecimal quantityNoAvailable;

  @Column(nullable = false, precision = 19, scale = 2)
  private BigDecimal quantity;

  @Column(name = "net_amount_currency_quotation", precision = 19, scale = 2)
  private BigDecimal netAmountCurrencyQuotation;

  @Column(name = "net_amount_currency_valuation", precision = 19, scale = 2)
  private BigDecimal netAmountCurrencyValuation;

  @Column(name = "percentage_participation_valuation", precision = 19, scale = 2)
  private BigDecimal percentageParticipationValuation;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "last_modified_by", nullable = false)
  private Integer lastModifiedBy;

  @Column(name = "last_modification_date_time", nullable = false)
  private LocalDateTime lastModificationDateTime;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "investment_market_currency_id")
  private InvestmentMarketCurrency investmentMarketCurrency;

  @Getter(AccessLevel.NONE)
  @Column(name = "percentage_passive_participation")
  private BigDecimal percentagePassiveParticipation;

  @Getter(AccessLevel.NONE)
  @Column(name = "percentage_active_participation")
  private BigDecimal percentageActiveParticipation;

  @Column(name = "investment_quotation_market_intraday")
  private Integer investmentQuotationMarketIntraday;

  public Integer getInvestmentId() {
    return investment.getId();
  }

  public record PortfolioIdentity(
      Integer investmentId,
      Integer allariaAccountNumber,
      Integer currencyValuationId,
      LocalDate date) {}

  public Portfolio() {}

  public String category() {
    return this.investmentInstrumentGroup.getDescription();
  }

  public String subCategory() {
    return this.investmentTypeGroup.getDescription();
  }

  public Optional<String> getMaybeSubCategoryDescription() {
    return this.investmentTypeGroup.getMaybeSubCategoryDescription();
  }

  public String assetName() {
    return this.investment.getOriginalDescription();
  }

  public String assetAbbreviation() {
    return this.investment.getDescription();
  }

  public String currencyValuationDescription() {
    return this.currencyValuation.getDescription();
  }

  public String currencyValuationCode() {
    return this.currencyValuation.getCode();
  }

  public int investmentId() {
    return getInvestment().getId();
  }

  public int accountNumber() {
    return getAllariaAccount().getAccountNumber();
  }

  public String accountName() {
    return getAllariaAccount().getDenomination();
  }

  public Integer currencyValuationId() {
    return getCurrencyValuation().getId();
  }

  public Boolean isLiquidation() {
    return getCriteria().isLiquidation();
  }

  public Boolean isConcertation() {
    return getCriteria().isConcertation();
  }

  public String currency() {
    return getInvestmentCurrencyQuotation().getDescription();
  }

  public Optional<BigDecimal> maybeInvestmentQuotation() {
    return Optional.ofNullable(investmentQuotation);
  }

  public Optional<BigDecimal> getPercentagePassiveParticipation() {
    return Optional.ofNullable(percentagePassiveParticipation);
  }

  public Optional<BigDecimal> getPercentageActiveParticipation() {
    return Optional.ofNullable(percentageActiveParticipation);
  }

  public Optional<InvestmentMarketCurrency> getInvestmentMarketCurrency() {
    return Optional.ofNullable(investmentMarketCurrency);
  }

  public QuotationKind getQuotationKind() {
    if (investmentQuotationMarketIntraday == null || investmentQuotationMarketIntraday == 1) {
      return MARKET_DATA;
    }
    return ALLARIA_DATA;
  }

  public PortfolioAsset createNewPortfolioAsset() {
    return new PortfolioAsset(
        assetName(),
        category(),
        subCategory(),
        assetAbbreviation(),
        currency(),
        getQuotationKind(),
        getInvestmentMarketCurrency()
            .map(InvestmentMarketCurrency::getDescription)
            .orElse(currency()),
        getMaybeSubCategoryDescription().orElse(null),
        Optional.ofNullable(getInvestment().getId()));
  }

  public String createSearchParameter() {
    return assetName()
        + "|"
        + assetAbbreviation()
        + "|"
        + category()
        + "|"
        + subCategory()
        + "|"
        + currency();
  }

  public PortfolioAsset.PortfolioAssetKey getPortfolioAssetKey() {
    return new PortfolioAsset.PortfolioAssetKey(
        assetName(), assetAbbreviation(), category(), subCategory(), currency());
  }

  public PortfolioPositionHistory.HistoryKey getPortfolioHistoryKey(
      Map<Integer, PortfolioAsset> portfolioAssetsByInvestmentId,
      Map<Integer, PortfolioAccount> portfolioAccountsByAccountNumber) {
    return new PortfolioPositionHistory.HistoryKey(
        portfolioAssetsByInvestmentId.get(investmentId()).getId(),
        portfolioAccountsByAccountNumber.get(accountNumber()).getId(),
        getDate().toLocalDate());
  }

  public PortfolioQuantities getQuantities() {
    return new PortfolioQuantities(quantity, this.criteria.getType());
  }
}
