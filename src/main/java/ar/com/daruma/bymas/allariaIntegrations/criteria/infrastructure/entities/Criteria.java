package ar.com.daruma.bymas.allariaIntegrations.criteria.infrastructure.entities;

import ar.com.daruma.bymas.portfolioPosition.infrastructure.entities.PortfolioQuantitiesType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Optional;

@Entity
@Table(name = "criterias")
public class Criteria {
  @Id private Integer id;

  @Column(nullable = false)
  private String description;

  @Column(name = "creation_date_time", nullable = false)
  private LocalDateTime creationDateTime;

  @Column(name = "created_by", nullable = false)
  private Integer createdBy;

  public Criteria() {}

  public Criteria(
      Integer id, String description, LocalDateTime creationDateTime, Integer createdBy) {
    this.id = id;
    this.description = description;
    this.creationDateTime = creationDateTime;
    this.createdBy = createdBy;
  }

  // Getters and Setters
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public LocalDateTime getCreationDateTime() {
    return creationDateTime;
  }

  public void setCreationDateTime(LocalDateTime creationDateTime) {
    this.creationDateTime = creationDateTime;
  }

  public Integer getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(Integer createdBy) {
    this.createdBy = createdBy;
  }

  public boolean isLiquidation() {
    return Optional.ofNullable(description).map(i -> i.equals("Liquidación")).orElse(false);
  }

  public boolean isConcertation() {
    return Optional.ofNullable(description).map(i -> i.equals("Concertación")).orElse(false);
  }

  public PortfolioQuantitiesType getType() {
    if (isConcertation()) {
      return PortfolioQuantitiesType.AGREEMENT;
    }
    return PortfolioQuantitiesType.SETTLEMENT;
  }
}
