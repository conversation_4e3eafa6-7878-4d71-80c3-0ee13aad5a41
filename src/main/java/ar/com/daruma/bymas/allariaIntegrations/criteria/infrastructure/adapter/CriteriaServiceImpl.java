package ar.com.daruma.bymas.allariaIntegrations.criteria.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.criteria.domain.CriteriaService;
import ar.com.daruma.bymas.allariaIntegrations.criteria.infrastructure.entities.Criteria;
import ar.com.daruma.bymas.allariaIntegrations.criteria.infrastructure.repository.CriteriaRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CriteriaServiceImpl implements CriteriaService {
  @Autowired private CriteriaRepository repository;

  @Override
  public Criteria save(Criteria criteria) {
    return repository.save(criteria);
  }

  @Override
  public Optional<Criteria> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<Criteria> findAll() {
    return repository.findAll();
  }
}
