package ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.domain.InvestmentInstrumentGroupService;
import ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.infrastructure.entities.InvestmentInstrumentGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.infrastructure.repository.InvestmentInstrumentGroupRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentInstrumentGroupServiceImpl implements InvestmentInstrumentGroupService {
  @Autowired private InvestmentInstrumentGroupRepository repository;

  @Override
  public InvestmentInstrumentGroup save(InvestmentInstrumentGroup group) {
    return repository.save(group);
  }

  @Override
  public Optional<InvestmentInstrumentGroup> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<InvestmentInstrumentGroup> findAll() {
    return repository.findAll();
  }
}
