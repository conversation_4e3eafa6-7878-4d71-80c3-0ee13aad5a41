package ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.domain;

import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.infrastructure.entities.InvestmentCurrencyGroup;
import java.util.List;
import java.util.Optional;

public interface InvestmentCurrencyGroupService {
  InvestmentCurrencyGroup save(InvestmentCurrencyGroup group);

  Optional<InvestmentCurrencyGroup> findById(Integer id);

  List<InvestmentCurrencyGroup> findAll();
}
