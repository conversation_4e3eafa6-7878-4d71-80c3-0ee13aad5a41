package ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.allariaAccount.domain.AllariaAccountService;
import ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.entities.AllariaAccount;
import ar.com.daruma.bymas.allariaIntegrations.allariaAccount.infrastructure.repository.AllariaAccountRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AllariaAccountServiceImpl implements AllariaAccountService {
  @Autowired private AllariaAccountRepository repository;

  @Override
  public AllariaAccount save(AllariaAccount account) {
    return repository.save(account);
  }

  @Override
  public Optional<AllariaAccount> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<AllariaAccount> findAll() {
    return repository.findAll();
  }
}
