package ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.domain;

import ar.com.daruma.bymas.allariaIntegrations.investmentTypeGroup.infrastructure.entities.InvestmentTypeGroup;
import java.util.List;
import java.util.Optional;

public interface InvestmentTypeGroupService {
  InvestmentTypeGroup save(InvestmentTypeGroup group);

  Optional<InvestmentTypeGroup> findById(Integer id);

  List<InvestmentTypeGroup> findAll();
}
