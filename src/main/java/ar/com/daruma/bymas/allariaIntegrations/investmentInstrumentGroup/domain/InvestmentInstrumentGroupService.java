package ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.domain;

import ar.com.daruma.bymas.allariaIntegrations.investmentInstrumentGroup.infrastructure.entities.InvestmentInstrumentGroup;
import java.util.List;
import java.util.Optional;

public interface InvestmentInstrumentGroupService {
  InvestmentInstrumentGroup save(InvestmentInstrumentGroup group);

  Optional<InvestmentInstrumentGroup> findById(Integer id);

  List<InvestmentInstrumentGroup> findAll();
}
