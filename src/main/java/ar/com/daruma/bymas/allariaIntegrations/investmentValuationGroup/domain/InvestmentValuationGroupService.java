package ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.domain;

import ar.com.daruma.bymas.allariaIntegrations.investmentValuationGroup.infrastructure.entities.InvestmentValuationGroup;
import java.util.List;
import java.util.Optional;

public interface InvestmentValuationGroupService {
  InvestmentValuationGroup save(InvestmentValuationGroup group);

  Optional<InvestmentValuationGroup> findById(Integer id);

  List<InvestmentValuationGroup> findAll();
}
