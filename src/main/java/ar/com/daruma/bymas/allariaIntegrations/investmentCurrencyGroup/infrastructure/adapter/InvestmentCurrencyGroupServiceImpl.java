package ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.infrastructure.adapter;

import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.domain.InvestmentCurrencyGroupService;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.infrastructure.entities.InvestmentCurrencyGroup;
import ar.com.daruma.bymas.allariaIntegrations.investmentCurrencyGroup.infrastructure.repository.InvestmentCurrencyGroupRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class InvestmentCurrencyGroupServiceImpl implements InvestmentCurrencyGroupService {
  @Autowired private InvestmentCurrencyGroupRepository repository;

  @Override
  public InvestmentCurrencyGroup save(InvestmentCurrencyGroup group) {
    return repository.save(group);
  }

  @Override
  public Optional<InvestmentCurrencyGroup> findById(Integer id) {
    return repository.findById(id);
  }

  @Override
  public List<InvestmentCurrencyGroup> findAll() {
    return repository.findAll();
  }
}
