package ar.com.daruma.bymas.allariaIntegrations.currencyValuation.domain;

import ar.com.daruma.bymas.allariaIntegrations.currencyValuation.infrastructure.entities.CurrencyValuation;
import java.util.List;
import java.util.Optional;

public interface CurrencyValuationService {
  CurrencyValuation save(CurrencyValuation currencyValuation);

  Optional<CurrencyValuation> findById(Integer id);

  List<CurrencyValuation> findAll();
}
