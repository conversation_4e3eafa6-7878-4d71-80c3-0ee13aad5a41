package ar.com.daruma.bymas.security.application;

import ar.com.daruma.bymas.configuration.authn.AuthnConfiguration;
import ar.com.daruma.bymas.configuration.keycloak.KeyCloakConfiguration;
import ar.com.daruma.bymas.security.domain.AuthDetails;
import ar.com.daruma.bymas.security.domain.AuthnDetails;
import ar.com.daruma.bymas.security.domain.AuthnResponse;
import ar.com.daruma.bymas.security.domain.KeyCloakAuthDetails;
import ar.com.daruma.citadel.dto.GetRequestDTO;
import ar.com.daruma.citadel.model.FirebaseAuthentication;
import ar.com.daruma.citadel.model.users.UserContext;
import ar.com.daruma.citadel.security.FirebaseAuthDetails;
import ar.com.daruma.citadel.service.FirebaseAuthenticationService;
import ar.com.daruma.citadel.utils.HttpClient;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Component
@RequiredArgsConstructor
public class BymasAuthFilter extends OncePerRequestFilter {

  private static final Logger logger = LogManager.getLogger(BymasAuthFilter.class);
  private static final List<String> AUTH_HEADER_NAMES = List.of("Authorization", "authorization");
  private static final String APP_NAME = "BY_MAS";
  private final KeyCloakConfiguration keyCloakConfiguration;

  private final AuthnConfiguration authnConfiguration;
  private final FirebaseAuthenticationService firebaseAuthenticationService;
  private final RequestMappingHandlerMapping requestMappingHandlerMapping;
  private JWKSet JWK_URL;

  @SneakyThrows
  private JWKSet getJWKSet() {
    assert keyCloakConfiguration != null;
    return JWKSet.load(URI.create(keyCloakConfiguration.getHost()).toURL());
  }

  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    if (pathExists(request)) {
      String companyType = request.getHeader("X-Client-Origin");
      String companyTarget = request.getHeader("X-Client-Target");
      if (companyType != null) {
        switch (companyType) {
          case "ALLARIA_MAS" -> maybeBearerToken(request)
              .flatMap(this::authWithFirebase)
              .ifPresent(auth -> SecurityContextHolder.getContext().setAuthentication(auth));
          case "ALLARIA" -> maybeBearerToken(request)
              .flatMap(token -> authWithAuthn(request))
              .ifPresent(auth -> SecurityContextHolder.getContext().setAuthentication(auth));
          case "API_PROVIDER" -> maybeBearerToken(request)
              .flatMap(token -> authWithKeyCloak(stripBearer(token), companyTarget))
              .ifPresent(auth -> SecurityContextHolder.getContext().setAuthentication(auth));
        }
      }
    }

    filterChain.doFilter(request, response);
  }

  @SneakyThrows
  private Optional<UsernamePasswordAuthenticationToken> authWithKeyCloak(
      String token, String companyTarget) {
    if (JWK_URL == null) {
      JWK_URL = getJWKSet();
    }
    DecodedJWT jwt;
    try {
      jwt = JWT.decode(token);
    } catch (Exception e) {
      logger.error("Invalid JWT token: {}", e.getMessage());
      return Optional.empty();
    }
    JWK jwk =
        Optional.ofNullable(JWK_URL.getKeyByKeyId(jwt.getKeyId()))
            .orElseThrow(
                () -> new NoSuchElementException("JWK not found for keyId: ${jwt.getKeyId}"));
    RSAPublicKey publicKey = (RSAPublicKey) jwk.toRSAKey().toPublicKey();
    Algorithm algorithm = Algorithm.RSA256(publicKey);
    JWTVerifier verifier = JWT.require(algorithm).withIssuer(jwt.getIssuer()).build();
    DecodedJWT decodedJwt = verifier.verify(token);

    KeyCloakAuthDetails keyCloakDetails = new KeyCloakAuthDetails(decodedJwt, companyTarget);
    UsernamePasswordAuthenticationToken authentication =
        new UsernamePasswordAuthenticationToken("", "", new ArrayList<>());
    authentication.setDetails(keyCloakDetails);

    return Optional.of(authentication);
  }

  private boolean pathExists(HttpServletRequest request) {
    try {
      return requestMappingHandlerMapping.getHandler(request) != null;
    } catch (Exception e) {
      logger.error("Error checking path existence: {}", e.getMessage());
      return false;
    }
  }

  private Optional<String> maybeBearerToken(HttpServletRequest request) {
    return AUTH_HEADER_NAMES.stream().map(request::getHeader).filter(Objects::nonNull).findFirst();
  }

  private Optional<UsernamePasswordAuthenticationToken> authWithFirebase(String token) {
    try {
      FirebaseAuthentication auth = firebaseAuthenticationService.auth(token);
      if (auth == null || !auth.isAuthenticated()) return Optional.empty();

      UserContext userContext = auth.getUserContext();
      FirebaseAuthDetails details = new FirebaseAuthDetails();
      details.setToken(stripBearer(token));
      details.setUserContext(userContext);

      UsernamePasswordAuthenticationToken authentication =
          new UsernamePasswordAuthenticationToken(userContext.getEmail(), "", new ArrayList<>());
      authentication.setDetails(details);
      return Optional.of(authentication);

    } catch (Exception e) {
      logger.warn("Firebase auth failed: {}", e.getMessage());
      return Optional.empty();
    }
  }

  private Optional<UsernamePasswordAuthenticationToken> authWithAuthn(HttpServletRequest request) {
    String bearerToken = maybeBearerToken(request).orElse(null);
    return requestAuthn(bearerToken).flatMap(this::buildAuthentication);
  }

  private Optional<AuthnDetails> requestAuthn(String bearerToken) {
    logger.info("Requesting authentication with Authn");

    GetRequestDTO<AuthnResponse> request = new GetRequestDTO<>();
    request.setUrl(String.format("%s/user?include=esco_accounts", authnConfiguration.getHost()));
    request.setCustomHeaders(Map.of("Authorization", bearerToken));
    request.setResponseClass(AuthnResponse.class);

    try {
      AuthnResponse authnResponse = HttpClient.get(request);
      AuthnDetails details = new AuthnDetails();
      details.setAuthnResponse(authnResponse);
      details.setToken(stripBearer(bearerToken));
      return Optional.of(details);
    } catch (Exception ex) {
      logger.error("Error authenticating with Authn: {}", ex.getMessage());
      return Optional.empty();
    }
  }

  private Optional<UsernamePasswordAuthenticationToken> buildAuthentication(AuthnDetails details) {

    if (!details.getAuthnResponse().getEscoValidAccount().getAccounts().isEmpty()) {
      String email = details.getAuthnResponse().getEmail();
      AuthDetails authDetails = new AuthDetails();
      authDetails.setGivenName(details.getAuthnResponse().getGivenName());
      authDetails.setLastName(details.getAuthnResponse().getLastName());
      authDetails.setToken(details.getToken());
      authDetails.setAccounts(details.getAuthnResponse().getEscoValidAccount().getAccounts());

      UsernamePasswordAuthenticationToken authentication =
          new UsernamePasswordAuthenticationToken(email, "", new ArrayList<>());
      authentication.setDetails(authDetails);
      return Optional.of(authentication);
    }

    return Optional.empty();
  }

  private String stripBearer(String token) {
    return token.replace("Bearer ", "").replace("bearer ", "").trim();
  }
}
