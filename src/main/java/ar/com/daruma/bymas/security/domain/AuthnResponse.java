package ar.com.daruma.bymas.security.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthnResponse {
  private String email;

  @JsonProperty("given_name")
  private String givenName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("esco")
  private EscoValidAccount escoValidAccount;
}
