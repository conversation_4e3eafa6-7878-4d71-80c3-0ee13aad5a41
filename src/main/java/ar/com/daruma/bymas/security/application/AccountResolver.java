package ar.com.daruma.bymas.security.application;

import ar.com.daruma.bymas.configuration.secrets.ByMasCompanySecrets;
import ar.com.daruma.bymas.portfolioAccount.application.find.FindRelatedAccounts;
import ar.com.daruma.bymas.portfolioAccount.infrastructure.entities.PortfolioAccount;
import ar.com.daruma.citadel.exceptions.ForbiddenException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@RequiredArgsConstructor
@Slf4j
public class AccountResolver {

  private final FindRelatedAccounts findRelatedAccounts;

  @Transactional(transactionManager = "byMasTransactionManager")
  public Optional<String> resolveAccountParam(JoinPoint joinPoint, Boolean isAllariaAccount) {
    Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();

    Optional<String> maybeAccountIdParam = extractAccountId(joinPoint, method);
    List<PortfolioAccount> accounts =
        maybeAccountIdParam
            .map(
                accountId ->
                    isAllariaAccount
                        ? findRelatedAccounts.findAndReturnWithOwnAccount(
                            Integer.parseInt(accountId), ByMasCompanySecrets.allariaCompany)
                        : findRelatedAccounts.findAndReturnWithOwnAccount(
                            Integer.parseInt(accountId), ByMasCompanySecrets.allariaMasCompany))
            .orElse(Collections.emptyList());
    Optional<String> maybeRelationshipAccountId =
        accounts.stream()
            .filter(
                isAllariaAccount
                    ? PortfolioAccount::isAllariaAccount
                    : PortfolioAccount::isAllariaMasAccount)
            .findFirst()
            .map(PortfolioAccount::getAccountId)
            .map(Object::toString);
    if (maybeAccountIdParam.isPresent() && maybeRelationshipAccountId.isEmpty()) {
      log.error(
          "No valid [{}] relationship found for account [{}].",
          isAllariaAccount ? "Allaria" : "Allaria+",
          maybeAccountIdParam.get());
      throw new ForbiddenException();
    }
    return maybeRelationshipAccountId;
  }

  private Optional<String> extractAccountId(JoinPoint joinPoint, Method method) {
    Parameter[] parameters = method.getParameters();
    Object[] args = joinPoint.getArgs();

    for (int i = 0; i < parameters.length; i++) {
      RequestParam annotation = parameters[i].getAnnotation(RequestParam.class);
      if (annotation != null && "account-id".equals(annotation.name())) {
        return Optional.ofNullable(args[i]).map(Object::toString);
      }
    }

    return Optional.empty();
  }
}
