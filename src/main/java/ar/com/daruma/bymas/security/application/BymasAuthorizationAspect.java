package ar.com.daruma.bymas.security.application;

import ar.com.daruma.bymas.security.domain.AuthDetails;
import ar.com.daruma.bymas.security.domain.KeyCloakAuthDetails;
import ar.com.daruma.bymas.security.domain.ValidAccount;
import ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization;
import ar.com.daruma.citadel.annotations.authorization.factories.AuthorizationStrategyFactory;
import ar.com.daruma.citadel.annotations.authorization.strategies.AuthorizationStrategy;
import ar.com.daruma.citadel.exceptions.ForbiddenException;
import ar.com.daruma.citadel.model.users.AuthUserRole;
import ar.com.daruma.citadel.model.users.UserContext;
import ar.com.daruma.citadel.security.FirebaseAuthDetails;
import com.auth0.jwt.interfaces.DecodedJWT;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class BymasAuthorizationAspect {

  private final AuthorizationStrategyFactory authorizationStrategyFactory;
  private final AccountResolver accountResolver;

  private static final String ROLE = "BY_MAS_OPERATOR";

  @Before("@annotation(ar.com.daruma.bymas.security.domain.annotations.BymasAuthorization)")
  public void checkAuthorization(JoinPoint joinPoint) {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication == null || !authentication.isAuthenticated()) {
      log.warn("No authentication found or user not authenticated.");
      throw new ForbiddenException();
    }

    Object details = authentication.getDetails();

    if (details instanceof FirebaseAuthDetails firebaseDetails) {
      handleFirebaseAuthorization(joinPoint, firebaseDetails);
      return;
    }

    if (details instanceof AuthDetails authDetails) {
      handleAuthnAuthorization(joinPoint, authDetails);
      return;
    }

    if (details instanceof KeyCloakAuthDetails keyCloakAuthDetails) {
      handleKeyCloakAuthorization(keyCloakAuthDetails);
      return;
    }

    log.warn("Unsupported authentication type: {}", details.getClass().getName());
    throw new ForbiddenException();
  }

  private void handleKeyCloakAuthorization(KeyCloakAuthDetails keyCloakAuthDetails) {
    DecodedJWT decodedJwt = keyCloakAuthDetails.jwt();
    String companyType = keyCloakAuthDetails.companyTarget();
    log.info("Validating Keycloak token for authorization...");
    if (!keyCloakTokenIsValid(decodedJwt) || companyType == null) {
      throw new ForbiddenException();
    }
    log.info("Keycloak token is valid, authorization complete...");
  }

  private void handleFirebaseAuthorization(
      JoinPoint joinPoint, FirebaseAuthDetails firebaseDetails) {
    UserContext userContext = firebaseDetails.getUserContext();
    Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
    BymasAuthorization annotation = method.getAnnotation(BymasAuthorization.class);
    AuthUserRole requiredRole = AuthUserRole.valueOf(annotation.role());

    Optional<String> accountId =
        userContext.isSystemAdmin()
            ? Optional.empty()
            : accountResolver.resolveAccountParam(joinPoint, false);
    Optional<String> maybeUserId = extractUserId(joinPoint, method);

    AuthorizationStrategy strategy = authorizationStrategyFactory.determineStrategy(requiredRole);
    strategy.authorize(userContext, requiredRole, accountId, maybeUserId);

    log.info(
        "Authorization successful via Firebase for user {}, role required: {}",
        firebaseDetails.getUserContext().getDisplayName(),
        requiredRole);
  }

  private void handleAuthnAuthorization(JoinPoint joinPoint, AuthDetails authDetails) {
    List<ValidAccount> validAccounts = authDetails.getAccounts();
    Optional<String> maybeAccountId = accountResolver.resolveAccountParam(joinPoint, true);
    maybeAccountId.ifPresentOrElse(
        accountId -> {
          if (validAccounts.stream()
              .noneMatch(validAccount -> validAccount.getId().toString().equals(accountId))) {
            log.info(
                "Cannot authorize user {}, account {} is not valid.",
                SecurityContextHolder.getContext().getAuthentication().getPrincipal(),
                accountId);
            throw new ForbiddenException();
          }
        },
        () -> {
          log.info(
              "Cannot authorize user {}, accountId is missing.",
              SecurityContextHolder.getContext().getAuthentication().getPrincipal());
          throw new ForbiddenException();
        });

    log.info(
        "Authorization successful via AuthN for user: {} in account: {}",
        authDetails.getGivenName() + " " + authDetails.getLastName(),
        maybeAccountId.orElse(""));
  }

  private Optional<String> extractUserId(JoinPoint joinPoint, Method method) {
    Parameter[] parameters = method.getParameters();
    Object[] args = joinPoint.getArgs();

    for (int i = 0; i < parameters.length; i++) {
      RequestParam annotation = parameters[i].getAnnotation(RequestParam.class);
      if (annotation != null && "userId".equals(annotation.name())) {
        return Optional.ofNullable(args[i]).map(Object::toString);
      }
    }

    return Optional.empty();
  }

  private boolean keyCloakTokenIsValid(DecodedJWT decoded) {
    try {
      Map<String, Object> resourceAccess = decoded.getClaim("resource_access").asMap();
      if (resourceAccess == null || !resourceAccess.containsKey("bymas")) {
        return false;
      }

      Object byMasAccessObj = resourceAccess.get("bymas");
      if (!(byMasAccessObj instanceof Map)) {
        return false;
      }

      @SuppressWarnings("unchecked")
      Map<String, Object> byMasAccessMap = (Map<String, Object>) byMasAccessObj;
      Object rolesObj = byMasAccessMap.get("roles");

      if (!(rolesObj instanceof List)) {
        return false;
      }

      @SuppressWarnings("unchecked")
      List<String> roles = (List<String>) rolesObj;
      return roles.contains(ROLE);

    } catch (Exception e) {
      log.warn("Error validating token: {}", e.getMessage());
      return false;
    }
  }
}
