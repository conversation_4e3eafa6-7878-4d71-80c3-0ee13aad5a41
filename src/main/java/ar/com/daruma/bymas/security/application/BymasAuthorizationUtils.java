package ar.com.daruma.bymas.security.application;

import ar.com.daruma.bymas.security.domain.AuthDetails;
import ar.com.daruma.bymas.security.domain.KeyCloakAuthDetails;
import ar.com.daruma.citadel.security.FirebaseAuthDetails;
import java.util.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class BymasAuthorizationUtils {

  public String getAccountCompanyNameFromContext() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    Object details = authentication.getDetails();
    if (details instanceof FirebaseAuthDetails) {
      return "ALLARIA_MAS";
    }
    if (details instanceof AuthDetails) {
      return "ALLARIA";
    }
    if (details instanceof KeyCloakAuthDetails keyCloakAuthDetails) {
      return keyCloakAuthDetails.companyTarget();
    }
    return null;
  }

  // TODO THIS NEEDS TO BE REMOVED FOR SECURITY AFTER PAMPA FIXES THEIR SERVICE
  public Boolean isAllariaMasAdmin() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    Object details = authentication.getDetails();
    if (details instanceof FirebaseAuthDetails authDetails) {
      return authDetails.getUserContext().isSystemAdmin();
    }
    return false;
  }
}
