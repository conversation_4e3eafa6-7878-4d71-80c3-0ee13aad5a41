version: '3'
resources:
  cpu: 4
  memory: "8GB"

environment:
  ENVIRONMENT: prod
  ALLARIAMAS_RDS_HOSTNAME: allariamas-db-int.allariamas.com.ar
  ALLARIAMAS_RDS_DB_NAME: darumadb
  ALLARIAINTEGRATIONS_RDS_HOSTNAME: allaria-integrations-db-int.allariamas.com.ar
  ALLARIAINTEGRATIONS_RDS_DB_NAME: allariadb
  BYMAS_RDS_HOSTNAME: bymas.db.internal.allaria.cloud
  BYMAS_RDS_DB_NAME: bymas
  ALLARIA_MAS_COMPANY_UUID: 6fcf5c7a-1c90-4de2-aeb7-a74d04246654
  ALLARIA_COMPANY_UUID: 501c1774-81db-4831-be10-4fb02394f825
  FRESCO_HOST: https://fresco.svc.internal.allaria.cloud
  MARKET_DATA_HOST: https://market-data.svc.internal.allaria.cloud
  PITBULL_HOST: https://pitbull-int.allariamas.com.ar
  AUTHN_HOST: https://authn.svc.internal.allaria.cloud
  GROTESCO_HOST: https://grotesco.svc.internal.allaria.cloud
  KEY_CLOAK_HOST: https://keycloak.svc.internal.allaria.cloud/realms/main/protocol/openid-connect/certs
  USER_ACCOUNT_MGMT_HOST: https://user-account-mgmt-int.allariamas.com.ar
  PHYSICAL_MONEY_MARKET_ID: 1
  JURIDICAL_MONEY_MARKET_ID: 2
  BYMAS_MAX_POOL_SIZE: 30
  ALLARIA_INT_MAX_POOL_SIZE: 30
  BYMAS_CONN_TIME_OUT: 30000
  ALLARIA_INT_CONN_TIME_OUT: 30000
secrets:
  ALLARIAMAS_RDS_USERNAME: application/by-mas:allariamas_rds_user
  ALLARIAMAS_RDS_PASSWORD: application/by-mas:allariamas_rds_password
  ALLARIAINTEGRATIONS_RDS_USERNAME: application/by-mas:allariaintegrations_rds_username
  ALLARIAINTEGRATIONS_RDS_PASSWORD: application/by-mas:allariaintegrations_rds_password
  BYMAS_RDS_USERNAME: application/by-mas:bymas_rds_username
  BYMAS_RDS_PASSWORD: application/by-mas:bymas_rds_password
  DISCORD_ID: application/by-mas:discord_id
  DISCORD_TOKEN: application/by-mas:discord_token
  ADMIN_TOKEN: application/by-mas:admin_token
  FIREBASE_KEY: application/by-mas:firebase_key
  KINESIS_ENABLED: application/by-mas:kinesis_enabled
  MOVIX_ENABLED: application/by-mas:movix_enabled