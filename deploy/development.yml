version: '3'
resources:
  cpu: 4
  memory: "8GB"

environment:
  ENVIRONMENT: dev
  ALLARIAMAS_RDS_HOSTNAME: allariamas-db-int.allariamas.dev
  ALLARIAMAS_RDS_DB_NAME: darumadb
  ALLARIAINTEGRATIONS_RDS_HOSTNAME: allaria-integrations-db-int.allariamas.dev
  ALLARIAINTEGRATIONS_RDS_DB_NAME: allariadb
  BYMAS_RDS_HOSTNAME: bymas.db.internal.allaria.dev
  BYMAS_RDS_DB_NAME: bymas
  ALLARIA_MAS_COMPANY_UUID: 184f8aa7-dd41-4a94-9609-e6080679c3f5
  ALLARIA_COMPANY_UUID: 5288c8d8-600e-4af8-b9e3-cd4842e54ee0
  FRESCO_HOST: https://fresco.svc.internal.allaria.dev
  MARKET_DATA_HOST: https://market-data.svc.internal.allaria.dev
  PITBULL_HOST: https://pitbull-int.allariamas.dev
  AUTHN_HOST: https://authn.svc.internal.allaria.dev
  GROTESCO_HOST: https://grotesco.svc.internal.allaria.dev
  KEY_CLOAK_HOST: https://keycloak.svc.internal.allaria.dev/realms/main/protocol/openid-connect/certs
  USER_ACCOUNT_MGMT_HOST: https://user-account-mgmt-int.allariamas.dev
  PHYSICAL_MONEY_MARKET_ID: 2
  JURIDICAL_MONEY_MARKET_ID: 3
  BYMAS_MAX_POOL_SIZE: 10
  ALLARIA_INT_MAX_POOL_SIZE: 10
  BYMAS_CONN_TIME_OUT: 30000
  ALLARIA_INT_CONN_TIME_OUT: 30000
secrets:
  ALLARIAMAS_RDS_USERNAME: application/by-mas:allariamas_rds_user
  ALLARIAMAS_RDS_PASSWORD: application/by-mas:allariamas_rds_password
  ALLARIAINTEGRATIONS_RDS_USERNAME: application/by-mas:allariaintegrations_rds_username
  ALLARIAINTEGRATIONS_RDS_PASSWORD: application/by-mas:allariaintegrations_rds_password
  BYMAS_RDS_USERNAME: application/by-mas:bymas_rds_username
  BYMAS_RDS_PASSWORD: application/by-mas:bymas_rds_password
  DISCORD_ID: application/by-mas:discord_id
  DISCORD_TOKEN: application/by-mas:discord_token
  ADMIN_TOKEN: application/by-mas:admin_token
  FIREBASE_KEY: application/by-mas:firebase_key
  KINESIS_ENABLED: application/by-mas:kinesis_enabled
  MOVIX_ENABLED: application/by-mas:movix_enabled

