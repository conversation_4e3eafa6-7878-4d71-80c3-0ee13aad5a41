CREATE TYPE event_state AS ENUM ('PENDING_PROCESSING', 'FAILED', 'SUCCESSFUL', 'REPROCESSING_SUCCESSFUL', 'REPROCESSING_FAILED', 'DISCARDED');
CREATE CAST (character varying AS event_state) WITH INOUT AS ASSIGNMENT;

CREATE TABLE kinesis_events
(
    id                             serial      not null PRIMARY KEY,
    kinesis_event                  JSONB       NOT NULL,
    state                          event_state NOT NULL,
    portfolio_account_id           UUID,
    portfolio_movement_external_id BIGINT,
    error                          TEXT,
    last_attempt_time              TIMESTAMP,
    last_error                     TEXT,
    created_at                     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at                     TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
ALTER TYPE event_state ADD VALUE 'SUCCESSFUL_UNKNOWN';

ALTER TABLE kinesis_events
    ADD COLUMN table_name VA<PERSON>HAR(255) NOT NULL;