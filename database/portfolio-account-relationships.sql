create type active_state as enum ('ACTIVE', 'INACTIVE');
create cast (character varying as active_state) with inout as assignment;

CREATE TABLE portfolio_account_relationships
(
    id                   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id           int               NOT NULL,
    portfolio_account_id UUID              NOT NULL references portfolio_accounts (id),
    state                active_state      NOT NULL,
    type                 relationship_type NOT NULL
);

create type relationship_type as enum ('ALLARIA_LINK', 'MIRRORED');
create cast (character varying as relationship_type) with inout as assignment;
alter table portfolio_account_relationships
    add column type relationship_type not null default 'MIRRORED';
alter table portfolio_account_relationships
    alter column type drop default;

alter table portfolio_account_relationships
    add column created_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;

CREATE INDEX portfolio_account_relationships_account_id_idx ON portfolio_account_relationships (account_id);
CREATE INDEX portfolio_account_relationships_portfolio_account_id_idx ON portfolio_account_relationships (portfolio_account_id);
