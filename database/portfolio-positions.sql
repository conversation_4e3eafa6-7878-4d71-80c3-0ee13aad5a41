CREATE TABLE portfolio_positions
(
    id                   UUID PRIMARY KEY,
    portfolio_account_id UUID      NOT NULL REFERENCES portfolio_accounts (id),
    portfolio_asset_id   UUID      NOT NULL REFERENCES portfolio_assets (id),
    quantity             NUMERIC   NOT NULL,
    description          VARCHAR,
    tags                 VARCHAR,
    metadata             JSONB,
    last_updated_at      TIMESTAMP NOT NULL,
    created_at           TIMESTAMP NOT NULL,

    constraint ck_account_asset_unique UNIQUE (portfolio_account_id, portfolio_asset_id)
);


alter table portfolio_positions
    rename column quantity to settlement_quantity;


alter table portfolio_positions
    add column agreement_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_positions
    alter column agreement_quantity drop default;


alter table portfolio_positions
    alter column id set DEFAULT gen_random_uuid();

CREATE INDEX portfolio_positions_portfolio_account_id_idx ON portfolio_positions (portfolio_account_id);
CREATE INDEX portfolio_positions_portfolio_asset_id_idx ON portfolio_positions (portfolio_asset_id);


alter table portfolio_positions
    add column last_synced_at TIMESTAMP NOT NULL DEFAULT NOW();

alter table portfolio_positions
    alter column last_synced_at drop default;


alter table portfolio_positions
    add column locked_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_positions
    add column available_quantity NUMERIC NOT NULL DEFAULT 0;


alter table portfolio_positions
    alter column locked_quantity drop default;
alter table portfolio_positions
    alter column available_quantity drop default;
