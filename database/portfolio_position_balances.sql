CREATE TABLE IF NOT EXISTS portfolio_position_balances (
    id UUID PRIMARY KEY,
    portfolio_position_id UUID NOT NULL,
    movement_id UUID,
    settlement_quantity NUMERIC NOT NULL,
    description VARCHAR,
    tags VARCHAR,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT now(),
    agreement_quantity NUMERIC NOT NULL,
    locked_quantity NUMERIC NOT NULL,
    available_quantity NUMERIC NOT NULL,
    is_cancel_movement BOOLEAN DEFAULT false,
    is_sync BOOLEAN DEFAULT false,
    movement_process_state public.movement_process_state
);

ALTER TABLE portfolio_position_balances
    ADD CONSTRAINT fk_portfolio_position_balances_position
        FOREIGN KEY (portfolio_position_id) REFERENCES portfolio_positions(id);

ALTER TABLE portfolio_position_balances
    ADD CONSTRAINT fk_portfolio_movements
        FOREIGN KEY (movement_id) REFERENCES portfolio_movements(id);
