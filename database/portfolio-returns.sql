CREATE TABLE portfolio_returns
(
    id                   UUID PRIMARY KEY,
    portfolio_account_id UUID    NOT NULL REFERENCES portfolio_accounts (id),
    portfolio_asset_id   UUID    NOT NULL REFERENCES portfolio_assets (id),
    initial_valuation    NUMERIC NOT NULL,
    final_valuation      NUMERIC NOT NULL,
    description          VARCHAR,
    tags                 VARCHAR,
    total_valuation      NUMERIC NOT NULL,
    metadata             JSONB,
    date                 DATE    NOT NULL,
    last_updated_at      DATE    NOT NULL,
    created_at           DATE    NOT NULL
);