CREATE TABLE portfolio_position_history
(
    id                   UUID PRIMARY KEY,
    portfolio_account_id UUID    NOT NULL REFERENCES portfolio_accounts (id),
    portfolio_asset_id   UUID    NOT NULL REFERENCES portfolio_assets (id),
    agreement_quantity   NUMERIC NOT NULL,
    settlement_quantity  NUMERIC NOT NULL,
    description          VARCHAR,
    tags                 VARCHAR,
    metadata             JSONB,
    date                 DATE    NOT NULL,
    created_at           DATE    NOT NULL
);


alter table portfolio_position_history
    add constraint unique_history unique (portfolio_account_id, portfolio_asset_id, date);

alter table portfolio_position_history
    alter column id set default gen_random_uuid();


CREATE INDEX IF NOT EXISTS portfolio_position_history_date_idx ON portfolio_position_history (date);
CREATE INDEX IF NOT EXISTS portfolio_position_history_portfolio_account_id_idx ON portfolio_position_history (portfolio_account_id);
CREATE INDEX IF NOT EXISTS portfolio_position_history_portfolio_asset_id_idx ON portfolio_position_history (portfolio_asset_id);

alter table portfolio_position_history
    add column agreement_locked_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_position_history
    add column settlement_locked_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_position_history
    add column agreement_available_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_position_history
    add column settlement_available_quantity NUMERIC NOT NULL DEFAULT 0;

alter table portfolio_position_history
    alter column agreement_locked_quantity drop default;
alter table portfolio_position_history
    alter column settlement_locked_quantity drop default;
alter table portfolio_position_history
    alter column agreement_available_quantity drop default;
alter table portfolio_position_history
    alter column settlement_available_quantity drop default;


alter table portfolio_position_history
    drop column agreement_locked_quantity;
alter table portfolio_position_history
    drop column settlement_locked_quantity;
alter table portfolio_position_history
    drop column agreement_available_quantity;
alter table portfolio_position_history
    drop column settlement_available_quantity;
