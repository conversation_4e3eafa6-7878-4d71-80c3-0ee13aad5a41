CREATE TYPE movement_type AS ENUM ('CARD', 'PAYMENT_LINK', 'SERVICE_PAYMENT', 'ECHEQ',
    'VEP', 'ARBA','DOLLAR_MEP','AUTOMATIC_DEBIT','RETURN','WEB_CHECKOUT','TRANSFER','CHEQ', 'ACCIONES', 'TITULO_PUBLICO',
    'TITULOS_PROVINCIALES', 'OBLIGACIONES_NEGOCIABLES', 'CAUTION',
    'FONDOS', 'INDICES', 'CEDEARS', 'PAGARES', 'MERVAL', 'CEDROS', 'LEBAC', 'FCI',
    'FUTURO_ACCIONES', 'CEDIN');
CREATE CAST (character varying AS movement_type) WITH INOUT AS ASSIGNMENT;
ALTER TYPE movement_type ADD VALUE 'ACTIONS';
ALTER TYPE movement_type ADD VALUE 'FUTURES';
ALTER TYPE movement_type ADD VALUE 'OPTIONS';
ALTER TYPE movement_type ADD VALUE 'PROGRAMMED_PAYMENT';

CREATE TYPE operation_type AS ENUM ('CREDIT','DEBIT');
CREATE CAST (character varying AS operation_type) WITH INOUT AS ASSIGNMENT;



DO
$$
    DECLARE
        new_value  text;
        value_list text[] := ARRAY ['PURCHASE','SELLING', 'INBOUND', 'OUTBOUND', 'MANUAL_DEBIT', 'MANUAL_CREDIT'];
    BEGIN
        FOREACH new_value IN ARRAY value_list
            LOOP
                IF NOT EXISTS (SELECT 1
                               FROM pg_enum
                               WHERE enumtypid = 'movement_type'::regtype
                                 AND enumlabel = new_value) THEN
                    EXECUTE 'ALTER TYPE movement_type ADD VALUE ' || quote_literal(new_value);
                END IF;
            END LOOP;
    END
$$;

CREATE TABLE portfolio_movements
(
    id                    UUID PRIMARY KEY,
    external_id           BIGINT         NOT NULL,
    operation_id          VARCHAR        NOT NULL,
    portfolio_account_id  UUID           NOT NULL REFERENCES portfolio_accounts (id),
    portfolio_asset_id    UUID           NOT NULL REFERENCES portfolio_assets (id),
    movement_type         movement_type  NOT NULL,
    market_operation_type VARCHAR        NOT NULL,
    description           VARCHAR,
    tags                  VARCHAR,
    state                 VARCHAR        NOT NULL,
    settlement_at         DATE           NOT NULL,
    agreement_at          DATE           NOT NULL,
    operation_type        operation_type NOT NULL,
    quantity              NUMERIC        NOT NULL,
    currency              VARCHAR        NOT NULL,
    net_amount            NUMERIC        NOT NULL,
    gross_amount          NUMERIC        NOT NULL,
    tax_amount            NUMERIC        NOT NULL,
    fee_amount            NUMERIC        NOT NULL,
    metadata              JSONB
);

alter table portfolio_movements
    alter column id set default gen_random_uuid();



CREATE TYPE movement_process_state AS ENUM ('BEYOND_24_HOURS','WITHIN_24_HOURS','SETTLED');
CREATE CAST (character varying AS movement_process_state) WITH INOUT AS ASSIGNMENT;

alter table portfolio_movements
    add column movement_process_state movement_process_state not null default 'WITHIN_24_HOURS';
alter table portfolio_movements
    alter column movement_process_state drop default;

UPDATE portfolio_movements
SET movement_process_state = CASE
                                 WHEN settlement_at <= CURRENT_DATE THEN ('SETTLED'::movement_process_state)
                                 WHEN settlement_at = CURRENT_DATE + 1 THEN ('WITHIN_24_HOURS'::movement_process_state)
                                 ELSE 'BEYOND_24_HOURS'
    END;

alter table portfolio_movements
    add column created_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_movements
    alter column created_at drop default;

alter table portfolio_movements
    add column updated_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_movements
    alter column updated_at drop default;

ALTER TABLE portfolio_movements
    ALTER COLUMN created_at TYPE timestamp;

ALTER TABLE portfolio_movements
    ALTER COLUMN updated_at TYPE timestamp;

CREATE TYPE movement_visibility AS ENUM ('IS_SHOWN', 'NOT_SHOWN');
CREATE CAST (character varying as movement_visibility) WITH INOUT AS ASSIGNMENT;

CREATE TYPE movement_position_effect AS ENUM ('AFFECTS_BALANCE', 'DOES_NOT_AFFECT_BALANCE');
CREATE CAST (character varying as movement_position_effect) WITH INOUT AS ASSIGNMENT;

alter table portfolio_movements
    add column position_effect movement_position_effect not null default 'AFFECTS_BALANCE';

alter table portfolio_movements
    add column visibility movement_visibility not null default 'IS_SHOWN';

alter table portfolio_movements
    alter column visibility drop default;
alter table portfolio_movements
    alter column position_effect drop default;


UPDATE portfolio_movements
SET updated_at      = CURRENT_TIMESTAMP,
    position_effect = 'DOES_NOT_AFFECT_BALANCE',
    visibility      = 'NOT_SHOWN'
WHERE description ILIKE '%Boleto%';


update portfolio_movements
set updated_at      = CURRENT_TIMESTAMP,
    position_effect = 'DOES_NOT_AFFECT_BALANCE'
where movement_type = 'TRANSFER';

update portfolio_movements
set updated_at = CURRENT_TIMESTAMP,
    visibility = 'NOT_SHOWN'
where description ILIKE '%Bloqueo monetario por transferencia%';

update portfolio_movements
set updated_at      = CURRENT_TIMESTAMP,
    visibility      = 'NOT_SHOWN',
    position_effect = 'DOES_NOT_AFFECT_BALANCE'
where description ILIKE '%Bloqueo Monetario por Suscripción%'
   OR description ILIKE '%Desbloqueo Monetario por Liquidación%';


UPDATE portfolio_movements
SET updated_at      = CURRENT_TIMESTAMP,
    position_effect = 'AFFECTS_BALANCE',
    visibility      = 'IS_SHOWN'
WHERE description NOT ILIKE '%Boleto%'
  AND movement_type <> 'TRANSFER'
  AND description NOT ILIKE '%Bloqueo monetario por transferencia%';

CREATE INDEX IF NOT EXISTS portfolio_movements_account_id_idx ON portfolio_movements (portfolio_account_id);
CREATE INDEX IF NOT EXISTS portfolio_movements_asset_id_idx ON portfolio_movements (portfolio_asset_id);
CREATE INDEX IF NOT EXISTS portfolio_movements_settlement_at_idx ON portfolio_movements (settlement_at);
CREATE INDEX IF NOT EXISTS portfolio_movements_agreement_at_idx ON portfolio_movements (agreement_at);
CREATE INDEX IF NOT EXISTS portfolio_movements_operation_id_idx ON portfolio_movements (operation_id);