CREATE TABLE portfolio_position_history_valuations
(
    id                              UUID PRIMARY KEY,
    portfolio_position_history_id   UUID    NOT NULL REFERENCES portfolio_position_history (id),
    portfolio_currency_valuation_id UUID    NOT NULL REFERENCES portfolio_currency_valuations (id),
    agreement_quantity              NUMERIC NOT NULL,
    settlement_quantity             NUMERIC NOT NULL,
    created_at                      DATE    NOT NULL
);

CREATE INDEX IF NOT EXISTS portfolio_position_history_valuations_history_id_idx ON portfolio_position_history_valuations (portfolio_position_history_id);
CREATE INDEX IF NOT EXISTS portfolio_position_history_valuations_currency_id_idx ON portfolio_position_history_valuations (portfolio_currency_valuation_id);

alter table portfolio_position_history_valuations
    add constraint unique_valuation_history unique (portfolio_position_history_id, portfolio_currency_valuation_id);
