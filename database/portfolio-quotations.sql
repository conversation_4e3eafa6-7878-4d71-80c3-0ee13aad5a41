CREATE TYPE quotation_type AS ENUM ('BUY', 'SELL');
CREATE CAST (character varying AS quotation_type) WITH INOUT AS ASSIGNMENT;
create type portfolio_quotation_kind AS ENUM ('MARKET_DATA','ALLARIA_DATA');
CREATE CAST (character varying AS portfolio_quotation_kind) WITH INOUT AS ASSIGNMENT;

CREATE TABLE portfolio_quotations
(
    id                 UUID PRIMARY KEY,
    portfolio_asset_id UUID                     NOT NULL REFERENCES portfolio_assets (id),
    price              NUMERIC                  NOT NULL,
    kind               portfolio_quotation_kind NOT NULL,
    quotation_type     quotation_type           NOT NULL,
    processed_at       TIMESTAMP                NOT NULL,
    created_at         TIMESTAMP                NOT NULL
);

alter table portfolio_quotations
    add column kind portfolio_quotation_kind NOT NULL DEFAULT 'MARKET_DATA';
alter table portfolio_quotations
    alter column kind drop default;


alter table portfolio_quotations
    add constraint uq_portfolio_quotations_unique unique (portfolio_asset_id, kind, quotation_type, processed_at);

CREATE INDEX idx_portfolio_quotation_kind
    ON portfolio_quotations (kind);
CREATE INDEX idx_portfolio_quotation_processed_at
    ON portfolio_quotations (processed_at);
CREATE INDEX idx_portfolio_quotation_portfolio_asset_id
    ON portfolio_quotations (portfolio_asset_id);


alter table portfolio_quotations
    drop column created_by;