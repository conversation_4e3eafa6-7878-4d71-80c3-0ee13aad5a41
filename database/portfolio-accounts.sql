CREATE TYPE CompanyType AS ENUM ('J<PERSON><PERSON><PERSON><PERSON>','PHY<PERSON><PERSON><PERSON>');
CREATE CAST (character varying AS CompanyType) WITH INOUT AS ASSIGNMENT;

CREATE TABLE portfolio_accounts
(
    id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id   UUID         NOT NULL references portfolio_companies (id),
    account_id   int          NOT NULL,
    name         VARCHAR(255) NOT NULL,
    company_type CompanyType  NOT NULL
);
alter table portfolio_accounts
    add column created_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_accounts
    alter column created_at drop default;

alter table portfolio_accounts
    add column updated_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_accounts
    alter column updated_at drop default;


alter table portfolio_accounts
    add constraint portfolio_accounts_company_id_account_id_unique unique (company_id, account_id);

ALTER TABLE portfolio_accounts
    ALTER COLUMN created_at TYPE timestamp;

ALTER TABLE portfolio_accounts
    ALTER COLUMN updated_at TYPE timestamp;

CREATE INDEX portfolio_accounts_account_id_idx ON portfolio_accounts (account_id);
CREATE INDEX portfolio_accounts_company_id_idx ON portfolio_accounts (company_id);
