CREATE TABLE portfolio_position_valuations
(
    id                              UUID PRIMARY KEY,
    portfolio_position_id           UUID      NOT NULL REFERENCES portfolio_positions (id),
    portfolio_currency_valuation_id UUID      NOT NULL REFERENCES portfolio_currency_valuations (id),
    quantity                        NUMERIC   NOT NULL,
    created_at                      TIMESTAMP NOT NULL,
    last_updated_at                 TIMESTAMP NOT NULL
);


alter table portfolio_position_valuations
    rename column quantity to settlement_quantity;

alter table portfolio_position_valuations
    add column agreement_quantity NUMERIC NOT NULL DEFAULT 0;
alter table portfolio_position_valuations
    alter column agreement_quantity drop default;

CREATE INDEX portfolio_position_valuations_position_id_idx ON portfolio_position_valuations (portfolio_position_id);
CREATE INDEX portfolio_position_valuations_currency_valuation_id_idx ON portfolio_position_valuations (portfolio_currency_valuation_id);

alter table portfolio_position_valuations
    add constraint unique_position_valuation unique (portfolio_position_id, portfolio_currency_valuation_id);
