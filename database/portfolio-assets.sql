CREATE TYPE category_type AS ENUM ('BONOS', 'ACCIONES', 'TITULOS', 'FCI'); -- se puede expandir los tipos
CREATE CAST (character varying AS category_type) WITH INOUT AS ASSIGNMENT;

CREATE TABLE portfolio_assets
(
    id                           UUID PRIMARY KEY,
    name                         VA<PERSON>HAR                  NOT NULL,
    abbreviation                 VARCHAR                  NOT NULL,
    category                     VARCHAR                  NOT NULL,
    sub_category                 VARCHAR                  NOT NULL,
    currency                     VARCHAR                  NOT NULL,
    quotation_by                 portfolio_quotation_kind NOT NULL,
    preferred_valuation_currency VARCHAR                  NOT NULL
);


alter table portfolio_assets
    add constraint unique_values unique (name, abbreviation, category, sub_category, currency);

alter table portfolio_assets
    add column quotation_by portfolio_quotation_kind NOT NULL DEFAULT 'MARKET_DATA';
alter table portfolio_assets
    alter column quotation_by drop default;

BEGIN;

ALTER TABLE portfolio_assets
    ADD COLUMN preferred_valuation_currency VARCHAR;

UPDATE portfolio_assets
SET preferred_valuation_currency = currency;

ALTER TABLE portfolio_assets
    ALTER COLUMN preferred_valuation_currency SET NOT NULL;

COMMIT;

alter table portfolio_assets
    add column created_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_assets
    alter column created_at drop default;

alter table portfolio_assets
    add column updated_at DATE NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table portfolio_assets
    alter column updated_at drop default;

ALTER TABLE portfolio_assets
    ALTER COLUMN created_at TYPE timestamp;

ALTER TABLE portfolio_assets
    ALTER COLUMN updated_at TYPE timestamp;

alter table portfolio_assets
    add column sub_category_description VARCHAR;


ALTER TABLE portfolio_assets
    ADD COLUMN allaria_asset_id INT;


CREATE INDEX IF NOT EXISTS portfolio_assets_name_idx ON portfolio_assets (name);